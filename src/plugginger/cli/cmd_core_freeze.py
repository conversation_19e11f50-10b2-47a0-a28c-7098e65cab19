# src/plugginger/cli/cmd_core_freeze.py

"""
Implementation of the 'plugginger freeze' command.

This module provides functionality to generate lockfiles for reproducible builds
by analyzing plugin dependencies and resolving Python package requirements.
"""

import inspect
import json
import logging
import sys
from datetime import datetime
from pathlib import Path

# import tomli  # Optional dependency for TOML support
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase
from plugginger.cli.utils import resolve_app_factory
from plugginger.config.models import (
    PluggingerLockfile,
    PluggingerLockfileMetadata,
    PluginLockInfo,
    PythonPackageLockInfo,
)

# Note: FreezeConflictError would be defined in core.exceptions for full implementation

logger = logging.getLogger(__name__)


def cmd_core_freeze(factory_path: str, output_path: Path, project_root: Path) -> None:
    """
    Execute the core freeze command.

    Args:
        factory_path: Path to app factory function (module:function)
        output_path: Path where lockfile will be written
        project_root: Root directory of the project
    """
    logger.info(f"Generating lockfile for factory: {factory_path}")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Output path: {output_path}")

    try:
        # Load the app builder
        app_builder = resolve_app_factory(factory_path)

        # Generate lockfile content
        lockfile = _generate_lockfile_content(app_builder, project_root)

        # Write lockfile
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(lockfile.model_dump(), f, indent=2, default=str)

        logger.info(f"Lockfile generated successfully: {output_path}")
        print(f"✓ Lockfile written to {output_path}")

    except Exception as e:
        logger.error(f"Failed to generate lockfile: {e}")
        raise


def _generate_lockfile_content(app_builder: PluggingerAppBuilder, project_root: Path) -> PluggingerLockfile:
    """
    Generate the complete lockfile content.

    Args:
        app_builder: The configured app builder
        project_root: Root directory of the project

    Returns:
        Complete lockfile data structure
    """
    logger.info("Analyzing plugin structure...")

    # Get plugin build order and classes
    plugin_items = _get_plugin_build_order_and_classes(app_builder)

    # Collect plugin metadata
    plugins = []
    all_python_deps: dict[str, str] = {}

    for registration_name, plugin_class in plugin_items:
        logger.debug(f"Processing plugin: {registration_name} ({plugin_class.__name__})")

        # Generate instance ID
        instance_id = app_builder._generate_plugin_instance_id(registration_name)

        # Get plugin metadata
        plugin_name = getattr(plugin_class, '_plugginger_plugin_name', registration_name)
        version = getattr(plugin_class, '_plugginger_plugin_version', '0.1.0')

        # Determine source location
        source_location = _get_plugin_source_location(plugin_class, project_root)

        # Create plugin lock info
        plugin_info = PluginLockInfo(
            name=plugin_name,
            version=str(version),
            instance_id=instance_id,
            source_location=source_location
        )
        plugins.append(plugin_info)

        # Discover Python dependencies for this plugin
        plugin_deps = _discover_plugin_python_dependencies(plugin_class, project_root)
        all_python_deps.update(plugin_deps)

    logger.info(f"Found {len(plugins)} plugins with {len(all_python_deps)} unique Python dependencies")

    # Resolve Python package dependencies
    resolved_packages = _resolve_all_python_dependencies(all_python_deps)

    # Create metadata
    metadata = PluggingerLockfileMetadata(
        created_at=datetime.now(),
        python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    )

    # Create lockfile
    lockfile = PluggingerLockfile(
        metadata=metadata,
        app_name=app_builder._app_name,
        plugins=plugins,
        resolved_python_packages=resolved_packages
    )

    return lockfile


def _get_plugin_build_order_and_classes(app_builder: PluggingerAppBuilder) -> list[tuple[str, type[PluginBase]]]:
    """
    Get the build order and classes from the app builder.

    Args:
        app_builder: The configured app builder

    Returns:
        List of (registration_name, plugin_class) tuples in build order
    """
    # Access the builder's internal state
    # Note: This is a simplified implementation - in a real scenario,
    # the builder would need to expose this information through a public API

    plugin_items = []
    for registration_name, plugin_class in app_builder._registered_item_classes.items():
        plugin_items.append((registration_name, plugin_class))

    # For now, return in registration order
    # In a full implementation, this would use the dependency graph's topological sort
    return plugin_items


def _get_plugin_source_location(plugin_class: type[PluginBase], project_root: Path) -> str:
    """
    Determine the source location of a plugin class.

    Args:
        plugin_class: The plugin class
        project_root: Root directory of the project

    Returns:
        Relative path to the plugin source
    """
    try:
        source_file = Path(inspect.getfile(plugin_class))
        relative_path = source_file.relative_to(project_root)
        return str(relative_path)
    except (OSError, ValueError):
        # Fallback to module name if file path cannot be determined
        return f"{plugin_class.__module__}.{plugin_class.__name__}"


def _discover_plugin_python_dependencies(plugin_class: type[PluginBase], project_root: Path) -> dict[str, str]:
    """
    Discover Python dependencies for a plugin.

    Args:
        plugin_class: The plugin class
        project_root: Root directory of the project

    Returns:
        Dictionary of package_name -> version_specifier
    """
    dependencies: dict[str, str] = {}

    try:
        # Get the plugin's source file directory
        source_file = Path(inspect.getfile(plugin_class))
        plugin_dir = source_file.parent

        # Look for pyproject.toml in plugin directory and parent directories
        current_dir = plugin_dir
        while current_dir != project_root.parent:
            pyproject_path = current_dir / "pyproject.toml"
            if pyproject_path.exists():
                deps = _parse_pyproject_dependencies(pyproject_path)
                dependencies.update(deps)
                break

            requirements_path = current_dir / "requirements.txt"
            if requirements_path.exists():
                deps = _parse_requirements_txt(requirements_path)
                dependencies.update(deps)
                break

            current_dir = current_dir.parent

    except (OSError, ValueError) as e:
        logger.warning(f"Could not discover dependencies for {plugin_class.__name__}: {e}")

    return dependencies


def _parse_pyproject_dependencies(pyproject_path: Path) -> dict[str, str]:
    """Parse dependencies from pyproject.toml."""
    dependencies: dict[str, str] = {}

    try:
        # For MVP, skip TOML parsing to avoid external dependencies
        logger.info(f"TOML parsing not implemented for {pyproject_path}")
        # In a full implementation, would use tomli:
        # with open(pyproject_path, 'rb') as f:
        #     data = tomli.load(f)
        # project_deps = data.get('project', {}).get('dependencies', [])
        # for dep in project_deps:
        #     if isinstance(dep, str):
        #         name, version = _parse_dependency_spec(dep)
        #         dependencies[name] = version

    except Exception as e:
        logger.warning(f"Could not parse {pyproject_path}: {e}")

    return dependencies


def _parse_requirements_txt(requirements_path: Path) -> dict[str, str]:
    """Parse dependencies from requirements.txt."""
    dependencies = {}

    try:
        with open(requirements_path, encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    name, version = _parse_dependency_spec(line)
                    dependencies[name] = version

    except Exception as e:
        logger.warning(f"Could not parse {requirements_path}: {e}")

    return dependencies


def _parse_dependency_spec(spec: str) -> tuple[str, str]:
    """
    Parse a dependency specification into name and version.

    Args:
        spec: Dependency specification (e.g., "requests>=2.25.0")

    Returns:
        Tuple of (package_name, version_specifier)
    """
    # Simple parsing - in a real implementation, use packaging.requirements
    spec = spec.strip()

    # Handle common operators
    for op in ['>=', '<=', '==', '!=', '>', '<', '~=']:
        if op in spec:
            name, version = spec.split(op, 1)
            return name.strip(), f"{op}{version.strip()}"

    # No version specified
    return spec, "*"


def _resolve_all_python_dependencies(all_deps: dict[str, str]) -> list[PythonPackageLockInfo]:
    """
    Resolve all Python package dependencies.

    Args:
        all_deps: Dictionary of package_name -> version_specifier

    Returns:
        List of resolved package information
    """
    logger.info(f"Resolving {len(all_deps)} Python dependencies...")

    resolved_packages = []

    # Simplified resolution - in a real implementation, use resolvelib
    for package_name, version_spec in all_deps.items():
        try:
            # For MVP, create a basic lock info without actual resolution
            package_info = PythonPackageLockInfo(
                name=package_name,
                version=version_spec.replace('>=', '').replace('==', '').replace('*', '1.0.0'),
                source="pypi"
            )
            resolved_packages.append(package_info)

        except Exception as e:
            logger.warning(f"Could not resolve {package_name}: {e}")

    logger.info(f"Resolved {len(resolved_packages)} packages")
    return resolved_packages
