# src/plugginger/stubgen/__init__.py

"""
Stub generator for Plugginger plugin proxies.

This module provides functionality to generate .pyi stub files for plugin proxies,
enabling better type checking and IDE support for Plugginger applications.
It uses a Strategy pattern for formatting different Python type annotations
into their string representations suitable for stub files.
"""

from __future__ import annotations

import inspect
import logging
from abc import ABC, abstractmethod
from collections.abc import Callable
from pathlib import Path
from typing import (
    Any,
    Final,
    ForwardRef,
    Literal,
    TypeVar,
    Union,
    cast,
    get_args,
    get_origin,
)

from typing_extensions import Protocol

from plugginger.api.plugin import PluginBase
from plugginger.core.constants import SERVICE_METADATA_KEY

logger = logging.getLogger("plugginger.stubgen")

# Maximum recursion depth for type formatting to prevent infinite loops
MAX_RECURSION_DEPTH: Final[int] = 10

# Header to be prepended to every generated stub file
_STUB_FILE_HEADER: Final[str] = (
    "# PMAINS_MODE: BEGIN\n"
    "# This file is auto-generated by `plugginger stubs generate`.\n"
    "# Do not edit this file manually, as your changes will be overwritten.\n"
    "# Typing stubs for Plugginger plugin proxies.\n"
    "from typing import Any, Awaitable, Optional, Union, List as list, Dict as dict, Tuple as tuple, Set as set, Type as type, Generic, TypeVar, Callable\n"
    "from typing_extensions import ParamSpec, Literal, Protocol\n"
    "\n"
    "# Placeholder for ParamSpec and TypeVar if needed by generated signatures\n"
    "# P_Stub = ParamSpec(\"P_Stub\")\n"
    "# R_Stub = TypeVar(\"R_Stub\")\n\n"
)
_STUB_FILE_FOOTER: Final[str] = (
    "\n# PMAINS_MODE: END\n"
)


class TypeFormatterProtocol(Protocol):
    """Protocol defining the interface for type annotation formatters."""

    def can_format(self, annotation: Any) -> bool:
        """Checks if this formatter can handle the given annotation."""
        ... # pragma: no cover

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats the annotation to its string representation for a stub file."""
        ... # pragma: no cover


class BaseTypeFormatter(ABC):
    """
    Abstract base class for type annotation formatters.

    Each formatter is responsible for a specific category of types.
    It holds a reference to the main `TypeHintStringifier` to allow for
    recursive formatting of nested types (e.g., arguments of generic types).
    """
    main_formatter: TypeHintStringifier

    def __init__(self, main_formatter: TypeHintStringifier) -> None:
        """
        Initializes the formatter with a reference to the main stringifier.

        Args:
            main_formatter: The main `TypeHintStringifier` instance for recursive calls.
        """
        self.main_formatter = main_formatter

    @abstractmethod
    def can_format(self, annotation: Any) -> bool:
        """
        Determines if this formatter can handle the given type annotation.

        Returns:
            True if this formatter is responsible for the annotation, False otherwise.
        """
        pass # pragma: no cover

    @abstractmethod
    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """
        Converts the given type annotation into its string representation.

        Args:
            annotation: The type annotation to format.
            recursion_depth: The current depth of recursion, used to prevent infinite loops.

        Returns:
            The string representation of the type annotation.
        """
        pass # pragma: no cover


class BuiltinTypeFormatter(BaseTypeFormatter):
    """Formatter for built-in Python types (int, str, list, dict, etc.)."""

    _builtin_types = (int, str, float, bool, bytes, list, dict, tuple, set, type(None))

    def can_format(self, annotation: Any) -> bool:
        """Checks if the annotation is one of the recognized built-in types."""
        return annotation in self._builtin_types

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats built-in types to their standard names (e.g., "int", "None", "list")."""
        if annotation is type(None):
            return "None"
        name = getattr(annotation, "__name__", None)
        if isinstance(name, str):
            return name
        logger.warning(f"BuiltinTypeFormatter received unexpected annotation: {annotation!r}") # pragma: no cover
        return "Any" # pragma: no cover


class TypeVarFormatter(BaseTypeFormatter):
    """Formatter for `typing.TypeVar` instances."""

    def can_format(self, annotation: Any) -> bool:
        """Checks if the annotation is a `TypeVar`."""
        return isinstance(annotation, TypeVar)

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats a `TypeVar` to its declared name."""
        actual_annotation = cast(TypeVar, annotation) # MyPy now knows it's a TypeVar
        return actual_annotation.__name__


class ForwardRefFormatter(BaseTypeFormatter):
    """Formatter for `typing.ForwardRef` instances (string forward references)."""

    def can_format(self, annotation: Any) -> bool:
        """Checks if the annotation is a `ForwardRef`."""
        return isinstance(annotation, ForwardRef)

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats a `ForwardRef` to its string argument (the forward reference itself)."""
        actual_annotation = cast(ForwardRef, annotation)
        return actual_annotation.__forward_arg__


class TypingModuleFormatter(BaseTypeFormatter):
    """
    Formatter for common constructs from the `typing` module and `collections.abc`
    (e.g., List, Dict, Union, Optional, Callable, Literal, Any, Awaitable).
    """

    def can_format(self, annotation: Any) -> bool:
        """
        Checks if the annotation is a special form from `typing` or `collections.abc`
        (like generics, Any, Union, etc.) or has a recognized generic origin.
        """
        if annotation is Any:
            return True

        origin = get_origin(annotation)
        if origin is not None:
            return True

        module_name = getattr(annotation, "__module__", "")
        return module_name in ("typing", "collections.abc", "typing_extensions")

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats constructs from the `typing` module."""
        if recursion_depth >= MAX_RECURSION_DEPTH:
            logger.warning(f"Max recursion depth ({MAX_RECURSION_DEPTH}) reached for type: {annotation!r}")
            return "..."

        if annotation is Any:
            return "Any"

        origin = get_origin(annotation)
        args = get_args(annotation)

        if origin is not None:
            return self._format_generic_with_origin(origin, args, recursion_depth)

        name = getattr(annotation, "__name__", None)
        if isinstance(name, str):
            if name in ("Awaitable", "Coroutine"):
                return f"{name}[Any]" if not args else self._format_generic_with_origin(annotation, args, recursion_depth)
            module_name = getattr(annotation, "__module__", "")
            if module_name in ("typing", "typing_extensions"):
                return name

        type_repr = str(annotation)
        if type_repr.startswith("typing."):
            return type_repr[len("typing."):]
        if type_repr.startswith("typing_extensions."): # pragma: no cover
            return type_repr[len("typing_extensions."):]

        logger.warning(f"TypingModuleFormatter fallback for: {annotation!r} (repr: {type_repr})")
        return type_repr


    def _format_generic_with_origin(self, origin: Any, args: tuple[Any, ...], recursion_depth: int) -> str:
        """Helper to format generic types like List[T], Union[X,Y], Callable[[A],B], etc."""
        origin_name_map = {
            list: "List", dict: "Dict", tuple: "Tuple", set: "Set",
            Union: "Union",
        }

        origin_display_name: str
        # Handle types.UnionType for Python 3.10+ `X | Y` syntax
        if hasattr(origin, "__name__") and origin.__name__ == "UnionType": # For `int | str` in Py 3.10+
            origin_display_name = "Union" # Represent as typing.Union for stubs
        elif hasattr(origin, "_name") and origin._name is not None: # For typing.List, typing.Dict etc.
            origin_display_name = str(origin._name)
        else: # Fallback for other origins like `collections.abc.Callable`
            origin_display_name = getattr(origin, "__name__", str(origin))


        # Special handling for Optional[X] which is Union[X, NoneType]
        if (origin_display_name == "Union") and len(args) == 2 and type(None) in args:
            non_none_type_arg = args[0] if args[1] is type(None) else args[1]
            return f"Optional[{self.main_formatter.format(non_none_type_arg, recursion_depth + 1)}]"

        if origin_display_name == "Literal" or origin is Literal:
            if not args: return "Literal"
            formatted_args = [repr(arg) for arg in args]
            return f"Literal[{', '.join(formatted_args)}]"

        if not args:
            return origin_name_map.get(origin, origin_display_name)

        formatted_args_list = [self.main_formatter.format(arg, recursion_depth + 1) for arg in args]

        if "Callable" in origin_display_name:
            if len(formatted_args_list) >= 1:
                param_args_str = "..."
                # args[0] for Callable is a list of param types or Ellipsis itself
                if len(args) > 1 and isinstance(args[0], list):
                    param_types_list = [self.main_formatter.format(p, recursion_depth + 1) for p in args[0]]
                    param_args_str = f"[{', '.join(param_types_list)}]"
                elif len(args) > 1 and args[0] is Ellipsis:
                     param_args_str = "..."

                return_type_str = formatted_args_list[-1]
                return f"Callable[[{param_args_str}], {return_type_str}]"
            return "Callable[..., Any]"

        if origin_display_name == "Tuple" or origin is tuple:
            if len(args) == 2 and args[1] is Ellipsis:
                return f"Tuple[{formatted_args_list[0]}, ...]"

        # Default formatting for other generics like List[int], Dict[str, bool]
        return f"{origin_name_map.get(origin, origin_display_name)}[{', '.join(formatted_args_list)}]"


class UserDefinedTypeFormatter(BaseTypeFormatter):
    """Formatter for user-defined classes and types not covered by other formatters."""

    def can_format(self, annotation: Any) -> bool:
        """Checks if the annotation is a user-defined class/type."""
        is_type = inspect.isclass(annotation)
        if not is_type:
            return False

        module_name = getattr(annotation, "__module__", "")
        if module_name in ("builtins", "typing", "collections.abc", "typing_extensions"):
            if get_origin(annotation) is not None: return False
            if annotation in (int, str, float, bool, bytes, list, dict, tuple, set, type(None)): return False
            if annotation is Any: return False
            # If from typing but not generic/builtin, it might be a special form like NoReturn
            # Let TypingModuleFormatter try it by name first. This one is for my_module.MyClass.
            # This logic can be tricky. The order of formatters is key.
            return True # A class from typing/collections.abc not caught by TypingModuleFormatter's origin check

        return True # Likely a user-defined type from another module

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Formats user-defined types using their `__module__` and `__qualname__`."""
        actual_annotation = cast(type[Any], annotation)
        module_name = getattr(actual_annotation, "__module__", None)
        # Use __qualname__ for nested classes, fallback to __name__
        qual_name = getattr(actual_annotation, "__qualname__", getattr(actual_annotation, "__name__", None))

        if not isinstance(qual_name, str):
            logger.warning(f"UserDefinedTypeFormatter: Could not get qualname for {annotation!r}") # pragma: no cover
            return "Any" # pragma: no cover

        if module_name and module_name != "builtins" and module_name != "__main__":
            return f"{module_name}.{qual_name}"
        return qual_name


class DefaultFormatter(BaseTypeFormatter):
    """Fallback formatter for any types not handled by more specific formatters."""

    def can_format(self, annotation: Any) -> bool:
        """Always returns `True` as this is the ultimate fallback."""
        return True # pragma: no cover

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """
        Provides a fallback string representation, typically "Any" or `str(annotation)`.
        Logs a warning as this indicates a type that might need a dedicated formatter.
        """
        logger.warning(
            f"DefaultFormatter: Using fallback string representation for type: {annotation!r}. "
            "Consider adding a specific formatter if this type appears often."
        )
        try:
            s = str(annotation)
            if len(s) > 70 or s.startswith("<") or (" at 0x" in s): # Heuristics for unhelpful str()
                return "Any"
            # Clean up common typing prefixes if s.startswith("typing."): s = s[len("typing."):]
            if s.startswith("typing_extensions."): s = s[len("typing_extensions."):]
            return s
        except Exception: # pylint: disable=broad-except # pragma: no cover
            return "Any"


class TypeHintStringifier:
    """
    Main orchestrator for converting Python type annotations into strings for `.pyi` stubs.
    It uses a list of specialized formatters (Strategy Pattern) to handle different
    types of annotations.
    """
    _formatters: list[BaseTypeFormatter]

    def __init__(self) -> None:
        """Initializes the stringifier with an ordered list of type formatters."""
        self._formatters = [
            BuiltinTypeFormatter(self),
            TypeVarFormatter(self),
            ForwardRefFormatter(self),
            TypingModuleFormatter(self),
            UserDefinedTypeFormatter(self),
            DefaultFormatter(self),
        ]

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """
        Formats a given type annotation into its string representation.
        (Full docstring in Tranche 6 Prompt)
        """
        if annotation is inspect.Parameter.empty or annotation is inspect.Signature.empty:
            return "Any"

        if recursion_depth > MAX_RECURSION_DEPTH:
            logger.warning(f"Max recursion depth ({MAX_RECURSION_DEPTH}) reached for type: {annotation!r}. Returning '...'")
            return "..."

        for formatter_instance in self._formatters:
            if formatter_instance.can_format(annotation):
                try:
                    formatted_string = formatter_instance.format(annotation, recursion_depth)
                    return str(formatted_string) # Ensure it's a string
                except Exception as e: # pylint: disable=broad-except
                    logger.warning(
                        f"Formatter '{formatter_instance.__class__.__name__}' failed for "
                        f"annotation '{annotation!r}': {e!r}. Trying next formatter."
                    )

        logger.error(f"Internal Error: No formatter (including DefaultFormatter) handled type: {annotation!r}.") # pragma: no cover
        return "Any"


_type_stringifier_instance: Final[TypeHintStringifier] = TypeHintStringifier()

def _get_formatted_type_hint_for_stub(annotation: Any) -> str:
    """Helper to get string representation of a type annotation using the global stringifier."""
    return _type_stringifier_instance.format(annotation)


def _format_parameter_for_stub(param: inspect.Parameter) -> str:
    """Formats a single `inspect.Parameter` into its string representation for a stub file."""
    name = param.name

    annotation_str = ": Any"
    if param.annotation is not inspect.Parameter.empty:
        annotation_str = f": {_get_formatted_type_hint_for_stub(param.annotation)}"

    default_str = ""
    if param.default is not inspect.Parameter.empty:
        if isinstance(param.default, str): default_str = f" = {param.default!r}"
        elif isinstance(param.default, int | float | bool | type(None)): default_str = f" = {param.default!r}"
        else: default_str = " = ..."

    param_kind_str = ""
    if param.kind == inspect.Parameter.VAR_POSITIONAL: param_kind_str = "*"
    elif param.kind == inspect.Parameter.VAR_KEYWORD: param_kind_str = "**"

    return f"{param_kind_str}{name}{annotation_str}{default_str}"


def _render_method_stub_for_proxy(method_name: str, original_method: Callable[..., Any]) -> str:
    """Generates the `.pyi` stub string for a single service method."""
    try:
        signature = inspect.signature(original_method)
    except (ValueError, TypeError):
        logger.warning(f"Could not get signature for service method '{method_name}'. Using generic stub.")
        return f"    async def {method_name}(self, *args: Any, **kwargs: Any) -> Awaitable[Any]: ...\n"

    params_for_stub: list[str] = []
    # Parameter handling needs to respect positional-only and keyword-only markers if present
    # For simplicity, we'll list them in order. MyPy can often infer from context.
    # A full stub generator would handle '*' and '/' markers.
    for param_name, param_obj in signature.parameters.items():
        if param_name == "self" or param_name == "cls":
            continue
        params_for_stub.append(_format_parameter_for_stub(param_obj))

    return_annotation_str = "Awaitable[Any]"
    if signature.return_annotation is not inspect.Parameter.empty:
        return_annotation_str = _get_formatted_type_hint_for_stub(signature.return_annotation)

    full_params_str = "self"
    if params_for_stub:
        full_params_str += f", {', '.join(params_for_stub)}"

    method_stub_lines: list[str] = []
    method_stub_lines.append(f"    async def {method_name}({full_params_str}) -> {return_annotation_str}:")
    method_stub_lines.append(f"        \"\"\"Proxy for service method '{method_name}'.\"\"\"\n")
    method_stub_lines.append("        ...")
    return "\n".join(method_stub_lines) + "\n"


def _collect_and_render_service_method_stubs(plugin_class: type[PluginBase]) -> tuple[list[str], set[str]]:
    """Collects service methods and renders their stubs, also collecting necessary imports."""
    method_stubs: list[str] = []
    required_imports: set[str] = set()

    def add_imports_from_type_str_recursive(annotation_node: Any) -> None:
        """Recursively traverses type annotations to find necessary imports."""
        if annotation_node is Any or annotation_node is inspect.Parameter.empty or annotation_node is type(None):
            return

        origin = get_origin(annotation_node)
        args = get_args(annotation_node)

        if origin: # It's a generic type
            module_name = getattr(origin, "__module__", "")
            origin_name = getattr(origin, "_name", getattr(origin, "__name__", None)) # _name for List, __name__ for UnionType
            if module_name == "typing" and origin_name:
                required_imports.add(f"from typing import {origin_name}")
            elif module_name == "typing_extensions" and origin_name: # pragma: no cover
                required_imports.add(f"from typing_extensions import {origin_name}")

            for arg in args:
                add_imports_from_type_str_recursive(arg)
        elif inspect.isclass(annotation_node): # User-defined class or non-generic from typing
            module_name = getattr(annotation_node, "__module__", "")
            class_name = getattr(annotation_node, "__qualname__", getattr(annotation_node, "__name__", None))
            if class_name and module_name and module_name != "builtins" and module_name != "__main__":
                required_imports.add(f"from {module_name} import {class_name}")
        elif isinstance(annotation_node, TypeVar):
            required_imports.add("from typing import TypeVar")
        elif isinstance(annotation_node, ForwardRef):
             # Forward refs are strings, no import needed for the ref itself, but the actual type might.
             # This is complex; for now, assume direct imports cover it.
             pass


    for attr_name in dir(plugin_class):
        if attr_name.startswith("_"): continue

        member = getattr(plugin_class, attr_name)
        service_meta = getattr(member, SERVICE_METADATA_KEY, None)

        if service_meta and service_meta.get("is_service") and callable(member):
            method_stubs.append(_render_method_stub_for_proxy(attr_name, member))
            try:
                signature = inspect.signature(member)
                add_imports_from_type_str_recursive(signature.return_annotation)
                for param in signature.parameters.values():
                    if param.name not in ('self', 'cls'):
                        add_imports_from_type_str_recursive(param.annotation)
            except (ValueError, TypeError): # pragma: no cover
                pass

    return method_stubs, required_imports


def _render_proxy_class_stub(plugin_registration_name: str, plugin_class: type[PluginBase]) -> str:
    """Renders the full `.pyi` stub file content for a single plugin's proxy."""
    proxy_class_name = "".join(word.capitalize() for word in plugin_registration_name.split("_")) + "Proxy"

    method_stubs, required_imports = _collect_and_render_service_method_stubs(plugin_class)

    standard_imports = {
        "from typing import Any, Awaitable, Optional, Union, List, Dict, Tuple, Set, Type, Generic, TypeVar, Callable",
        "from typing_extensions import ParamSpec, Literal, Protocol",
    }
    all_imports = sorted(standard_imports.union(required_imports))

    stub_content_lines: list[str] = []
    stub_content_lines.append(_STUB_FILE_HEADER.strip())
    stub_content_lines.extend(all_imports)
    stub_content_lines.append("\n")
    stub_content_lines.append(f"class {proxy_class_name}:\n")
    stub_content_lines.append(f"    \"\"\"Auto-generated proxy stub for plugin '{plugin_registration_name}'.\"\"\"\n")

    if not method_stubs:
        stub_content_lines.append("    pass  # No services found for this plugin.\n")
    else:
        for stub_line in method_stubs:
            stub_content_lines.append(stub_line)

    stub_content_lines.append(_STUB_FILE_FOOTER.strip())
    return "\n".join(stub_content_lines)


def generate_stubs_for_plugins(
    plugin_classes_map: dict[str, type[PluginBase]],
    output_directory: Path,
) -> int:
    print("generate_stubs_for_plugins called")
    print("generate_stubs_for_plugins called")
    """
    Generates `.pyi` proxy stub files for a collection of plugin classes.
    (Full docstring in Tranche 6 Prompt)
    """
    print("generate_stubs_for_plugins called")
    output_directory.mkdir(parents=True, exist_ok=True)
    (output_directory / "py.typed").touch(exist_ok=True)

    count_written = 0
    for registration_name, plugin_cls in plugin_classes_map.items():
        if not (inspect.isclass(plugin_cls) and issubclass(plugin_cls, PluginBase)): # pragma: no cover
            logger.warning(f"Skipping item for stub generation: '{registration_name}' "
                           f"(class: {plugin_cls.__name__}) is not a valid PluginBase subclass.")
            continue

        stub_file_name = f"{registration_name}_proxy.pyi"
        stub_file_path = output_directory / stub_file_name

        logger.debug(f"Rendering stub for plugin registered as '{registration_name}' (class: {plugin_cls.__name__}).")
        try:
            stub_code = _render_proxy_class_stub(registration_name, plugin_cls)
        except Exception as e: # pylint: disable=broad-except # pragma: no cover
            logger.error(f"Failed to render stub code for plugin '{registration_name}': {e!r}", exc_info=True)
            continue

        needs_write = True
        if stub_file_path.exists():
            try:
                if stub_file_path.read_text(encoding="utf-8") == stub_code:
                    needs_write = False
            except OSError as e: # pragma: no cover
                logger.warning(f"Could not read existing stub file '{stub_file_path}': {e}. Will overwrite.")

        if needs_write:
            try:
                stub_file_path.write_text(stub_code, encoding="utf-8")
                log_path_str: str
                try:
                    log_path_str = str(stub_file_path.relative_to(Path.cwd()))
                except ValueError: # pragma: no cover
                    log_path_str = str(stub_file_path)
                logger.info(f"Generated/Updated stub file: {log_path_str}")
                count_written += 1
            except OSError as e: # pragma: no cover
                logger.error(f"Failed to write stub file '{stub_file_path}': {e!r}", exc_info=True)
        else:
            logger.debug(f"Stub file for '{registration_name}' is already up-to-date.")

    return count_written
