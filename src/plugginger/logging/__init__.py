# src/plugginger/logging/__init__.py

"""
Structured logging module for AI-Agent analysis and debugging.

This module provides structured logging capabilities specifically designed
for AI-Agent compatibility, enabling machine-readable logs with rich context
and performance metrics.
"""

from __future__ import annotations

from .analyzer import LogAnalyzer
from .structured_logger import (
    EventType,
    LogLevel,
    OperationTimer,
    StructuredLogger,
)

__all__ = [
    "EventType",
    "LogAnalyzer",
    "LogLevel",
    "OperationTimer",
    "StructuredLogger",
]
