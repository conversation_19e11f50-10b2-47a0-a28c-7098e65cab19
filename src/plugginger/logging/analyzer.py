# src/plugginger/logging/analyzer.py

"""
Log analysis tools for AI-Agent consumption and debugging.

This module provides tools for analyzing structured logs, detecting performance
issues, and generating insights for AI-Agent decision making.
"""

from __future__ import annotations

import json
from typing import Any


class LogAnalyzer:
    """AI-Agent helper for log analysis and issue detection."""

    @staticmethod
    def parse_structured_logs(log_file: str) -> list[dict[str, Any]]:
        """
        Parse structured logs for AI analysis.

        Args:
            log_file: Path to log file containing JSON log entries

        Returns:
            List of parsed log entries as dictionaries
        """
        logs = []
        with open(log_file, encoding='utf-8') as f:
            for line in f:
                try:
                    log_entry = json.loads(line.strip())
                    logs.append(log_entry)
                except json.JSONDecodeError:
                    # Skip non-JSON lines
                    continue
        return logs

    @staticmethod
    def analyze_build_performance(logs: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Analyze build performance from logs.

        Args:
            logs: List of parsed log entries

        Returns:
            Performance analysis results
        """
        build_events = [log for log in logs if log.get('event_type', '').startswith('build_')]
        performance_events = [log for log in logs if log.get('event_type') == 'performance_metric']

        return {
            "total_build_time_ms": sum(log.get('duration_ms', 0) for log in build_events),
            "plugin_loading_time_ms": sum(
                log.get('duration_ms', 0) for log in performance_events
                if 'plugin_inclusion' in log.get('context', {}).get('operation', '')
            ),
            "slowest_operations": sorted(
                performance_events,
                key=lambda x: x.get('duration_ms', 0),
                reverse=True
            )[:5],
            "error_count": len([log for log in logs if log.get('level') == 'error']),
            "plugin_count": len({
                log.get('context', {}).get('plugin_name')
                for log in logs
                if log.get('context', {}).get('plugin_name')
            })
        }

    @staticmethod
    def detect_issues(logs: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        Detect potential issues from logs.

        Args:
            logs: List of parsed log entries

        Returns:
            List of detected issues with suggestions
        """
        issues = []

        # Detect slow operations
        for log in logs:
            if (log.get('event_type') == 'performance_metric' and
                log.get('duration_ms', 0) > 1000):  # > 1 second
                issues.append({
                    "type": "slow_operation",
                    "operation": log.get('context', {}).get('operation'),
                    "duration_ms": log.get('duration_ms'),
                    "suggestion": "Consider optimizing this operation"
                })

        # Detect error patterns
        error_logs = [log for log in logs if log.get('level') == 'error']
        if len(error_logs) > 5:
            issues.append({
                "type": "high_error_rate",
                "error_count": len(error_logs),
                "suggestion": "Multiple errors detected, check configuration"
            })

        return issues

    @staticmethod
    def generate_performance_report(logs: list[dict[str, Any]]) -> dict[str, Any]:
        """
        Generate comprehensive performance report.

        Args:
            logs: List of parsed log entries

        Returns:
            Comprehensive performance report
        """
        analysis = LogAnalyzer.analyze_build_performance(logs)
        issues = LogAnalyzer.detect_issues(logs)

        # Calculate additional metrics
        performance_events = [log for log in logs if log.get('event_type') == 'performance_metric']

        avg_operation_time = 0
        if performance_events:
            avg_operation_time = sum(log.get('duration_ms', 0) for log in performance_events) / len(performance_events)

        return {
            "summary": {
                "total_events": len(logs),
                "performance_events": len(performance_events),
                "error_events": analysis["error_count"],
                "average_operation_time_ms": avg_operation_time
            },
            "performance": analysis,
            "issues": issues,
            "recommendations": LogAnalyzer._generate_recommendations(analysis, issues)
        }

    @staticmethod
    def _generate_recommendations(
        analysis: dict[str, Any],
        issues: list[dict[str, Any]]
    ) -> list[str]:
        """
        Generate optimization recommendations based on analysis.

        Args:
            analysis: Performance analysis results
            issues: Detected issues

        Returns:
            List of optimization recommendations
        """
        recommendations = []

        # Check build time
        total_build_time = analysis.get("total_build_time_ms", 0)
        if total_build_time > 5000:  # > 5 seconds
            recommendations.append("Build time is high, consider optimizing plugin loading")

        # Check plugin loading time
        plugin_loading_time = analysis.get("plugin_loading_time_ms", 0)
        if plugin_loading_time > total_build_time * 0.5:
            recommendations.append("Plugin loading takes significant time, review plugin dependencies")

        # Check for slow operations
        slow_operations = [issue for issue in issues if issue.get("type") == "slow_operation"]
        if slow_operations:
            recommendations.append(f"Found {len(slow_operations)} slow operations, consider optimization")

        # Check error rate
        if analysis.get("error_count", 0) > 0:
            recommendations.append("Errors detected, review configuration and dependencies")

        return recommendations
