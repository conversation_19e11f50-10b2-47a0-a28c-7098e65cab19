"""
Manifest loading and validation during plugin loading.

This module provides functionality to load and validate YAML manifests
during the plugin loading process, ensuring schema compliance and
providing clear error messages for validation failures.
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Any

from plugginger.core.exceptions import PluginRegistrationError
from plugginger.schemas import PluginManifest, manifest_from_yaml


class ManifestLoader:
    """
    Loads and validates YAML manifests during plugin loading.

    This class provides functionality to discover, load, and validate
    plugin manifests from YAML files, ensuring they comply with the
    schema and providing clear error messages for validation failures.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the manifest loader.

        Args:
            logger: Optional logger for debugging and error reporting
        """
        self._logger = logger or logging.getLogger(__name__)

    def load_plugin_manifest(
        self,
        plugin_class: type[Any],
        manifest_path: str | Path | None = None,
        require_manifest: bool = False
    ) -> PluginManifest | None:
        """
        Load and validate a plugin manifest from YAML file.

        This method attempts to load a manifest for the given plugin class,
        either from a specified path or by discovering it automatically.

        Args:
            plugin_class: Plugin class to load manifest for
            manifest_path: Optional explicit path to manifest file
            require_manifest: If True, raises error if manifest not found

        Returns:
            Loaded and validated manifest, or None if not found and not required

        Raises:
            PluginRegistrationError: If manifest is invalid or required but not found
        """
        # Get plugin name for error messages
        plugin_name = getattr(plugin_class, '__name__', str(plugin_class))

        # Determine manifest file path
        if manifest_path:
            manifest_file: Path | None = Path(manifest_path)
        else:
            manifest_file = self._discover_manifest_file(plugin_class)

        # Check if manifest file exists
        if not manifest_file or not manifest_file.exists():
            if require_manifest:
                raise PluginRegistrationError(
                    f"Required manifest file not found for plugin '{plugin_name}'. "
                    f"Expected: {manifest_file or 'manifest.yaml in plugin directory'}"
                )
            self._logger.debug(f"No manifest file found for plugin '{plugin_name}' (optional)")
            return None

        # Load and validate manifest
        try:
            self._logger.debug(f"Loading manifest for plugin '{plugin_name}' from {manifest_file}")

            with open(manifest_file, encoding='utf-8') as f:
                yaml_content = f.read()

            # Parse and validate YAML against schema
            manifest = manifest_from_yaml(yaml_content, PluginManifest)

            # Type assertion for mypy - manifest_from_yaml with PluginManifest returns PluginManifest
            assert isinstance(manifest, PluginManifest)

            self._logger.info(f"Successfully loaded manifest for plugin '{plugin_name}'")
            return manifest

        except FileNotFoundError:
            if require_manifest:
                raise PluginRegistrationError(
                    f"Manifest file not found for plugin '{plugin_name}': {manifest_file}"
                ) from None
            return None

        except Exception as e:
            # Provide clear error message with file path and validation details
            error_msg = (
                f"Invalid manifest file for plugin '{plugin_name}' at {manifest_file}: {e}"
            )
            self._logger.error(error_msg)
            raise PluginRegistrationError(error_msg) from e

    def _discover_manifest_file(self, plugin_class: type[Any]) -> Path | None:
        """
        Discover manifest file for a plugin class.

        This method attempts to find a manifest file using common conventions:
        1. manifest.yaml in the same directory as the plugin module
        2. {plugin_name}_manifest.yaml in the same directory
        3. manifest.yaml in parent directory

        Args:
            plugin_class: Plugin class to discover manifest for

        Returns:
            Path to manifest file if found, None otherwise
        """
        # Get plugin module file path
        try:
            module = plugin_class.__module__
            if not module or module == '__main__':
                return None

            import sys
            module_obj = sys.modules.get(module)
            if not module_obj or not hasattr(module_obj, '__file__'):
                return None

            module_file = module_obj.__file__
            if not module_file:
                return None

            module_dir = Path(module_file).parent

        except (AttributeError, KeyError):
            return None

        # Try common manifest file names
        plugin_name = getattr(plugin_class, '__name__', 'unknown').lower()

        candidates = [
            module_dir / "manifest.yaml",
            module_dir / f"{plugin_name}_manifest.yaml",
            module_dir / f"{plugin_name}.manifest.yaml",
            module_dir.parent / "manifest.yaml",  # Parent directory
        ]

        for candidate in candidates:
            if candidate.exists() and candidate.is_file():
                self._logger.debug(f"Discovered manifest file: {candidate}")
                return candidate

        return None

    def validate_manifest_schema(self, yaml_content: str, file_path: str | Path) -> PluginManifest:
        """
        Validate YAML content against the plugin manifest schema.

        Args:
            yaml_content: YAML content to validate
            file_path: Path to the file (for error messages)

        Returns:
            Validated manifest object

        Raises:
            PluginRegistrationError: If validation fails
        """
        try:
            manifest = manifest_from_yaml(yaml_content, PluginManifest)
            # Type assertion for mypy - manifest_from_yaml with PluginManifest returns PluginManifest
            assert isinstance(manifest, PluginManifest)
            return manifest
        except Exception as e:
            raise PluginRegistrationError(
                f"Manifest validation failed for {file_path}: {e}"
            ) from e


def load_plugin_manifest(
    plugin_class: type[Any],
    manifest_path: str | Path | None = None,
    require_manifest: bool = False,
    logger: logging.Logger | None = None
) -> PluginManifest | None:
    """
    Convenience function to load a plugin manifest.

    Args:
        plugin_class: Plugin class to load manifest for
        manifest_path: Optional explicit path to manifest file
        require_manifest: If True, raises error if manifest not found
        logger: Optional logger for debugging

    Returns:
        Loaded and validated manifest, or None if not found and not required

    Raises:
        PluginRegistrationError: If manifest is invalid or required but not found
    """
    loader = ManifestLoader(logger)
    return loader.load_plugin_manifest(plugin_class, manifest_path, require_manifest)
