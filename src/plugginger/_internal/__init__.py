# src/plugginger/_internal/__init__.py

"""
Internal implementation modules for the Plugginger framework.

This package contains internal implementation details that are not part of the
public API. These modules provide the core runtime functionality and should
not be imported directly by user code.

Modules:
- runtime: Runtime components like fault handling and executor management
"""

__all__ = [
    "runtime",
]
