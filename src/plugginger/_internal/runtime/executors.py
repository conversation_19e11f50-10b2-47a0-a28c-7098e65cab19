# src/plugginger/_internal/runtime/executors.py

"""
Executor registry for managing ThreadPoolExecutor instances.

This module provides the ExecutorRegistry class that manages named
ThreadPoolExecutor instances for background task execution within
the Plugginger framework.
"""

from __future__ import annotations

import os
from concurrent.futures import Executor, ThreadPoolExecutor

from plugginger.config.models import ExecutorConfig
from plugginger.core.constants import DEFAULT_EXECUTOR_NAME
from plugginger.core.types import LoggerCallable


class ExecutorRegistry:
    """
    Registry for managing named ThreadPoolExecutor instances.

    This class provides centralized management of thread pool executors used
    for background task execution. It supports both automatic configuration
    from ExecutorConfig objects and registration of pre-configured Executor
    instances.

    Attributes:
        _logger: Logger function for debugging and error reporting
        _executors: Dictionary mapping executor names to concurrent.futures.Executor instances
    """

    def __init__(self, default_executor_config: ExecutorConfig, logger: LoggerCallable) -> None:
        """
        Initialize the executor registry with a default executor.

        Args:
            default_executor_config: Configuration for the default executor
            logger: Logger function for debugging and error reporting
        """
        self._logger = logger
        self._executors: dict[str, Executor] = {}

        # Register the default executor immediately
        self.register_executor(DEFAULT_EXECUTOR_NAME, default_executor_config)

    def register_executor(self, name: str, config_or_executor: ExecutorConfig | Executor) -> None:
        """
        Register a new executor with the given name.

        This method accepts either an ExecutorConfig object (which will be used to
        create a new ThreadPoolExecutor) or a pre-configured Executor instance.

        Args:
            name: Unique name for the executor
            config_or_executor: Either ExecutorConfig for automatic creation or Executor instance

        Raises:
            ValueError: If name already exists or if ExecutorConfig has invalid max_workers
            TypeError: If config_or_executor is neither ExecutorConfig nor Executor
        """
        # Check for name conflicts
        if name in self._executors:
            error_msg = f"Executor with name '{name}' already exists"
            self._logger(f"[Plugginger] ERROR: {error_msg}")
            raise ValueError(error_msg)

        if isinstance(config_or_executor, Executor):
            # Register pre-configured executor directly
            self._executors[name] = config_or_executor
            self._logger(f"[Plugginger] Registered custom executor '{name}'")

        elif isinstance(config_or_executor, ExecutorConfig):
            # Create ThreadPoolExecutor from configuration
            config = config_or_executor

            # Resolve max_workers with sensible defaults
            if config.max_workers is None:
                # Use Python 3.8+ ThreadPoolExecutor default logic
                resolved_max_workers = min(32, (os.cpu_count() or 1) + 4)
            else:
                resolved_max_workers = config.max_workers

            # Validate max_workers
            if resolved_max_workers <= 0:
                raise ValueError(f"max_workers must be positive, got {resolved_max_workers}")

            # Create ThreadPoolExecutor with appropriate parameters
            # Use configured thread_name_prefix or generate a default based on executor name
            thread_prefix = config.thread_name_prefix or f"{name}-worker"
            executor = ThreadPoolExecutor(
                max_workers=resolved_max_workers, thread_name_prefix=thread_prefix
            )
            self._executors[name] = executor

            self._logger(
                f"[Plugginger] Created ThreadPoolExecutor '{name}' with {resolved_max_workers} workers"
            )

        else:
            raise TypeError(
                f"config_or_executor must be ExecutorConfig or Executor, got {type(config_or_executor)}"
            )

    def get_executor(self, name: str = DEFAULT_EXECUTOR_NAME) -> Executor:
        """
        Retrieve an executor by name.

        Args:
            name: Name of the executor to retrieve (defaults to DEFAULT_EXECUTOR_NAME)

        Returns:
            The Executor instance associated with the given name

        Raises:
            KeyError: If no executor with the given name is registered
        """
        if name not in self._executors:
            error_msg = f"No executor registered with name '{name}'"
            self._logger(f"[Plugginger] ERROR: {error_msg}")
            raise KeyError(error_msg)

        return self._executors[name]

    def shutdown_executors(self, wait: bool = True) -> None:
        """
        Shutdown all registered executors.

        This method attempts to shutdown all registered executors gracefully.
        If an individual executor fails to shutdown, the error is logged but
        the shutdown process continues for other executors.

        Args:
            wait: Whether to wait for currently executing tasks to complete
        """
        self._logger("[Plugginger] Shutting down all executors...")

        for name, executor_instance in self._executors.items():
            try:
                self._logger(f"[Plugginger] Shutting down executor '{name}'")
                executor_instance.shutdown(wait=wait)
            except Exception as exc:
                # Catch all exceptions during shutdown to ensure other executors are processed
                # Common exceptions: RuntimeError (if already shut down), AttributeError, etc.
                self._logger(f"[Plugginger] ERROR: Failed to shutdown executor '{name}': {exc!r}")

        self._logger("[Plugginger] All executor shutdown attempts completed")
