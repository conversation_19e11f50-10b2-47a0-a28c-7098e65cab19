# src/plugginger/_internal/graph.py

"""
Dependency graph management for the Plugginger framework.

This module provides the DependencyGraph class for managing plugin dependencies
and determining the correct initialization order using topological sorting.
"""

from __future__ import annotations

from collections import deque
from typing import Generic, TypeVar

from plugginger.core.exceptions import CircularDependencyError

T_Node = TypeVar("T_Node")


class DependencyGraph(Generic[T_Node]):
    """
    Generic dependency graph for managing nodes and their dependencies.

    This class provides functionality for building dependency graphs, adding
    dependency relationships, and performing topological sorting to determine
    the correct processing order.

    Type Parameters:
        T_Node: Type of the nodes in the graph (typically str for plugin names)

    Attributes:
        _nodes: Set of all nodes in the graph
        _edges: Dictionary mapping each node to its set of prerequisites
        _dependents: Dictionary mapping each node to its set of dependents
    """

    def __init__(self) -> None:
        """Initialize an empty dependency graph."""
        self._nodes: set[T_Node] = set()
        self._edges: dict[T_Node, set[T_Node]] = {}  # node -> prerequisites
        self._dependents: dict[T_Node, set[T_Node]] = {}  # node -> dependents

    def add_node(self, node: T_Node) -> None:
        """
        Add a node to the graph.

        Args:
            node: Node to add to the graph

        Note:
            If the node already exists, this operation is idempotent.
        """
        if node not in self._nodes:
            self._nodes.add(node)
            self._edges[node] = set()
            self._dependents[node] = set()

    def add_dependency_edge(self, prerequisite_node: T_Node, dependent_node: T_Node) -> None:
        """
        Add a dependency edge from prerequisite to dependent.

        This creates an edge indicating that prerequisite_node must be processed
        before dependent_node. In other words: dependent_node depends on prerequisite_node.

        Args:
            prerequisite_node: Node that must be processed first
            dependent_node: Node that depends on the prerequisite

        Note:
            Both nodes will be automatically added to the graph if they don't exist.
        """
        # Ensure both nodes exist in the graph
        self.add_node(prerequisite_node)
        self.add_node(dependent_node)

        # Add the dependency relationship
        self._edges[dependent_node].add(prerequisite_node)
        self._dependents[prerequisite_node].add(dependent_node)

    def get_prerequisites(self, node: T_Node) -> set[T_Node]:
        """
        Get all direct prerequisites of a node.

        Args:
            node: Node to get prerequisites for

        Returns:
            Set of nodes that this node directly depends on

        Raises:
            KeyError: If node is not in the graph
        """
        if node not in self._nodes:
            raise KeyError(f"Node '{node}' not found in dependency graph")
        return self._edges[node].copy()

    def get_dependents(self, node: T_Node) -> set[T_Node]:
        """
        Get all direct dependents of a node.

        Args:
            node: Node to get dependents for

        Returns:
            Set of nodes that directly depend on this node

        Raises:
            KeyError: If node is not in the graph
        """
        if node not in self._nodes:
            raise KeyError(f"Node '{node}' not found in dependency graph")
        return self._dependents[node].copy()

    def get_all_nodes(self) -> set[T_Node]:
        """
        Get all nodes in the graph.

        Returns:
            Set of all nodes in the graph
        """
        return self._nodes.copy()

    def topological_sort(self) -> list[T_Node]:
        """
        Perform topological sort using Kahn's algorithm.

        Returns a list where dependencies appear before the nodes that depend on them.
        The sorting is deterministic - nodes with the same precedence are sorted
        by their string representation for consistent results.

        Returns:
            List of nodes in dependency order (prerequisites first)

        Raises:
            CircularDependencyError: If circular dependencies are detected
        """
        # Create working copies of the data structures
        in_degree = {}
        for node in self._nodes:
            in_degree[node] = len(self._edges[node])

        # Initialize queue with nodes that have no dependencies
        # Sort for deterministic ordering
        queue = deque(sorted([node for node in self._nodes if in_degree[node] == 0], key=str))
        result = []

        while queue:
            # Process node with no remaining dependencies
            current = queue.popleft()
            result.append(current)

            # Update in-degree for all dependents
            dependents_to_process = []
            for dependent in self._dependents[current]:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    dependents_to_process.append(dependent)

            # Add newly available nodes to queue in sorted order for determinism
            for dependent in sorted(dependents_to_process, key=str):
                queue.append(dependent)

        # Check for circular dependencies
        if len(result) != len(self._nodes):
            # Find remaining nodes (part of cycles)
            remaining_nodes = [node for node in self._nodes if in_degree[node] > 0]
            raise CircularDependencyError(
                f"Circular dependency detected involving nodes: {sorted(remaining_nodes, key=str)}"
            )

        return result

    def __len__(self) -> int:
        """Get the number of nodes in the graph."""
        return len(self._nodes)

    def __contains__(self, node: T_Node) -> bool:
        """Check if a node exists in the graph."""
        return node in self._nodes

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"DependencyGraph(nodes={len(self._nodes)}, edges={sum(len(deps) for deps in self._edges.values())})"
