"""
Plugin and application manifest schemas.

This package provides Pydantic models and utilities for generating and validating
plugin manifest files in YAML format. These manifests enable AI agents to
understand plugin structure without Python imports.

The manifest system supports:
- Plugin metadata (name, version, author, etc.)
- Service definitions with type signatures
- Event listener definitions with patterns
- Dependency declarations with version constraints
- Configuration schemas (JSON Schema format)
- Runtime requirements (Python version, execution mode)

Example usage:
    ```python
    from plugginger.schemas import generate_plugin_manifest, manifest_to_yaml

    # Generate manifest from plugin class
    manifest = generate_plugin_manifest(MyPlugin, author="<PERSON>")

    # Convert to YAML
    yaml_content = manifest_to_yaml(manifest)

    # Save to file
    with open("manifest.yaml", "w") as f:
        f.write(yaml_content)
    ```
"""

from plugginger.schemas.generator import (
    generate_app_manifest,
    generate_plugin_manifest,
    manifest_from_yaml,
    manifest_to_yaml,
)
from plugginger.schemas.manifest import (
    AppManifest,
    DependencyInfo,
    EventListenerInfo,
    ExecutionMode,
    ParameterInfo,
    ParameterKind,
    PluginManifest,
    PluginMetadata,
    PluginRuntime,
    ServiceInfo,
)

__all__ = [
    # Manifest models
    "PluginManifest",
    "AppManifest",
    "PluginMetadata",
    "PluginRuntime",
    "DependencyInfo",
    "ServiceInfo",
    "EventListenerInfo",
    "ParameterInfo",
    "ParameterKind",
    "ExecutionMode",

    # Generator functions
    "generate_plugin_manifest",
    "generate_app_manifest",
    "manifest_to_yaml",
    "manifest_from_yaml",
]
