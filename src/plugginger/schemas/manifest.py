"""
Plugin manifest schema definitions.

This module provides Pydantic models for validating and generating
plugin manifest files in YAML format. These manifests enable
AI agents to understand plugin structure without Python imports.
"""

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator


class ExecutionMode(str, Enum):
    """Plugin execution modes for security and isolation."""

    THREAD = "thread"
    """Execute in the same process using threads (default, fastest)."""

    PROCESS = "process"
    """Execute in a separate process (more isolation, slower)."""

    EXTERNAL = "external"
    """Execute as external service (maximum isolation, requires network)."""


class ParameterKind(str, Enum):
    """Parameter kinds for service and event method signatures."""

    POSITIONAL_ONLY = "POSITIONAL_ONLY"
    POSITIONAL_OR_KEYWORD = "POSITIONAL_OR_KEYWORD"
    VAR_POSITIONAL = "VAR_POSITIONAL"
    KEYWORD_ONLY = "KEYWORD_ONLY"
    VAR_KEYWORD = "VAR_KEYWORD"


class ParameterInfo(BaseModel):
    """Information about a method parameter."""

    name: str = Field(description="Parameter name")
    annotation: str | None = Field(default=None, description="Type annotation as string")
    default: Any = Field(default=None, description="Default value if any")
    kind: ParameterKind = Field(description="Parameter kind")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class ServiceInfo(BaseModel):
    """Information about a plugin service method."""

    name: str = Field(description="Service name")
    method_name: str = Field(description="Python method name")
    description: str | None = Field(default=None, description="Service description")
    timeout_seconds: float | None = Field(default=None, description="Service timeout")
    signature: str = Field(description="Full method signature")
    parameters: list[ParameterInfo] = Field(default_factory=list, description="Method parameters")
    return_annotation: str | None = Field(default=None, description="Return type annotation")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class EventListenerInfo(BaseModel):
    """Information about a plugin event listener method."""

    patterns: list[str] = Field(description="Event patterns this listener handles")
    method_name: str = Field(description="Python method name")
    description: str | None = Field(default=None, description="Event listener description")
    timeout_seconds: float | None = Field(default=None, description="Event listener timeout")
    priority: int = Field(default=0, description="Execution priority (higher = earlier)")
    signature: str = Field(description="Full method signature")
    parameters: list[ParameterInfo] = Field(default_factory=list, description="Method parameters")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class DependencyInfo(BaseModel):
    """Information about a plugin dependency."""

    name: str = Field(description="Dependency name")
    version: str | None = Field(default=None, description="Version constraint (SemVer)")
    optional: bool = Field(default=False, description="Whether dependency is optional")
    description: str | None = Field(default=None, description="Dependency description")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluginMetadata(BaseModel):
    """Core plugin metadata."""

    name: str = Field(description="Plugin name (must be valid Python identifier)")
    version: str = Field(description="Plugin version (PEP 440 compatible)")
    description: str | None = Field(default=None, description="Plugin description")
    author: str | None = Field(default=None, description="Plugin author")
    homepage: str | None = Field(default=None, description="Plugin homepage URL")
    repository: str | None = Field(default=None, description="Plugin repository URL")
    license: str | None = Field(default=None, description="Plugin license")
    keywords: list[str] = Field(default_factory=list, description="Plugin keywords")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate plugin name is a valid Python identifier."""
        if not v.isidentifier():
            raise ValueError(f"Plugin name '{v}' must be a valid Python identifier")
        if v.startswith("_") or v.endswith("_"):
            raise ValueError(f"Plugin name '{v}' cannot start or end with underscore")
        return v

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluginRuntime(BaseModel):
    """Plugin runtime configuration."""

    execution_mode: ExecutionMode = Field(default=ExecutionMode.THREAD, description="Execution mode")
    python_version: str | None = Field(default=None, description="Required Python version")
    plugginger_version: str = Field(description="Required Plugginger version")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluginManifest(BaseModel):
    """
    Complete plugin manifest schema.

    This model defines the structure of manifest.yaml files that describe
    plugin metadata, dependencies, services, and event listeners in a
    machine-readable format for AI agents.
    """

    # Schema version for future compatibility
    manifest_version: Literal["1.0.0"] = Field(default="1.0.0", description="Manifest schema version")

    # Core metadata
    metadata: PluginMetadata = Field(description="Plugin metadata")

    # Runtime configuration
    runtime: PluginRuntime = Field(description="Runtime configuration")

    # Dependencies
    dependencies: list[DependencyInfo] = Field(default_factory=list, description="Plugin dependencies")

    # Services exposed by this plugin
    services: list[ServiceInfo] = Field(default_factory=list, description="Services provided by plugin")

    # Event listeners in this plugin
    event_listeners: list[EventListenerInfo] = Field(default_factory=list, description="Event listeners")

    # Configuration schema (JSON Schema)
    config_schema: dict[str, Any] | None = Field(default=None, description="Plugin configuration schema")

    # Generation metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="When manifest was generated")
    generated_by: str = Field(default="plugginger", description="Tool that generated this manifest")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class AppManifest(BaseModel):
    """
    Application manifest schema.

    This model defines the structure for describing complete applications
    with multiple plugins and their relationships.
    """

    # Schema version
    manifest_version: Literal["1.0.0"] = Field(default="1.0.0", description="Manifest schema version")

    # Application metadata
    app_name: str = Field(description="Application name")
    app_version: str = Field(description="Application version")
    description: str | None = Field(default=None, description="Application description")

    # Plugins in this application
    plugins: list[str] = Field(description="List of plugin names in this application")

    # Plugin configurations
    plugin_configs: dict[str, dict[str, Any]] = Field(
        default_factory=dict,
        description="Plugin-specific configurations keyed by plugin name"
    )

    # Global application configuration
    global_config: dict[str, Any] = Field(
        default_factory=dict,
        description="Global application configuration"
    )

    # Generation metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="When manifest was generated")
    generated_by: str = Field(default="plugginger", description="Tool that generated this manifest")

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


# Export all schema models
__all__ = [
    "ExecutionMode",
    "ParameterKind",
    "ParameterInfo",
    "ServiceInfo",
    "EventListenerInfo",
    "DependencyInfo",
    "PluginMetadata",
    "PluginRuntime",
    "PluginManifest",
    "AppManifest",
]
