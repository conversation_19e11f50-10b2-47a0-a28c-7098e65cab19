"""
JSON Schema for Plugginger app graph discovery.

This module defines the JSON Schema for the output of `plugginger inspect --json`,
providing a standardized format for AI agents to understand app structure.
"""

import json
from typing import Any

# JSON Schema for app graph discovery output
APP_GRAPH_SCHEMA = {
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "$id": "https://plugginger.dev/schemas/app-graph/v1.0.0",
    "title": "Plugginger App Graph",
    "description": "Schema for Plugginger application structure discovery output",
    "type": "object",
    "required": ["app", "plugins", "dependency_graph", "metadata"],
    "properties": {
        "app": {
            "type": "object",
            "description": "Basic application information",
            "required": ["name", "plugin_count"],
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Application name"
                },
                "plugin_count": {
                    "type": "integer",
                    "minimum": 0,
                    "description": "Number of registered plugins"
                },
                "max_fractal_depth": {
                    "type": ["integer", "null"],
                    "minimum": 0,
                    "description": "Maximum fractal composition depth"
                }
            },
            "additionalProperties": False
        },
        "plugins": {
            "type": "array",
            "description": "List of registered plugins",
            "items": {
                "$ref": "#/$defs/plugin"
            }
        },
        "dependency_graph": {
            "type": "object",
            "description": "Plugin dependency graph",
            "required": ["nodes", "edges"],
            "properties": {
                "nodes": {
                    "type": "array",
                    "description": "Graph nodes (plugins)",
                    "items": {
                        "$ref": "#/$defs/graph_node"
                    }
                },
                "edges": {
                    "type": "array",
                    "description": "Graph edges (dependencies)",
                    "items": {
                        "$ref": "#/$defs/graph_edge"
                    }
                },
                "cycles": {
                    "type": "array",
                    "description": "Detected dependency cycles",
                    "items": {
                        "$ref": "#/$defs/dependency_cycle"
                    }
                }
            },
            "additionalProperties": False
        },
        "metadata": {
            "type": "object",
            "description": "Generation metadata",
            "required": ["generated_at", "generated_by", "schema_version"],
            "properties": {
                "generated_at": {
                    "type": "string",
                    "format": "date-time",
                    "description": "ISO timestamp when analysis was generated"
                },
                "generated_by": {
                    "type": "string",
                    "description": "Tool that generated this analysis"
                },
                "schema_version": {
                    "type": "string",
                    "pattern": "^\\d+\\.\\d+\\.\\d+$",
                    "description": "Schema version (semantic versioning)"
                }
            },
            "additionalProperties": False
        }
    },
    "additionalProperties": False,
    "$defs": {
        "plugin": {
            "type": "object",
            "description": "Plugin information",
            "required": ["registration_name", "class_name", "module", "services", "event_listeners", "dependencies", "metadata"],
            "properties": {
                "registration_name": {
                    "type": "string",
                    "description": "Name used for plugin registration"
                },
                "class_name": {
                    "type": "string",
                    "description": "Python class name"
                },
                "module": {
                    "type": "string",
                    "description": "Python module path"
                },
                "services": {
                    "type": "array",
                    "description": "Services provided by this plugin",
                    "items": {
                        "$ref": "#/$defs/service"
                    }
                },
                "event_listeners": {
                    "type": "array",
                    "description": "Event listeners in this plugin",
                    "items": {
                        "$ref": "#/$defs/event_listener"
                    }
                },
                "dependencies": {
                    "type": "array",
                    "description": "Plugin dependencies",
                    "items": {
                        "$ref": "#/$defs/dependency"
                    }
                },
                "metadata": {
                    "type": "object",
                    "description": "Plugin metadata from @plugin decorator",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "Plugin name"
                        },
                        "version": {
                            "type": "string",
                            "description": "Plugin version"
                        },
                        "description": {
                            "type": "string",
                            "description": "Plugin description"
                        },
                        "author": {
                            "type": "string",
                            "description": "Plugin author"
                        }
                    },
                    "additionalProperties": True
                }
            },
            "additionalProperties": False
        },
        "service": {
            "type": "object",
            "description": "Service definition",
            "required": ["name", "method_name", "signature", "metadata"],
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Service name (from @service decorator or method name)"
                },
                "method_name": {
                    "type": "string",
                    "description": "Python method name"
                },
                "signature": {
                    "$ref": "#/$defs/method_signature"
                },
                "metadata": {
                    "type": "object",
                    "description": "Service metadata from @service decorator",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "Service name"
                        },
                        "description": {
                            "type": "string",
                            "description": "Service description"
                        },
                        "timeout_seconds": {
                            "type": "number",
                            "minimum": 0,
                            "description": "Service timeout in seconds"
                        }
                    },
                    "additionalProperties": True
                }
            },
            "additionalProperties": False
        },
        "event_listener": {
            "type": "object",
            "description": "Event listener definition",
            "required": ["method_name", "signature", "metadata"],
            "properties": {
                "method_name": {
                    "type": "string",
                    "description": "Python method name"
                },
                "event_pattern": {
                    "type": ["string", "null"],
                    "description": "Primary event pattern (first pattern for backward compatibility)"
                },
                "patterns": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "All event patterns this listener handles"
                },
                "signature": {
                    "$ref": "#/$defs/method_signature"
                },
                "metadata": {
                    "type": "object",
                    "description": "Event listener metadata from @on_event decorator",
                    "properties": {
                        "patterns": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            },
                            "description": "Event patterns"
                        },
                        "priority": {
                            "type": "integer",
                            "description": "Listener priority"
                        },
                        "description": {
                            "type": "string",
                            "description": "Listener description"
                        }
                    },
                    "additionalProperties": True
                }
            },
            "additionalProperties": False
        },
        "dependency": {
            "type": "object",
            "description": "Plugin dependency",
            "required": ["name", "optional"],
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Dependency name"
                },
                "optional": {
                    "type": "boolean",
                    "description": "Whether dependency is optional"
                },
                "version": {
                    "type": ["string", "null"],
                    "description": "Version constraint"
                }
            },
            "additionalProperties": False
        },
        "method_signature": {
            "type": "object",
            "description": "Method signature information",
            "required": ["parameters", "return_type"],
            "properties": {
                "parameters": {
                    "type": "array",
                    "description": "Method parameters",
                    "items": {
                        "$ref": "#/$defs/parameter"
                    }
                },
                "return_type": {
                    "type": ["string", "null"],
                    "description": "Return type annotation as string"
                },
                "docstring": {
                    "type": "object",
                    "description": "Method docstring information",
                    "properties": {
                        "summary": {
                            "type": ["string", "null"],
                            "description": "Docstring summary"
                        },
                        "description": {
                            "type": ["string", "null"],
                            "description": "Docstring description"
                        },
                        "raw": {
                            "type": ["string", "null"],
                            "description": "Raw docstring"
                        }
                    },
                    "additionalProperties": True
                }
            },
            "additionalProperties": False
        },
        "parameter": {
            "type": "object",
            "description": "Method parameter",
            "required": ["name", "kind"],
            "properties": {
                "name": {
                    "type": "string",
                    "description": "Parameter name"
                },
                "type": {
                    "type": ["string", "null"],
                    "description": "Type annotation as string"
                },
                "default": {
                    "type": ["string", "null"],
                    "description": "Default value as string"
                },
                "kind": {
                    "type": "string",
                    "enum": ["POSITIONAL_ONLY", "POSITIONAL_OR_KEYWORD", "VAR_POSITIONAL", "KEYWORD_ONLY", "VAR_KEYWORD"],
                    "description": "Parameter kind"
                }
            },
            "additionalProperties": False
        },
        "graph_node": {
            "type": "object",
            "description": "Dependency graph node",
            "required": ["id", "type"],
            "properties": {
                "id": {
                    "type": "string",
                    "description": "Node identifier (plugin name)"
                },
                "type": {
                    "type": "string",
                    "enum": ["plugin"],
                    "description": "Node type"
                },
                "metadata": {
                    "type": "object",
                    "description": "Node metadata",
                    "properties": {
                        "class_name": {
                            "type": "string",
                            "description": "Plugin class name"
                        },
                        "module": {
                            "type": "string",
                            "description": "Plugin module"
                        },
                        "plugin_name": {
                            "type": "string",
                            "description": "Plugin name"
                        },
                        "plugin_version": {
                            "type": "string",
                            "description": "Plugin version"
                        }
                    },
                    "additionalProperties": True
                }
            },
            "additionalProperties": False
        },
        "graph_edge": {
            "type": "object",
            "description": "Dependency graph edge",
            "required": ["from", "to", "type", "optional"],
            "properties": {
                "from": {
                    "type": "string",
                    "description": "Source plugin name"
                },
                "to": {
                    "type": "string",
                    "description": "Target dependency name"
                },
                "type": {
                    "type": "string",
                    "enum": ["depends_on"],
                    "description": "Edge type"
                },
                "optional": {
                    "type": "boolean",
                    "description": "Whether dependency is optional"
                },
                "version_constraint": {
                    "type": ["string", "null"],
                    "description": "Version constraint for the dependency"
                }
            },
            "additionalProperties": False
        },
        "dependency_cycle": {
            "type": "object",
            "description": "Dependency cycle information",
            "required": ["nodes", "type"],
            "properties": {
                "nodes": {
                    "type": "array",
                    "description": "Nodes involved in the cycle",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string",
                    "enum": ["circular_dependency"],
                    "description": "Type of cycle"
                }
            },
            "additionalProperties": False
        }
    }
}


def validate_app_graph(data: dict[str, Any]) -> tuple[bool, list[str]]:
    """
    Validate app graph data against the JSON schema.

    Args:
        data: App graph data to validate

    Returns:
        Tuple of (is_valid, error_messages)
    """
    try:
        import jsonschema

        jsonschema.validate(data, APP_GRAPH_SCHEMA)
        return True, []

    except ImportError:
        # jsonschema not available, skip validation
        return True, ["jsonschema not available - validation skipped"]
    except Exception as e:
        # Handle validation errors and schema errors
        return False, [str(e)]


def get_schema_as_json() -> str:
    """
    Get the app graph schema as formatted JSON string.

    Returns:
        JSON schema as string
    """
    return json.dumps(APP_GRAPH_SCHEMA, indent=2)
