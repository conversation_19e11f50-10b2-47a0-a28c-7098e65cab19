"""
Experimental fractal composition features.

⚠️  WARNING: EXPERIMENTAL ⚠️
This module contains experimental features that may change or be removed
without notice. Do not use in production code.

Features:
- Advanced fractal composition patterns
- Multi-level app nesting
- Cross-fractal communication
"""

from __future__ import annotations

import warnings
from typing import Any, Protocol

# Issue warning when this experimental module is used
warnings.warn(
    "plugginger.experimental.fractal contains unstable APIs. "
    "Use at your own risk.",
    FutureWarning,
    stacklevel=2
)


class AdvancedFractalComposer:
    """
    Experimental advanced fractal composition features.

    This class provides experimental functionality for complex
    fractal app compositions that go beyond the basic AppPlugin
    capabilities.

    ⚠️ EXPERIMENTAL: This API may change without notice.
    """

    def __init__(self) -> None:
        """Initialize the advanced fractal composer."""
        self._fractal_depth_limit = 10
        self._cross_fractal_events: dict[str, list[str]] = {}

    def set_fractal_depth_limit(self, limit: int) -> None:
        """
        Set the maximum allowed fractal nesting depth.

        Args:
            limit: Maximum nesting depth (default: 10)
        """
        if limit < 1:
            raise ValueError("Fractal depth limit must be at least 1")
        self._fractal_depth_limit = limit

    def get_fractal_depth_limit(self) -> int:
        """
        Get the current fractal depth limit.

        Returns:
            Current maximum nesting depth
        """
        return self._fractal_depth_limit

    def register_cross_fractal_event(self, event_pattern: str, target_fractals: list[str]) -> None:
        """
        Register an event pattern for cross-fractal communication.

        ⚠️ EXPERIMENTAL: This feature is not yet implemented.

        Args:
            event_pattern: Event pattern to listen for
            target_fractals: List of fractal app names to forward events to
        """
        self._cross_fractal_events[event_pattern] = target_fractals

    def get_cross_fractal_events(self) -> dict[str, list[str]]:
        """
        Get all registered cross-fractal event mappings.

        Returns:
            Dictionary mapping event patterns to target fractal lists
        """
        return self._cross_fractal_events.copy()


class FractalMetrics:
    """
    Experimental metrics collection for fractal compositions.

    ⚠️ EXPERIMENTAL: This API may change without notice.
    """

    def __init__(self) -> None:
        """Initialize fractal metrics collector."""
        self._metrics: dict[str, Any] = {}

    def record_fractal_depth(self, app_name: str, depth: int) -> None:
        """
        Record the nesting depth of a fractal app.

        Args:
            app_name: Name of the fractal app
            depth: Nesting depth level
        """
        if "fractal_depths" not in self._metrics:
            self._metrics["fractal_depths"] = {}
        self._metrics["fractal_depths"][app_name] = depth

    def get_max_fractal_depth(self) -> int:
        """
        Get the maximum fractal depth recorded.

        Returns:
            Maximum depth, or 0 if no depths recorded
        """
        depths = self._metrics.get("fractal_depths", {})
        return max(depths.values()) if depths else 0

    def get_fractal_count(self) -> int:
        """
        Get the total number of fractal apps recorded.

        Returns:
            Number of fractal apps
        """
        depths = self._metrics.get("fractal_depths", {})
        return len(depths)


# Experimental protocol for future fractal features
class FractalCommunicationProtocol(Protocol):
    """
    Experimental protocol for cross-fractal communication.

    ⚠️ EXPERIMENTAL: This protocol may change without notice.
    """

    def send_to_fractal(self, target_fractal: str, event_data: dict[str, Any]) -> None:
        """
        Send data to another fractal app.

        Args:
            target_fractal: Name of the target fractal app
            event_data: Data to send
        """
        ...

    def receive_from_fractal(self, source_fractal: str) -> dict[str, Any] | None:
        """
        Receive data from another fractal app.

        Args:
            source_fractal: Name of the source fractal app

        Returns:
            Received data, or None if no data available
        """
        ...


# Export experimental fractal features
__all__ = [
    "AdvancedFractalComposer",
    "FractalMetrics",
    "FractalCommunicationProtocol",
]
