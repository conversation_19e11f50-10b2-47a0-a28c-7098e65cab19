"""
Experimental APIs for Plugginger Framework.

⚠️  WARNING: EXPERIMENTAL FEATURES ⚠️

All APIs in this namespace are EXPERIMENTAL and may change or be removed
without notice in future versions. They are not covered by semantic versioning
guarantees and should not be used in production code.

These features are provided for:
- Early testing and feedback
- Research and development
- Proof-of-concept implementations

Use at your own risk!

Available experimental modules:
- fractal: Advanced fractal composition features
- events: Event sourcing and advanced event patterns
- registry: Plugin marketplace and discovery features
"""

from __future__ import annotations

import warnings
from typing import Any

# Issue deprecation warning when experimental module is imported
warnings.warn(
    "plugginger.experimental contains unstable APIs that may change or be removed "
    "without notice. Use at your own risk and avoid in production code.",
    FutureWarning,
    stacklevel=2
)

# Registry for experimental API components
# Maps experimental API names to (module_path, attribute_name) tuples
_EXPERIMENTAL_API_REGISTRY: dict[str, tuple[str, str]] = {
    # Fractal composition experimental features
    "AdvancedFractalComposer": ("plugginger.experimental.fractal", "AdvancedFractalComposer"),
    "FractalMetrics": ("plugginger.experimental.fractal", "FractalMetrics"),
    "FractalCommunicationProtocol": ("plugginger.experimental.fractal", "FractalCommunicationProtocol"),

    # Event sourcing experimental features
    "EventRecord": ("plugginger.experimental.events", "EventRecord"),
    "EventSourcingMixin": ("plugginger.experimental.events", "EventSourcingMixin"),
    "AdvancedEventFilter": ("plugginger.experimental.events", "AdvancedEventFilter"),
    "EventPersistenceProtocol": ("plugginger.experimental.events", "EventPersistenceProtocol"),

    # Plugin registry experimental features
    "PluginStatus": ("plugginger.experimental.registry", "PluginStatus"),
    "PluginMetadata": ("plugginger.experimental.registry", "PluginMetadata"),
    "PluginRegistry": ("plugginger.experimental.registry", "PluginRegistry"),
    "PluginDiscovery": ("plugginger.experimental.registry", "PluginDiscovery"),
    "PluginLoaderProtocol": ("plugginger.experimental.registry", "PluginLoaderProtocol"),
}

# Public API exports - derived from registry keys
__all__ = list(_EXPERIMENTAL_API_REGISTRY.keys())


def __getattr__(name: str) -> Any:
    """
    Provide access to experimental framework components through lazy imports.

    This allows us to defer imports until actually needed, reducing startup time
    and avoiding circular import issues.

    Args:
        name: The name of the experimental component to import

    Returns:
        The requested experimental component

    Raises:
        AttributeError: If the requested component is not available
    """
    if name in _EXPERIMENTAL_API_REGISTRY:
        module_path, attribute_name = _EXPERIMENTAL_API_REGISTRY[name]

        # Import the module dynamically
        import importlib
        module = importlib.import_module(module_path)

        # Get the specific attribute from the module
        return getattr(module, attribute_name)

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


def get_experimental_features() -> list[str]:
    """
    Get a list of all available experimental features.

    Returns:
        List of experimental feature names
    """
    return list(_EXPERIMENTAL_API_REGISTRY.keys())


def is_experimental_feature(name: str) -> bool:
    """
    Check if a given name is an experimental feature.

    Args:
        name: The feature name to check

    Returns:
        True if the feature is experimental, False otherwise
    """
    return name in _EXPERIMENTAL_API_REGISTRY
