"""
Experimental event sourcing and advanced event patterns.

⚠️  WARNING: EXPERIMENTAL ⚠️
This module contains experimental features that may change or be removed
without notice. Do not use in production code.

Features:
- Event sourcing patterns
- Event replay capabilities
- Advanced event filtering
- Event persistence
"""

from __future__ import annotations

import warnings
from collections.abc import Callable
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Protocol

# Issue warning when this experimental module is used
warnings.warn(
    "plugginger.experimental.events contains unstable APIs. "
    "Use at your own risk.",
    FutureWarning,
    stacklevel=2
)


@dataclass
class EventRecord:
    """
    Experimental event record for event sourcing.

    ⚠️ EXPERIMENTAL: This structure may change without notice.
    """
    event_id: str
    event_type: str
    event_data: dict[str, Any]
    timestamp: datetime
    source_plugin: str
    metadata: dict[str, Any]


class EventSourcingMixin:
    """
    Experimental mixin for event sourcing capabilities.

    This mixin can be added to plugins to provide event sourcing
    functionality for state reconstruction and audit trails.

    ⚠️ EXPERIMENTAL: This API may change without notice.
    """

    def __init__(self) -> None:
        """Initialize event sourcing mixin."""
        self._event_store: list[EventRecord] = []
        self._event_handlers: dict[str, list[Callable[[EventRecord], None]]] = {}

    def record_event(
        self,
        event_type: str,
        event_data: dict[str, Any],
        metadata: dict[str, Any] | None = None
    ) -> str:
        """
        Record an event in the event store.

        Args:
            event_type: Type of the event
            event_data: Event payload data
            metadata: Optional metadata

        Returns:
            Event ID
        """
        import uuid

        event_id = str(uuid.uuid4())
        event_record = EventRecord(
            event_id=event_id,
            event_type=event_type,
            event_data=event_data,
            timestamp=datetime.utcnow(),
            source_plugin=self.__class__.__name__,
            metadata=metadata or {}
        )

        self._event_store.append(event_record)
        self._dispatch_event(event_record)

        return event_id

    def replay_events(
        self,
        event_type_filter: str | None = None,
        from_timestamp: datetime | None = None
    ) -> list[EventRecord]:
        """
        Replay events from the event store.

        Args:
            event_type_filter: Optional filter by event type
            from_timestamp: Optional filter by timestamp

        Returns:
            List of matching event records
        """
        events = self._event_store

        if event_type_filter:
            events = [e for e in events if e.event_type == event_type_filter]

        if from_timestamp:
            events = [e for e in events if e.timestamp >= from_timestamp]

        return events

    def register_event_handler(
        self,
        event_type: str,
        handler: Callable[[EventRecord], None]
    ) -> None:
        """
        Register a handler for specific event types.

        Args:
            event_type: Event type to handle
            handler: Handler function
        """
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)

    def _dispatch_event(self, event_record: EventRecord) -> None:
        """
        Dispatch an event to registered handlers.

        Args:
            event_record: Event to dispatch
        """
        handlers = self._event_handlers.get(event_record.event_type, [])
        for handler in handlers:
            try:
                handler(event_record)
            except Exception:
                # In experimental code, we silently ignore handler errors
                # In production, this would need proper error handling
                pass


class AdvancedEventFilter:
    """
    Experimental advanced event filtering capabilities.

    ⚠️ EXPERIMENTAL: This API may change without notice.
    """

    def __init__(self) -> None:
        """Initialize advanced event filter."""
        self._filters: list[Callable[[dict[str, Any]], bool]] = []

    def add_filter(self, filter_func: Callable[[dict[str, Any]], bool]) -> None:
        """
        Add a custom filter function.

        Args:
            filter_func: Function that returns True if event should pass
        """
        self._filters.append(filter_func)

    def should_process_event(self, event_data: dict[str, Any]) -> bool:
        """
        Check if an event should be processed based on filters.

        Args:
            event_data: Event data to check

        Returns:
            True if event should be processed
        """
        return all(f(event_data) for f in self._filters)

    def clear_filters(self) -> None:
        """Clear all registered filters."""
        self._filters.clear()


# Experimental protocol for event persistence
class EventPersistenceProtocol(Protocol):
    """
    Experimental protocol for event persistence.

    ⚠️ EXPERIMENTAL: This protocol may change without notice.
    """

    def persist_event(self, event_record: EventRecord) -> None:
        """
        Persist an event record.

        Args:
            event_record: Event to persist
        """
        ...

    def load_events(
        self,
        event_type_filter: str | None = None,
        from_timestamp: datetime | None = None
    ) -> list[EventRecord]:
        """
        Load events from persistence.

        Args:
            event_type_filter: Optional filter by event type
            from_timestamp: Optional filter by timestamp

        Returns:
            List of matching event records
        """
        ...


# Export experimental event features
__all__ = [
    "EventRecord",
    "EventSourcingMixin",
    "AdvancedEventFilter",
    "EventPersistenceProtocol",
]
