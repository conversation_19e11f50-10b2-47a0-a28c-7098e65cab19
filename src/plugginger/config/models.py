# src/plugginger/config/models.py

"""
Pydantic models for Plugginger configuration structures.

This module defines the core configuration models used throughout
the Plugginger framework for type-safe configuration management.
"""

from __future__ import annotations

from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, Field

from plugginger.core.config import EventListenerFaultPolicy, LogLevel


class ExecutorConfig(BaseModel):
    """Configuration for a named ThreadPoolExecutor."""

    name: str = Field(
        description="Unique name for this executor configuration"
    )
    max_workers: int = Field(
        default=4,
        ge=1,
        description="Maximum number of worker threads in the pool"
    )
    thread_name_prefix: str = Field(
        default="PluggingerWorker",
        description="Prefix for worker thread names"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class GlobalAppConfig(BaseModel):
    """
    Global configuration for a PluggingerAppInstance.

    This model defines the main application configuration that is
    validated by PluggingerAppBuilder.build().
    """

    app_name: str = Field(
        default="PluggingerApp",
        min_length=1,
        description="The name of the application instance"
    )

    log_level: LogLevel = Field(
        default=LogLevel.INFO,
        description="Logging level for the application"
    )

    event_listener_fault_policy: EventListenerFaultPolicy = Field(
        default=EventListenerFaultPolicy.FAIL_FAST,
        description="Strategy for handling errors in event listeners"
    )

    default_event_listener_timeout_seconds: float = Field(
        default=5.0,
        gt=0.0,
        description="Default timeout for event listeners in seconds"
    )

    executors: list[ExecutorConfig] = Field(
        default_factory=lambda: [ExecutorConfig(name="default")],
        description="Thread pool executor configurations"
    )

    plugin_configs: dict[str, dict[str, Any]] = Field(
        default_factory=dict,
        description="Plugin-specific configurations keyed by plugin instance_id"
    )

    max_fractal_depth: int = Field(
        default=10,
        ge=1,
        description="Maximum nesting depth for fractal AppPlugin composition"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


# --- Lockfile Models ---

class PluginLockInfo(BaseModel):
    """Information about a locked plugin in the lockfile."""

    name: str = Field(description="Plugin name")
    version: str = Field(description="Plugin version")
    instance_id: str = Field(description="Unique instance identifier")
    source_location: str | None = Field(
        default=None,
        description="Source location (file path, git repo, etc.)"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PythonPackageLockInfo(BaseModel):
    """Information about a locked Python package dependency."""

    name: str = Field(description="Package name")
    version: str = Field(description="Exact package version")
    source: Literal["pypi", "git", "local", "other"] = Field(
        default="pypi",
        description="Source of the package"
    )
    integrity_hash: str | None = Field(
        default=None,
        description="Integrity hash for verification"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluggingerLockfileMetadata(BaseModel):
    """Metadata for the Plugginger lockfile."""

    lockfile_version: str = Field(
        default="1.0.0",
        description="Version of the lockfile format"
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="When the lockfile was created"
    )
    created_by: str = Field(
        default="plugginger-cli",
        description="Tool that created the lockfile"
    )
    python_version: str | None = Field(
        default=None,
        description="Python version used during freeze"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluggingerLockfile(BaseModel):
    """
    Complete structure of a plugginger.lock.json file.

    This model defines the schema for lockfiles generated by
    the 'plugginger core freeze' command.
    """

    metadata: PluggingerLockfileMetadata = Field(
        default_factory=PluggingerLockfileMetadata,
        description="Lockfile metadata"
    )

    app_name: str = Field(
        description="Name of the application this lockfile pertains to"
    )

    plugins: list[PluginLockInfo] = Field(
        default_factory=list,
        description="List of all plugins in the locked application"
    )

    resolved_python_packages: list[PythonPackageLockInfo] = Field(
        default_factory=list,
        description="Resolved Python package dependencies"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }
