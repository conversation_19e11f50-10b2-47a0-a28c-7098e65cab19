# src/plugginger/config/__init__.py

"""
Configuration management for Plugginger framework.

This package provides Pydantic models and utilities for managing
application and plugin configurations with type safety and validation.
"""

from plugginger.config.models import (
    ExecutorConfig,
    GlobalAppConfig,
    PluggingerLockfile,
    PluggingerLockfileMetadata,
    PluginLockInfo,
    PythonPackageLockInfo,
)

__all__ = [
    "GlobalAppConfig",
    "ExecutorConfig",
    "PluginLockInfo",
    "PythonPackageLockInfo",
    "PluggingerLockfileMetadata",
    "PluggingerLockfile",
]
