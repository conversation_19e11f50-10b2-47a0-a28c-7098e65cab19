# src/plugginger/testing/mock_app.py

"""
Mock application instance for testing.

This module provides mock implementations of the Plugginger application
components for isolated testing of plugins and services.
"""

from __future__ import annotations

import asyncio
import concurrent.futures
import logging
from typing import Any


class MockServiceDispatcher:
    """Mock service dispatcher for testing."""

    def __init__(self) -> None:
        """Initialize the mock service dispatcher."""
        self._services: dict[str, Any] = {}
        self._call_history: list[tuple[str, tuple[Any, ...], dict[str, Any]]] = []

    def add_service(self, service_name: str, service_method: Any) -> None:
        """Register a mock service."""
        self._services[service_name] = service_method

    async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
        """Call a mock service and record the call."""
        self._call_history.append((service_name, args, kwargs))

        if service_name in self._services:
            service = self._services[service_name]
            if asyncio.iscoroutinefunction(service):
                return await service(*args, **kwargs)
            else:
                return service(*args, **kwargs)

        # Return a mock result if service not found
        return f"mock_result_for_{service_name}"

    def has_service(self, service_name: str) -> bool:
        """Check if a service is registered."""
        return service_name in self._services

    def list_services(self) -> list[str]:
        """Get list of registered services."""
        return list(self._services.keys())

    def get_call_history(self) -> list[tuple[str, tuple[Any, ...], dict[str, Any]]]:
        """Get the history of service calls."""
        return self._call_history.copy()

    def clear_call_history(self) -> None:
        """Clear the service call history."""
        self._call_history.clear()

    def was_called(self, service_name: str) -> bool:
        """Check if a service was called."""
        return any(call[0] == service_name for call in self._call_history)

    def call_count(self, service_name: str) -> int:
        """Get the number of times a service was called."""
        return sum(1 for call in self._call_history if call[0] == service_name)

    def remove_service(self, service_name: str) -> bool:
        """Remove a mock service."""
        if service_name in self._services:
            del self._services[service_name]
            return True
        return False


class MockEventDispatcher:
    """Mock event dispatcher for testing."""

    def __init__(self) -> None:
        """Initialize the mock event dispatcher."""
        self._listeners: dict[str, list[Any]] = {}
        self._event_history: list[tuple[str, dict[str, Any]]] = []

    def add_listener(self, event_pattern: str, listener: Any) -> None:
        """Register a mock event listener."""
        if event_pattern not in self._listeners:
            self._listeners[event_pattern] = []
        self._listeners[event_pattern].append(listener)

    async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
        """Emit a mock event and record it."""
        self._event_history.append((event_type, event_data.copy()))

        # Call matching listeners
        for pattern, listeners in self._listeners.items():
            if self._pattern_matches(pattern, event_type):
                for listener in listeners:
                    try:
                        if asyncio.iscoroutinefunction(listener):
                            await listener(event_data, event_type)
                        else:
                            listener(event_data, event_type)
                    except Exception:
                        # Ignore listener errors in mock
                        pass

    def _pattern_matches(self, pattern: str, event_type: str) -> bool:
        """Check if an event pattern matches an event type."""
        if pattern == event_type:
            return True

        # Simple wildcard matching
        if "*" in pattern:
            pattern_parts = pattern.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                return event_type.startswith(prefix) and event_type.endswith(suffix)

        return False

    def remove_listener(self, event_pattern: str, listener: Any) -> bool:
        """Remove an event listener."""
        if event_pattern in self._listeners:
            try:
                self._listeners[event_pattern].remove(listener)
                return True
            except ValueError:
                pass
        return False

    def list_patterns(self) -> list[str]:
        """Get list of registered event patterns."""
        return list(self._listeners.keys())

    async def shutdown(self) -> None:
        """Shutdown the mock event dispatcher."""
        self._listeners.clear()

    def get_event_history(self) -> list[tuple[str, dict[str, Any]]]:
        """Get the history of emitted events."""
        return self._event_history.copy()

    def clear_event_history(self) -> None:
        """Clear the event emission history."""
        self._event_history.clear()

    def was_emitted(self, event_type: str) -> bool:
        """Check if an event was emitted."""
        return any(event[0] == event_type for event in self._event_history)

    def emission_count(self, event_type: str) -> int:
        """Get the number of times an event was emitted."""
        return sum(1 for event in self._event_history if event[0] == event_type)


class MockPluggingerAppInstance:
    """Mock Plugginger application instance for testing."""

    def __init__(self, app_name: str = "MockApp") -> None:
        """Initialize the mock app instance."""
        self._app_name = app_name
        self._service_dispatcher = MockServiceDispatcher()
        self._event_dispatcher = MockEventDispatcher()
        self._managed_tasks: dict[str, asyncio.Task[Any]] = {}
        self._task_counter = 0
        self._is_shutdown = False
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=2, thread_name_prefix="MockWorker")
        self._logger = logging.getLogger(f"plugginger.mock.{app_name}")

    async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
        """Call a service through the mock dispatcher."""
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        return await self._service_dispatcher.call_service(service_name, *args, **kwargs)

    async def emit_event(
        self, event_type: str, event_data: dict[str, Any], *, wait_for_listeners: bool = True
    ) -> None:
        """Emit an event through the mock dispatcher."""
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        await self._event_dispatcher.emit_event(event_type, event_data)

    def create_managed_task(
        self, coro: Any, *, name: str | None = None, plugin_id: str | None = None
    ) -> asyncio.Task[Any]:
        """Create a managed background task."""
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        if not asyncio.iscoroutine(coro):
            raise ValueError(f"Expected coroutine, got: {type(coro)}")

        # Generate task ID
        self._task_counter += 1
        task_id = f"mock_task_{self._task_counter}"
        if name:
            task_id = f"{name}_{self._task_counter}"

        # Create the task
        task = asyncio.create_task(coro, name=task_id)
        self._managed_tasks[task_id] = task

        # Add cleanup callback
        task.add_done_callback(lambda t: self._cleanup_task(task_id))

        return task

    def get_managed_tasks(
        self, plugin_id: str | None = None
    ) -> list[asyncio.Task[Any]]:
        """Get all managed tasks."""
        return list(self._managed_tasks.values())

    async def cancel_managed_tasks(self, plugin_id: str | None = None) -> None:
        """Cancel all managed tasks."""
        tasks_to_cancel = list(self._managed_tasks.values())

        for task in tasks_to_cancel:
            if not task.done():
                task.cancel()

        if tasks_to_cancel:
            await asyncio.gather(*tasks_to_cancel, return_exceptions=True)

    def _cleanup_task(self, task_id: str) -> None:
        """Clean up a completed task."""
        self._managed_tasks.pop(task_id, None)

    async def shutdown(self) -> None:
        """Shutdown the mock app instance."""
        if self._is_shutdown:
            return

        self._is_shutdown = True
        await self.cancel_managed_tasks()
        await self._event_dispatcher.shutdown()
        self._executor.shutdown(wait=True)

    def get_executor(self, name: str = "default") -> concurrent.futures.ThreadPoolExecutor:
        """Get a thread pool executor for background tasks."""
        return self._executor

    def list_services(self) -> list[str]:
        """List all registered services."""
        return self._service_dispatcher.list_services()

    def list_event_patterns(self) -> list[str]:
        """List all registered event patterns."""
        return self._event_dispatcher.list_patterns()

    async def run(self) -> None:
        """Run the mock application (placeholder for compatibility)."""
        self._logger.info(f"Mock app '{self._app_name}' is running")
        try:
            # Keep running until shutdown
            while not self._is_shutdown:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
        finally:
            await self.shutdown()

    async def start_all_plugins(self) -> None:
        """Start all plugins (mock implementation)."""
        self._logger.info("Starting all plugins (mock)")

    async def stop_all_plugins(self) -> None:
        """Stop all plugins (mock implementation)."""
        self._logger.info("Stopping all plugins (mock)")

    @property
    def app_name(self) -> str:
        """Get the application name."""
        return self._app_name

    @property
    def is_shutdown(self) -> bool:
        """Check if the application has been shut down."""
        return self._is_shutdown

    @property
    def task_count(self) -> int:
        """Get the number of active managed tasks."""
        return len(self._managed_tasks)

    @property
    def service_dispatcher(self) -> MockServiceDispatcher:
        """Get the mock service dispatcher."""
        return self._service_dispatcher

    @property
    def event_dispatcher(self) -> MockEventDispatcher:
        """Get the mock event dispatcher."""
        return self._event_dispatcher

    def __repr__(self) -> str:
        """String representation for debugging."""
        return (
            f"MockPluggingerAppInstance("
            f"name='{self._app_name}', "
            f"tasks={len(self._managed_tasks)}, "
            f"shutdown={self._is_shutdown})"
        )


def create_mock_app(app_name: str = "MockApp") -> MockPluggingerAppInstance:
    """
    Create a mock application instance for testing.

    Args:
        app_name: Name for the mock application

    Returns:
        Mock application instance
    """
    return MockPluggingerAppInstance(app_name)
