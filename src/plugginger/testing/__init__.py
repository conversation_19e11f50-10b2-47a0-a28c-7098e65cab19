# src/plugginger/testing/__init__.py

"""
Testing framework for the Plugginger plugin system.

This module provides comprehensive testing utilities including mock objects,
event collectors, service call collectors, and test helpers for isolated
plugin testing.

Example:
    ```python
    from plugginger.testing import (
        create_mock_app,
        create_plugin_test_runner,
        create_test_fixture,
        EventCollector,
        ServiceCallCollector
    )

    # Create a mock app for testing
    mock_app = create_mock_app("TestApp")

    # Test a plugin in isolation
    async with create_plugin_test_runner(MyPlugin) as runner:
        runner.inject_dependency("config", {"setting": "value"})

        # Call plugin services
        result = await runner.call_service("my_service", param="test")

        # Emit events to plugin
        await runner.emit_event("test.event", {"data": "value"})

        # Verify behavior
        assert runner.service_collector.was_called("my_service")
        assert runner.event_collector.has_event("test.event")
    ```
"""

from __future__ import annotations

# Collectors
from plugginger.testing.collectors import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Collector,
    ServiceCallCollector,
    create_event_collector,
    create_service_call_collector,
)

# Test helpers
from plugginger.testing.helpers import (
    PluginTestRunner,
    TestFixture,
    create_plugin_test_runner,
    create_test_fixture,
    run_plugin_standalone,
)

# Mock objects
from plugginger.testing.mock_app import (
    MockEventDispatcher,
    MockPluggingerAppInstance,
    MockServiceDispatcher,
    create_mock_app,
)

__all__ = [
    # Mock objects
    "MockServiceDispatcher",
    "MockEventDispatcher",
    "MockPluggingerAppInstance",
    "create_mock_app",

    # Collectors
    "EventCollector",
    "ServiceCallCollector",
    "CollectorManager",
    "create_event_collector",
    "create_service_call_collector",

    # Test helpers
    "PluginTestRunner",
    "TestFixture",
    "create_plugin_test_runner",
    "create_test_fixture",
    "run_plugin_standalone",
]
