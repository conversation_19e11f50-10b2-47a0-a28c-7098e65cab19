"""
API stability markers and utilities.

This module provides decorators and utilities for marking API stability levels
and managing the transition from experimental to stable APIs.
"""

from __future__ import annotations

import functools
import warnings
from collections.abc import Callable
from enum import Enum
from typing import Any, TypeVar, cast

F = TypeVar("F", bound=Callable[..., Any])


class StabilityLevel(Enum):
    """
    API stability levels for Plugginger framework.

    These levels indicate the stability guarantees for different APIs
    and help users understand what changes to expect.
    """

    EXPERIMENTAL = "experimental"
    """
    Experimental APIs that may change or be removed without notice.
    Not covered by semantic versioning guarantees.
    """

    STABLE_CANDIDATE = "stable_candidate"
    """
    APIs that are feature-complete and unlikely to change.
    May receive minor breaking changes if critical issues are found.
    """

    STABLE = "stable"
    """
    Stable APIs that follow strict backward compatibility rules.
    Breaking changes require major version bump.
    """

    DEPRECATED = "deprecated"
    """
    Deprecated APIs that will be removed in a future version.
    Users should migrate to replacement APIs.
    """


def stability(level: StabilityLevel, since: str | None = None) -> Callable[[F], F]:
    """
    Mark an API with its stability level.

    This decorator adds metadata about the stability level of an API,
    helping users understand what changes to expect and when.

    Args:
        level: The stability level of the API
        since: Version when this stability level was assigned

    Returns:
        Decorator function that adds stability metadata

    Example:
        ```python
        @stability(StabilityLevel.STABLE_CANDIDATE, since="v0.9.0")
        def my_stable_function() -> str:
            return "This is a stable candidate API"

        @stability(StabilityLevel.EXPERIMENTAL)
        def my_experimental_function() -> str:
            return "This is experimental and may change"
        ```
    """

    def decorator(func: F) -> F:
        # Add stability metadata
        func.__stability_level__ = level  # type: ignore[attr-defined]
        func.__stability_since__ = since  # type: ignore[attr-defined]

        # Issue warning for experimental APIs
        if level == StabilityLevel.EXPERIMENTAL:
            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Any:
                warnings.warn(
                    f"{func.__name__} is experimental and may change or be removed "
                    f"without notice. Use at your own risk.",
                    FutureWarning,
                    stacklevel=2
                )
                return func(*args, **kwargs)

            # Preserve metadata on wrapper
            wrapper.__stability_level__ = level  # type: ignore[attr-defined]
            wrapper.__stability_since__ = since  # type: ignore[attr-defined]

            return cast(F, wrapper)

        return func

    return decorator


def experimental(since: str | None = None) -> Callable[[F], F]:
    """
    Mark an API as experimental.

    Shorthand for @stability(StabilityLevel.EXPERIMENTAL).
    Issues a FutureWarning when the API is used.

    Args:
        since: Version when this API became experimental

    Returns:
        Decorator function that marks API as experimental

    Example:
        ```python
        @experimental(since="v0.9.0")
        def new_feature() -> str:
            return "This feature is still experimental"
        ```
    """
    return stability(StabilityLevel.EXPERIMENTAL, since=since)


def stable_candidate(since: str | None = None) -> Callable[[F], F]:
    """
    Mark an API as a stable candidate.

    Shorthand for @stability(StabilityLevel.STABLE_CANDIDATE).
    These APIs are feature-complete and unlikely to change.

    Args:
        since: Version when this API became a stable candidate

    Returns:
        Decorator function that marks API as stable candidate

    Example:
        ```python
        @stable_candidate(since="v0.9.0")
        def core_feature() -> str:
            return "This is a stable candidate API"
        ```
    """
    return stability(StabilityLevel.STABLE_CANDIDATE, since=since)


def stable(since: str | None = None) -> Callable[[F], F]:
    """
    Mark an API as stable.

    Shorthand for @stability(StabilityLevel.STABLE).
    These APIs follow strict backward compatibility rules.

    Args:
        since: Version when this API became stable

    Returns:
        Decorator function that marks API as stable

    Example:
        ```python
        @stable(since="v1.0.0")
        def established_feature() -> str:
            return "This is a stable API"
        ```
    """
    return stability(StabilityLevel.STABLE, since=since)


def get_stability_level(obj: Any) -> StabilityLevel | None:
    """
    Get the stability level of an object.

    Args:
        obj: Object to check for stability level

    Returns:
        StabilityLevel if marked, None otherwise

    Example:
        ```python
        level = get_stability_level(some_function)
        if level == StabilityLevel.EXPERIMENTAL:
            print("This function is experimental!")
        ```
    """
    return getattr(obj, "__stability_level__", None)


def get_stability_info(obj: Any) -> dict[str, Any] | None:
    """
    Get complete stability information for an object.

    Args:
        obj: Object to get stability info for

    Returns:
        Dictionary with stability info, or None if not marked

    Example:
        ```python
        info = get_stability_info(some_function)
        if info:
            print(f"Stability: {info['level'].value}")
            print(f"Since: {info['since']}")
        ```
    """
    level = get_stability_level(obj)
    if level is None:
        return None

    return {
        "level": level,
        "since": getattr(obj, "__stability_since__", None),
    }


def is_experimental(obj: Any) -> bool:
    """
    Check if an object is marked as experimental.

    Args:
        obj: Object to check

    Returns:
        True if experimental, False otherwise

    Example:
        ```python
        if is_experimental(some_function):
            print("Use with caution - this is experimental!")
        ```
    """
    return get_stability_level(obj) == StabilityLevel.EXPERIMENTAL


def is_stable_candidate(obj: Any) -> bool:
    """
    Check if an object is marked as a stable candidate.

    Args:
        obj: Object to check

    Returns:
        True if stable candidate, False otherwise

    Example:
        ```python
        if is_stable_candidate(some_function):
            print("This API is unlikely to change")
        ```
    """
    return get_stability_level(obj) == StabilityLevel.STABLE_CANDIDATE


def is_stable(obj: Any) -> bool:
    """
    Check if an object is marked as stable.

    Args:
        obj: Object to check

    Returns:
        True if stable, False otherwise

    Example:
        ```python
        if is_stable(some_function):
            print("This API follows strict backward compatibility")
        ```
    """
    return get_stability_level(obj) == StabilityLevel.STABLE


class StabilityMixin:
    """
    Mixin class for adding stability information to classes.

    This mixin can be added to classes to automatically mark them
    with stability levels and issue appropriate warnings.

    Example:
        ```python
        class ExperimentalPlugin(StabilityMixin, PluginBase):
            __stability_level__ = StabilityLevel.EXPERIMENTAL
            __stability_since__ = "v0.9.0"
        ```
    """

    def __init_subclass__(cls, **kwargs: Any) -> None:
        """Process stability markers when subclass is created."""
        super().__init_subclass__(**kwargs)

        # Check if this class has stability markers
        level = getattr(cls, "__stability_level__", None)

        if level == StabilityLevel.EXPERIMENTAL:
            # Wrap __init__ to issue experimental warning
            original_init = cls.__init__

            @functools.wraps(original_init)
            def experimental_init(self: Any, *args: Any, **kwargs: Any) -> None:
                warnings.warn(
                    f"{cls.__name__} is experimental and may change or be removed "
                    f"without notice. Use at your own risk.",
                    FutureWarning,
                    stacklevel=2
                )
                original_init(self, *args, **kwargs)

            # Use type() to avoid mypy method assignment error
            type.__setattr__(cls, "__init__", experimental_init)


def check_stability_compatibility(required_level: StabilityLevel, obj: Any) -> bool:
    """
    Check if an object meets the required stability level.

    Args:
        required_level: Minimum required stability level
        obj: Object to check

    Returns:
        True if object meets or exceeds required stability level

    Example:
        ```python
        # Check if function is at least stable candidate
        if check_stability_compatibility(StabilityLevel.STABLE_CANDIDATE, my_func):
            print("Safe to use in production")
        ```
    """
    obj_level = get_stability_level(obj)

    if obj_level is None:
        # Assume stable if not marked (for backward compatibility)
        return True

    # Define stability hierarchy (higher number = more stable)
    stability_order = {
        StabilityLevel.EXPERIMENTAL: 0,
        StabilityLevel.STABLE_CANDIDATE: 1,
        StabilityLevel.STABLE: 2,
        StabilityLevel.DEPRECATED: -1,  # Special case - never compatible
    }

    obj_stability = stability_order.get(obj_level, 0)
    required_stability = stability_order.get(required_level, 0)

    return obj_stability >= required_stability


# Export all stability utilities
__all__ = [
    "StabilityLevel",
    "stability",
    "experimental",
    "stable_candidate",
    "stable",
    "get_stability_level",
    "get_stability_info",
    "is_experimental",
    "is_stable_candidate",
    "is_stable",
    "StabilityMixin",
    "check_stability_compatibility",
]
