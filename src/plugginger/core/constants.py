# src/plugginger/_internal/constants.py

"""
Defines internal constants used consistently across the Plugginger framework.

These constants are primarily for identifying metadata attributes that are
dynamically attached to plugin classes and their methods by Plugginger's
decorators (e.g., `@plugin`, `@service`, `@on_event`, `@background_task`).
The Plugginger kernel (specifically the `PluggingerAppBuilder` and runtime
components) uses these keys to discover and interpret the capabilities and
configurations of plugins.

Using centralized constants helps avoid typos and ensures consistency when
accessing this metadata. These are considered internal implementation details
and should not be relied upon by user code.
"""

from __future__ import annotations  # Ensures all type hints are treated as forward references

from typing import Final  # For declaring constants with type checking support

# Metadata key set by the @plugin decorator on a plugin class.
# The value associated with this key is typically a boolean `True` to indicate
# that the class has been processed by the @plugin decorator.
# Other metadata like name, version, config_schema are stored as direct attributes
# (e.g., `_plugginger_plugin_name`) on the class by the @plugin decorator.
PLUGIN_METADATA_KEY: Final[str] = "_plugginger_is_plugin_class_meta"


# Metadata key set by the @service decorator on a plugin's service method.
# The value associated with this key is a dictionary containing service configuration,
# for example: `{"is_service": True, "custom_name": Optional[str]}`.
# - "is_service": Always True for methods decorated with @service.
# - "custom_name": The user-defined local name for the service if provided via
#   `@service(name="...")`, otherwise None (implying method name is used).
SERVICE_METADATA_KEY: Final[str] = "_plugginger_service_config"


# Metadata key set by the @on_event decorator on a plugin's event listener method.
# The value associated with this key is a dictionary containing event listener
# configuration, for example: `{"event_patterns": Tuple[str, ...]}`.
# - "event_patterns": A tuple of event type strings or patterns the listener subscribes to.
EVENT_METADATA_KEY: Final[str] = "_plugginger_event_config"


# Metadata key set by the @background_task decorator on a plugin's method.
# The value associated with this key is a string: the name of the
# `ThreadPoolExecutor` instance (managed by `ExecutorRegistry`) that should
# be used to run this background task.
BACKGROUND_TASK_EXECUTOR_KEY: Final[str] = "_plugginger_background_executor_name"


# Default name for the primary `ThreadPoolExecutor` instance managed by the
# `ExecutorRegistry` within a `PluggingerAppInstance`. Plugins using
# `@background_task` without specifying an executor name will use this one.
DEFAULT_EXECUTOR_NAME: Final[str] = "default"


# Default application name used when no name is specified
DEFAULT_APP_NAME: Final[str] = "PluggingerApp"


# Default prefix for instance IDs generated by the PluggingerAppBuilder for plugins
# in a top-level application. This is not strictly a metadata key but a convention
# used in ID generation. Sub-applications (AppPlugins) will typically use their
# AppPlugin's registration name (as seen by the outer app) as a prefix for their
# internal plugin instance IDs.
# This constant might not be directly used if instance_id generation is purely dynamic
# based on app_name and plugin_name.
# Example: If app_name is "main_app", a top-level plugin "db" might get "main_app:db".
# For now, this is more of a conceptual placeholder for ID generation strategy.
# DEFAULT_APP_INSTANCE_ID_PREFIX: Final[str] = "app"


# --- Timeout and Limit Constants ---

# Default timeout for plugin operations (setup, teardown, etc.)
DEFAULT_PLUGIN_TIMEOUT_SECONDS: Final[float] = 60.0

# Default timeout for event processing
DEFAULT_EVENT_TIMEOUT_SECONDS: Final[float] = 30.0

# Default timeout for service calls
DEFAULT_SERVICE_TIMEOUT_SECONDS: Final[float] = 30.0

# Maximum allowed length for plugin names
MAX_PLUGIN_NAME_LENGTH: Final[int] = 100

# Maximum allowed length for service names
MAX_SERVICE_NAME_LENGTH: Final[int] = 100

# Maximum allowed length for event names
MAX_EVENT_NAME_LENGTH: Final[int] = 200

# Reserved plugin names that cannot be used by user plugins
RESERVED_PLUGIN_NAMES: Final[tuple[str, ...]] = (
    "system",
    "kernel",
    "framework",
    "plugginger",
    "app",
    "core",
    "internal",
)
