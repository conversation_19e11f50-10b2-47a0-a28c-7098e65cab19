"""
Deprecation utilities for managing API transitions.

This module provides decorators and utilities for marking APIs as deprecated
and managing the transition to new APIs while maintaining backward compatibility.
"""

from __future__ import annotations

import functools
import warnings
from collections.abc import Callable
from typing import Any, TypeVar, cast

F = TypeVar("F", bound=Callable[..., Any])


def deprecated(
    message: str,
    removal_version: str | None = None,
    replacement: str | None = None,
    category: type[Warning] = DeprecationWarning,
) -> Callable[[F], F]:
    """
    Mark a function, method, or class as deprecated.

    This decorator issues a warning when the decorated item is used,
    helping users migrate to newer APIs while maintaining backward compatibility.

    Args:
        message: Deprecation message explaining what is deprecated
        removal_version: Version when the deprecated item will be removed
        replacement: Suggested replacement for the deprecated item
        category: Warning category to use (default: DeprecationWarning)

    Returns:
        Decorator function that adds deprecation warnings

    Example:
        ```python
        @deprecated(
            "get_user_by_id is deprecated, use get_user instead",
            removal_version="v2.0.0",
            replacement="get_user"
        )
        async def get_user_by_id(self, user_id: int) -> dict:
            return await self.get_user(user_id)
        ```
    """

    def decorator(func: F) -> F:
        # Build complete warning message
        warning_parts = [message]

        if replacement:
            warning_parts.append(f"Use {replacement} instead.")

        if removal_version:
            warning_parts.append(f"Will be removed in {removal_version}.")

        warning_message = " ".join(warning_parts)

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            warnings.warn(
                warning_message,
                category=category,
                stacklevel=2
            )
            return func(*args, **kwargs)

        # Add deprecation metadata to the function
        wrapper.__deprecated__ = True  # type: ignore[attr-defined]
        wrapper.__deprecation_message__ = warning_message  # type: ignore[attr-defined]
        wrapper.__removal_version__ = removal_version  # type: ignore[attr-defined]
        wrapper.__replacement__ = replacement  # type: ignore[attr-defined]

        return cast(F, wrapper)

    return decorator


def deprecated_parameter(
    parameter_name: str,
    message: str | None = None,
    removal_version: str | None = None,
    replacement: str | None = None,
) -> Callable[[F], F]:
    """
    Mark a specific parameter as deprecated.

    This decorator checks if a deprecated parameter is used and issues
    a warning, while still allowing the function to work normally.

    Args:
        parameter_name: Name of the deprecated parameter
        message: Custom deprecation message
        removal_version: Version when the parameter will be removed
        replacement: Suggested replacement parameter

    Returns:
        Decorator function that warns about deprecated parameters

    Example:
        ```python
        @deprecated_parameter(
            "old_param",
            replacement="new_param",
            removal_version="v2.0.0"
        )
        def my_function(new_param: str, old_param: str | None = None) -> str:
            if old_param is not None:
                new_param = old_param  # Handle backward compatibility
            return new_param
        ```
    """

    def decorator(func: F) -> F:
        # Build warning message
        if message:
            warning_message = message
        else:
            warning_message = f"Parameter '{parameter_name}' is deprecated"

        if replacement:
            warning_message += f", use '{replacement}' instead"

        if removal_version:
            warning_message += f". Will be removed in {removal_version}"

        warning_message += "."

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Check if deprecated parameter is used
            if parameter_name in kwargs:
                warnings.warn(
                    warning_message,
                    DeprecationWarning,
                    stacklevel=2
                )

            return func(*args, **kwargs)

        return cast(F, wrapper)

    return decorator


def is_deprecated(obj: Any) -> bool:
    """
    Check if an object is marked as deprecated.

    Args:
        obj: Object to check for deprecation

    Returns:
        True if the object is deprecated, False otherwise

    Example:
        ```python
        if is_deprecated(some_function):
            print("This function is deprecated!")
        ```
    """
    return getattr(obj, "__deprecated__", False)


def get_deprecation_info(obj: Any) -> dict[str, Any] | None:
    """
    Get deprecation information for an object.

    Args:
        obj: Object to get deprecation info for

    Returns:
        Dictionary with deprecation info, or None if not deprecated

    Example:
        ```python
        info = get_deprecation_info(deprecated_function)
        if info:
            print(f"Deprecated: {info['message']}")
            print(f"Removal: {info['removal_version']}")
            print(f"Replacement: {info['replacement']}")
        ```
    """
    if not is_deprecated(obj):
        return None

    return {
        "message": getattr(obj, "__deprecation_message__", None),
        "removal_version": getattr(obj, "__removal_version__", None),
        "replacement": getattr(obj, "__replacement__", None),
    }


class DeprecatedMixin:
    """
    Mixin class for marking entire classes as deprecated.

    This mixin can be added to classes to automatically issue deprecation
    warnings when the class is instantiated.

    Example:
        ```python
        class OldPlugin(DeprecatedMixin, PluginBase):
            __deprecation_message__ = "OldPlugin is deprecated, use NewPlugin instead"
            __removal_version__ = "v2.0.0"
            __replacement__ = "NewPlugin"
        ```
    """

    def __init_subclass__(cls, **kwargs: Any) -> None:
        """Issue deprecation warning when subclass is created."""
        super().__init_subclass__(**kwargs)

        # Check if this class (not a parent) is marked as deprecated
        if hasattr(cls, "__deprecation_message__"):
            original_init = cls.__init__

            @functools.wraps(original_init)
            def deprecated_init(self: Any, *args: Any, **kwargs: Any) -> None:
                message = getattr(cls, "__deprecation_message__", f"{cls.__name__} is deprecated")
                removal_version = getattr(cls, "__removal_version__", None)
                replacement = getattr(cls, "__replacement__", None)

                warning_parts = [message]
                if replacement:
                    warning_parts.append(f"Use {replacement} instead.")
                if removal_version:
                    warning_parts.append(f"Will be removed in {removal_version}.")

                warnings.warn(
                    " ".join(warning_parts),
                    DeprecationWarning,
                    stacklevel=2
                )

                original_init(self, *args, **kwargs)

            # Use type() to avoid mypy method assignment error
            type.__setattr__(cls, "__init__", deprecated_init)


def warn_experimental(module_name: str) -> None:
    """
    Issue a warning for experimental module usage.

    This function is used by experimental modules to warn users
    that the APIs are unstable and may change without notice.

    Args:
        module_name: Name of the experimental module

    Example:
        ```python
        # In plugginger/experimental/some_module.py
        warn_experimental("plugginger.experimental.some_module")
        ```
    """
    warnings.warn(
        f"{module_name} contains experimental APIs that may change or be removed "
        f"without notice. Use at your own risk and avoid in production code.",
        FutureWarning,
        stacklevel=3  # Skip this function and the module import
    )


# Export all deprecation utilities
__all__ = [
    "deprecated",
    "deprecated_parameter",
    "is_deprecated",
    "get_deprecation_info",
    "DeprecatedMixin",
    "warn_experimental",
]
