# src/plugginger/core/exceptions.py

"""
Core exception hierarchy with zero external dependencies.

All Plugginger-specific exceptions inherit from `PluggingerError`.
This allows users to catch all framework-related errors with a single
`except PluggingerError:` block if desired, or to catch more specific
errors for finer-grained error handling. Each exception aims to provide
clear context about the nature of the error.
"""

from __future__ import annotations  # Ensures all type hints are treated as forward references

import traceback
from collections.abc import Sequence  # Standard typing imports
from datetime import UTC, datetime
from enum import Enum
from typing import Any


class ErrorCategory(Enum):
    """
    Categorizes errors for better debugging and AI-Agent analysis.

    This classification helps distinguish between different types of errors:
    - USER_INPUT: Configuration or usage errors by the developer
    - PLUGIN_ERROR: Issues within plugin code or plugin-specific problems
    - FRAMEWORK_BUG: Internal framework bugs that should be reported
    - DEPENDENCY_ERROR: Missing dependencies or version conflicts
    - CONFIGURATION: Application configuration problems
    - RUNTIME_ERROR: Runtime issues during normal operation
    """
    USER_INPUT = "user_input"
    PLUGIN_ERROR = "plugin_error"
    FRAMEWORK_BUG = "framework_bug"
    DEPENDENCY_ERROR = "dependency"
    CONFIGURATION = "configuration"
    RUNTIME_ERROR = "runtime"


class PluggingerError(Exception):
    """
    Base class for all custom exceptions raised by the Plugginger framework.

    This enhanced version provides structured error information for better debugging
    and AI-Agent analysis. All framework errors include categorization, context,
    and actionable suggestions when possible.
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        error_code: str | None = None,
        category: ErrorCategory | None = None,
        context: dict[str, Any] | None = None,
        suggestion: str | None = None,
        related_docs: str | None = None
    ) -> None:
        """
        Initializes a new PluggingerError with enhanced context.

        Args:
            message: A human-readable description of the error.
            error_code: Unique error code for programmatic handling (e.g., "PLUGIN_LOAD_FAILED").
            category: Error category for classification and analysis.
            context: Additional context information (plugin names, config values, etc.).
            suggestion: Actionable suggestion for resolving the error.
            related_docs: URL or reference to relevant documentation.
            *args: Additional arguments to pass to the base Exception class.
        """
        super().__init__(message, *args)
        self.error_code = error_code or "GENERIC_ERROR"
        self.category = category or ErrorCategory.RUNTIME_ERROR
        self.context = context or {}
        self.suggestion = suggestion
        self.related_docs = related_docs
        self.timestamp = datetime.now(UTC)

    def to_dict(self) -> dict[str, Any]:
        """
        Convert exception to structured format for AI-Agent analysis.

        Returns:
            Dictionary containing all error information in a structured format.
        """
        return {
            "error_code": self.error_code,
            "category": self.category.value,
            "message": str(self),
            "context": self.context,
            "suggestion": self.suggestion,
            "related_docs": self.related_docs,
            "timestamp": self.timestamp.isoformat(),
            "stack_trace": traceback.format_exc() if self.__traceback__ else None
        }


# --- Configuration & Registration Errors ---
class ConfigurationError(PluggingerError):
    """
    Raised when there's an error in the application or plugin configuration.

    This often occurs during the application build phase if `GlobalAppConfig`
    or a plugin-specific `config_schema` (Pydantic model) fails validation,
    or if configuration data is malformed or missing.

    Attributes:
        validation_errors (Optional[Any]): Detailed validation errors, typically
            from Pydantic's `ValidationError.errors()` if the original error
            was a Pydantic `ValidationError`. This provides structured information
            about which configuration fields failed validation and why.
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        validation_errors: Any | None = None,
        plugin_name: str | None = None,
        config_key: str | None = None,
        **kwargs: Any
    ) -> None:
        """
        Initializes a ConfigurationError with enhanced context.

        Args:
            message: A summary of the configuration error.
            validation_errors: Optional structured validation error details (e.g., from Pydantic).
            plugin_name: Name of the plugin with configuration issues.
            config_key: Specific configuration key that failed.
            *args: Additional arguments for the base exception.
            **kwargs: Additional context for the base PluggingerError.
        """
        # Build context from validation errors and plugin info
        context = kwargs.get('context', {})
        if plugin_name:
            context['plugin_name'] = plugin_name
        if config_key:
            context['config_key'] = config_key
        if validation_errors:
            context['validation_errors'] = validation_errors

        # Set defaults for enhanced error handling
        kwargs.setdefault('error_code', 'CONFIGURATION_ERROR')
        kwargs.setdefault('category', ErrorCategory.CONFIGURATION)
        kwargs.setdefault('context', context)
        kwargs.setdefault('suggestion', 'Check configuration format and required fields')

        super().__init__(message, *args, **kwargs)
        self.validation_errors = validation_errors


class PluginRegistrationError(PluggingerError):
    """
    Raised for general errors encountered during plugin registration by the
    `PluggingerAppBuilder`.

    Examples include:
    - Attempting to register a plugin with a name that is already in use.
    - Providing a class for registration that is not a valid plugin type
      (e.g., does not inherit from `PluginBase` or is not decorated with `@plugin`).
    - Invalid metadata (name, version) provided in the `@plugin` decorator.
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        plugin_name: str | None = None,
        plugin_class: str | None = None,
        **kwargs: Any
    ) -> None:
        """
        Initializes a PluginRegistrationError with enhanced context.

        Args:
            message: A summary of the registration error.
            plugin_name: Name of the plugin that failed to register.
            plugin_class: Class name of the plugin that failed.
            *args: Additional arguments for the base exception.
            **kwargs: Additional context for the base PluggingerError.
        """
        # Build context from plugin info
        context = kwargs.get('context', {})
        if plugin_name:
            context['plugin_name'] = plugin_name
        if plugin_class:
            context['plugin_class'] = plugin_class

        # Set defaults for enhanced error handling
        kwargs.setdefault('error_code', 'PLUGIN_REGISTRATION_FAILED')
        kwargs.setdefault('category', ErrorCategory.PLUGIN_ERROR)
        kwargs.setdefault('context', context)
        kwargs.setdefault('suggestion', 'Ensure plugin inherits from PluginBase and has @plugin decorator')

        super().__init__(message, *args, **kwargs)


# --- Dependency Handling Errors ---
class DependencyError(PluggingerError):
    """
    Base class for errors related to plugin dependencies encountered during
    the application build phase by `PluggingerAppBuilder`.
    """

    def __init__(self, message: str, *args: Any, **kwargs: Any) -> None:
        """Initialize DependencyError with enhanced context."""
        kwargs.setdefault('category', ErrorCategory.DEPENDENCY_ERROR)
        super().__init__(message, *args, **kwargs)


class MissingDependencyError(DependencyError):
    """
    Raised by `PluggingerAppBuilder` when a plugin declared in its `needs`
    attribute (via a `Depends` object) cannot be found among the set of
    registered plugins in the current application instance being built.
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        plugin_name: str | None = None,
        missing_dependencies: list[str] | None = None,
        **kwargs: Any
    ) -> None:
        """
        Initializes a MissingDependencyError with enhanced context.

        Args:
            message: A summary of the missing dependency error.
            plugin_name: Name of the plugin with missing dependencies.
            missing_dependencies: List of missing dependency names.
            *args: Additional arguments for the base exception.
            **kwargs: Additional context for the base PluggingerError.
        """
        # Build context from dependency info
        context = kwargs.get('context', {})
        if plugin_name:
            context['plugin_name'] = plugin_name
        if missing_dependencies:
            context['missing_dependencies'] = missing_dependencies

        # Set defaults for enhanced error handling
        kwargs.setdefault('error_code', 'MISSING_DEPENDENCY')
        kwargs.setdefault('context', context)
        if missing_dependencies:
            kwargs.setdefault('suggestion', f'Register these missing dependencies: {", ".join(missing_dependencies)}')

        super().__init__(message, *args, **kwargs)


class CircularDependencyError(DependencyError):
    """
    Raised by `PluggingerAppBuilder` if a circular dependency (a cycle)
    is detected in the plugin dependency graph during the build phase,
    making a valid initialization order impossible.
    """


class DependencyVersionConflictError(DependencyError):
    """
    Raised by `PluggingerAppBuilder` if the declared version of a depended-upon
    plugin does not satisfy the version constraint (e.g., `">=1.0,<2.0"`)
    specified in the `Depends` object of the dependent plugin.
    """


class MissingTypeAnnotationForDIError(DependencyError):
    """
    Raised by the DI container when attempting to inject dependencies into a constructor
    parameter that lacks a type annotation and has no default value.

    The DI container requires explicit type annotations to resolve dependencies.
    Parameters without type annotations must have default values to be optional.

    Attributes:
        class_name (str): The name of the class being instantiated
        parameter_name (str): The name of the parameter missing type annotation
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        class_name: str | None = None,
        parameter_name: str | None = None,
    ) -> None:
        """
        Initializes a MissingTypeAnnotationForDIError.

        Args:
            message: A summary of the missing type annotation error
            class_name: The name of the class being instantiated
            parameter_name: The name of the parameter missing type annotation
            *args: Additional arguments for the base exception
        """
        super().__init__(message, *args)
        self.class_name = class_name
        self.parameter_name = parameter_name


class DIContainerError(DependencyError):
    """
    Base class for errors specific to the DI container's operation.

    This includes errors during dependency resolution, instance creation,
    and other container-specific operations.
    """


class DependencyResolutionError(DIContainerError):
    """
    Raised when the DI container cannot resolve a dependency during instance creation.

    This typically occurs when a required dependency type is not registered in the
    container and the constructor parameter has no default value.

    Attributes:
        target_class (str): The class being instantiated
        dependency_type (str): The type that could not be resolved
        parameter_name (str): The constructor parameter requiring the dependency
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        target_class: str | None = None,
        dependency_type: str | None = None,
        parameter_name: str | None = None,
    ) -> None:
        """
        Initializes a DependencyResolutionError.

        Args:
            message: A summary of the dependency resolution error
            target_class: The class being instantiated
            dependency_type: The type that could not be resolved
            parameter_name: The constructor parameter requiring the dependency
            *args: Additional arguments for the base exception
        """
        super().__init__(message, *args)
        self.target_class = target_class
        self.dependency_type = dependency_type
        self.parameter_name = parameter_name


# --- Service Layer Errors ---
class ServiceDefinitionError(PluggingerError):
    """
    Raised for errors in the definition or registration of a service method.
    Examples:
    - A non-asynchronous (`def` instead of `async def`) method decorated with `@service`.
    - An invalid custom service name provided to `@service(name=...)`.
    """


class ServiceNameConflictError(ServiceDefinitionError):
    """
    Raised by `PluggingerAppBuilder` (during plugin processing) or by the
    internal service dispatcher if an attempt is made to register a service
    with a fully qualified name that is already in use within the same
    `PluggingerAppInstance`.
    """


class ServiceNotFoundError(PluggingerError):
    """
    Raised at runtime by `PluggingerAppInstance.call_service()` or through
    an injected plugin proxy if an attempt is made to call a service name
    that is not registered or could not be found.
    """


class ServiceExecutionError(PluggingerError):
    """
    Raised at runtime when a successfully called service method throws an
    unhandled exception during its execution. The original exception that
    caused this error is chained and accessible via the `__cause__` attribute.
    """


# --- Event System Errors ---
class EventDefinitionError(PluggingerError):
    """
    Raised for errors in the definition or registration of an event listener method.
    Examples:
    - An invalid event pattern string or type provided to `@on_event`.
    - A non-asynchronous method decorated with `@on_event`.
    - An event listener method with an incorrect signature (e.g., wrong number
      of parameters).
    """


class EventListenerError(PluggingerError):
    """Base class for errors occurring during an event listener's execution at runtime."""


class EventListenerUnhandledError(EventListenerError):
    """
    Raised specifically when an event listener fails (throws an unhandled exception)
    and the configured `EventListenerFaultPolicy` for the application is set to `FAIL_FAST`.
    The original exception that caused this error is chained.
    """


class EventListenerTimeoutError(EventListenerError):
    """
    Raised specifically when an event listener times out during execution
    and the configured `EventListenerFaultPolicy` for the application is set to `FAIL_FAST`.
    This is a specialized case of `EventListenerUnhandledError` for timeout scenarios.
    """


class EventPayloadValidationError(EventDefinitionError):
    """
    Raised when event payload data fails Pydantic validation for a typed event listener.

    This occurs when:
    - An event listener expects a specific Pydantic model as its event_data parameter
    - The emitted event data cannot be successfully parsed into that model
    - Validation errors occur during the automatic conversion process

    The original Pydantic ValidationError is chained and accessible via `__cause__`.

    Attributes:
        event_type (str): The type of event that failed validation
        listener_name (str): The qualified name of the listener that expected the model
        validation_errors (Any): Detailed validation errors from Pydantic
    """

    def __init__(
        self,
        message: str,
        *args: Any,
        event_type: str | None = None,
        listener_name: str | None = None,
        validation_errors: Any | None = None,
    ) -> None:
        """
        Initializes an EventPayloadValidationError.

        Args:
            message: A summary of the validation error
            event_type: The type of event that failed validation
            listener_name: The qualified name of the failing listener
            validation_errors: Detailed validation error information from Pydantic
            *args: Additional arguments for the base exception
        """
        super().__init__(message, *args)
        self.event_type = event_type
        self.listener_name = listener_name
        self.validation_errors = validation_errors


# --- Lifecycle & Concurrency Errors ---
class PluginTeardownError(PluggingerError):
    """
    Raised by `PluggingerAppInstance.stop_all_plugins()` if one or more plugins
    fail (throw an exception) during their `teardown()` phase.
    This exception may aggregate multiple underlying errors from different plugins.

    Attributes:
        individual_errors (Sequence[BaseException]): A sequence of the individual
            exceptions (or wrapped exceptions with context) caught from
            failing `teardown()` methods.
    """

    def __init__(
        self, message: str, *args: Any, individual_errors: Sequence[BaseException] | None = None
    ) -> None:
        """
        Initializes a PluginTeardownError.

        Args:
            message: A summary message for the teardown failure.
            individual_errors: A sequence of exceptions from individual plugin teardowns.
            *args: Additional arguments for the base exception.
        """
        super().__init__(message, *args)
        self.individual_errors: Sequence[BaseException] = individual_errors or []


class BackgroundTaskError(PluggingerError):
    """
    Raised when an unhandled exception occurs within a synchronous function
    that was decorated with `@background_task` and executed in a background thread.
    This error is typically raised when the `Awaitable` returned by the
    `@background_task` wrapper is awaited and the underlying synchronous
    function failed. The original exception is chained.
    """


class BackgroundTaskQueueError(BackgroundTaskError):  # Inherits from BackgroundTaskError
    """
    Raised by the `@background_task` decorator if the underlying executor's
    task submission queue is conceptually full and a new task cannot be
    immediately accepted.
    Note: Standard `ThreadPoolExecutor.submit` might block or have internal
    queuing instead of raising this directly. This error implies that either
    a custom executor wrapper with queue limits is in place, or a non-blocking
    submission attempt failed due to capacity.
    """


# --- Build / Freeze Process Errors (CLI Tooling) ---
class LockfileError(PluggingerError):
    """
    Raised for general errors related to reading, writing, or processing
    the `plugginger.lock.json` file by the `plugginger core freeze` command
    or other lockfile-consuming logic within the framework or tools.
    """


class FreezeConflictError(LockfileError):
    """
    Raised by the `plugginger core freeze` command if it cannot find a
    conflict-free resolution for all Python package dependencies required
    by the set of included plugins (e.g., Plugin A needs `requests==2.0`
    and Plugin B needs `requests==3.0`).
    """


# --- Validation Errors ---
class ValidationError(PluggingerError):
    """
    Raised when input validation fails.

    This error is used for general validation failures such as invalid
    configuration values, malformed input data, or constraint violations.
    """


# --- App-Plugin (Fractal Composition) Errors ---
class AppPluginError(PluggingerError):
    """
    Base class for errors specifically related to `AppPluginBase` instances
    and the fractal composition mechanism. This can include errors during
    the initialization or lifecycle management of a sub-app, issues with
    event bridging, or problems with inter-app communication.
    """
