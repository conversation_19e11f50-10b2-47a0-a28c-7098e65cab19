# src/plugginger/core/config.py

"""
Core configuration types and enums with zero external dependencies.

This module contains fundamental configuration definitions that are used
throughout the Plugginger framework without causing circular imports.
"""

from __future__ import annotations

from enum import Enum
from typing import Literal


class EventListenerFaultPolicy(str, Enum):
    """
    Defines strategies for handling errors that occur within event listeners
    during event dispatch.
    """

    LOG_AND_CONTINUE = "log"
    """Log the error and continue processing other listeners for the same event,
    as well as subsequent events. This is often suitable for production to ensure
    one failing listener doesn't halt all event processing."""

    FAIL_FAST = "fail"
    """Log the error and immediately propagate it (typically by raising
    `EventListenerUnhandledError`). This usually stops the current event processing
    and potentially the application, making it suitable for development and testing
    to catch errors quickly."""

    ISOLATE_AND_LOG = "isolate"
    """Log the error and prevent the specific failing listener from being called
    again for the duration of the application's runtime (for any event it was
    subscribed to, or just the specific failing event pattern). This helps contain
    faulty listeners without stopping others."""


class LogLevel(str, Enum):
    """Log level enumeration."""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# Type alias for log levels (for backward compatibility)
LogLevelType = Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

# Default configuration values
DEFAULT_LOG_LEVEL: LogLevel = LogLevel.INFO
DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS: float = 5.0
DEFAULT_EVENT_LISTENER_FAULT_POLICY = EventListenerFaultPolicy.FAIL_FAST
DEFAULT_MAX_FRACTAL_DEPTH: int = 10
DEFAULT_APP_NAME: str = "PluggingerApp"

# Validation constraints
MIN_EVENT_LISTENER_TIMEOUT: float = 0.1
MAX_EVENT_LISTENER_TIMEOUT: float = 300.0
MIN_FRACTAL_DEPTH: int = 1
MAX_FRACTAL_DEPTH: int = 100
MIN_APP_NAME_LENGTH: int = 1
MAX_APP_NAME_LENGTH: int = 100

# Event system configuration
DEFAULT_MAX_CONCURRENT_EVENTS: int = 100
DEFAULT_EVENT_CLEANUP_INTERVAL: float = 1.0
DEFAULT_EVENT_TASK_TIMEOUT: float = 30.0
