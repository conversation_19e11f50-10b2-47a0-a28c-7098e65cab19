# src/plugginger/api/depends.py

"""
Dependency injection utilities using the DI container.

This module provides the Depends class and utilities for dependency injection
in plugin methods, without circular import dependencies.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, TypeVar

from plugginger.core.exceptions import DependencyError

if TYPE_CHECKING:
    pass

T = TypeVar("T")


class Depends:
    """
    Dependency injection marker for plugin dependencies.

    This is a pure data class that marks plugin dependencies. The actual
    dependency resolution is handled by the PluggingerAppBuilder through
    GenericPluginProxy instances.

    Examples:
        ```python
        @plugin(name="user_service", version="1.0.0")
        class UserServicePlugin(PluginBase):
            needs: List[Depends] = [
                Depends("database"),
                Depends("auth", version_constraint=">=1.0.0")
            ]
        ```
    """

    def __init__(
        self,
        dependency: str,
        *,
        optional: bool = False,
        version_constraint: str | None = None,
    ) -> None:
        """
        Initialize a dependency injection marker.

        Args:
            dependency: The plugin name to depend on (e.g., "database", "auth")
            optional: Whether the dependency is optional
            version_constraint: Version constraint for plugin dependencies (e.g., ">=1.0,<2.0")
        """
        if not isinstance(dependency, str):
            raise DependencyError(
                f"Dependency must be a string plugin name, got {type(dependency)}"
            )

        self.dependency = dependency
        self.optional = optional
        self.version_constraint = version_constraint

    @property
    def plugin_identifier(self) -> str:
        """
        Get the plugin identifier for dependency resolution.

        Returns:
            Plugin identifier string
        """
        return self.dependency



    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"Depends({self.dependency!r}, optional={self.optional})"
