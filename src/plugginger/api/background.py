# src/plugginger/api/background.py

"""
Background task decorator for running synchronous functions in thread pools.

This module provides the @background_task decorator to offload CPU-bound or
blocking I/O operations to a managed thread pool executor, preventing them
from blocking the main asyncio event loop.
"""

from __future__ import annotations

import asyncio
import inspect
from collections.abc import Awaitable, Callable
from functools import wraps
from typing import Any, Protocol, TypeVar, cast, overload

from plugginger.core.exceptions import BackgroundTaskError

# Type variables for generic function signatures
F = TypeVar("F", bound=Callable[..., Any])
R = TypeVar("R")

# pylint: disable=missing-class-docstring,too-few-public-methods
class BackgroundTaskCallable(Protocol):
    _background_task_executor: str
    _background_task_original: Callable[..., Any]
    _is_background_task: bool

    def __call__(self, *args: Any, **kwargs: Any) -> Awaitable[Any]: ...

DEFAULT_EXECUTOR_NAME = "default"


@overload
def background_task(func: F) -> Callable[..., Awaitable[Any]]:
    """Direct decoration: @background_task"""
    pass  # pylint: disable=unnecessary-pass


@overload
def background_task(
    *, executor: str = DEFAULT_EXECUTOR_NAME
) -> Callable[[F], Callable[..., Awaitable[Any]]]:
    """Factory decoration: @background_task(executor="custom")"""
    pass  # pylint: disable=unnecessary-pass


def background_task(
    func: F | None = None, *, executor: str = DEFAULT_EXECUTOR_NAME
) -> Callable[..., Awaitable[Any]] | Callable[[F], Callable[..., Awaitable[Any]]]:
    """
    Decorator to run synchronous functions in a background thread pool.

    This decorator converts a synchronous function into an async function that
    executes the original function in a thread pool executor managed by the
    PluggingerAppInstance.

    Args:
        func: The function to decorate (for direct decoration)
        executor: Name of the executor to use (default: "default")

    Returns:
        An async wrapper function that executes the original function in a thread pool

    Raises:
        TypeError: If the decorated function is already async
        BackgroundTaskError: If execution fails

    Example:
        ```python
        @plugin(name="worker")
        class WorkerPlugin(PluginBase):
            @service
            @background_task
            def cpu_intensive_work(self, data: str) -> str:
                # This runs in a thread pool
                return process_data(data)

            @service
            @background_task(executor="io_pool")
            def blocking_io(self, url: str) -> bytes:
                # This runs in a custom executor
                return urllib.request.urlopen(url).read()
        ```
    """

    def decorator(target_func: F) -> Callable[..., Awaitable[Any]]:
        # Validate that the function is synchronous
        if inspect.iscoroutinefunction(target_func):
            raise TypeError(
                f"@background_task can only be applied to synchronous functions, "
                f"but '{target_func.__name__}' is async"
            )

        @wraps(target_func)
        async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
            """Async wrapper that executes the function in a thread pool."""
            try:
                # Get the current event loop
                loop = asyncio.get_running_loop()

                # Execute the function in the specified thread pool executor
                # Note: In a full implementation, this would use the app's executor registry
                result = await loop.run_in_executor(
                    None,  # Use default executor for now
                    lambda: target_func(*args, **kwargs),
                )
                return result

            except Exception as e:
                raise BackgroundTaskError(
                    f"Background task '{target_func.__name__}' failed: {e}"
                ) from e

        # Store metadata for the app to use
        # Cast to a Protocol that includes the dynamically added attributes
        wrapped_func = cast(BackgroundTaskCallable, async_wrapper)
        wrapped_func._background_task_executor = executor  # pylint: disable=protected-access
        wrapped_func._background_task_original = target_func  # pylint: disable=protected-access
        wrapped_func._is_background_task = True  # pylint: disable=protected-access

        return wrapped_func

    # Handle both direct decoration and factory decoration
    if func is None:
        # Factory usage: @background_task(executor="custom")
        return decorator
    # Direct usage: @background_task
    return decorator(func)


def is_background_task(func: Callable[..., Any]) -> bool:
    """Check if a function is decorated with @background_task."""
    return getattr(func, "_is_background_task", False)


def get_background_task_executor(func: Callable[..., Any]) -> str:
    """Get the executor name for a background task function."""
    return getattr(func, "_background_task_executor", DEFAULT_EXECUTOR_NAME)


def get_background_task_original(func: Callable[..., Any]) -> Callable[..., Any]:
    """Get the original synchronous function from a background task wrapper."""
    return getattr(func, "_background_task_original", func)
