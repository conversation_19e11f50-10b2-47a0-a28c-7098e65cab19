# src/plugginger/api/app.py

"""
Application instance API using dependency injection.

This module provides the PluggingerAppInstance class that serves as the main
interface for interacting with the plugin framework at runtime with comprehensive
lifecycle management and fractal composition support.
"""


import asyncio
import logging
from collections.abc import Callable
from typing import TYPE_CHECKING, Any

from plugginger.config.models import GlobalAppConfig
from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import (
    BackgroundTaskError,
)

# Schema imports moved to function level to avoid circular imports

if TYPE_CHECKING:
    from plugginger._internal.runtime_facade import RuntimeFacade
    from plugginger.api.app_plugin import AppPluginBase
    from plugginger.api.plugin import PluginBase


class PluggingerAppInstance:
    """
    Main application instance for the Plugginger framework.

    This class provides the runtime interface for interacting with plugins,
    services, events, and background tasks with comprehensive lifecycle
    management and fractal composition support.

    Attributes:
        _app_name: Name of the application
        _runtime_facade: Runtime facade for all framework operations
        _global_config: Global application configuration
        _logger: Logger instance for application logging
        _parent_app_plugin_context: Parent AppPlugin for fractal composition
        _current_build_depth_for_sub_apps: Current nesting depth
        _max_build_depth_for_sub_apps: Maximum allowed nesting depth
    """

    def __init__(
        self,
        app_name: str = DEFAULT_APP_NAME,
        runtime_facade: "RuntimeFacade | None" = None,
        global_config: GlobalAppConfig | None = None,
        parent_app_plugin_context: "AppPluginBase | None" = None,
        _builder_fractal_depth: int = 0,
        _registered_plugin_classes: dict[str, type] | None = None,
    ) -> None:
        """
        Initialize the application instance.

        Args:
            app_name: Name of the application
            runtime_facade: Runtime facade for framework operations
            global_config: Global application configuration
            parent_app_plugin_context: Parent AppPlugin for fractal composition
            _builder_fractal_depth: Current nesting depth (internal use)
            _registered_plugin_classes: Plugin classes registered with builder (internal use)
        """
        self._app_name = app_name
        self._runtime_facade = runtime_facade
        self._global_config = global_config or GlobalAppConfig(app_name=app_name)
        self._parent_app_plugin_context = parent_app_plugin_context
        self._registered_plugin_classes = _registered_plugin_classes or {}

        # Store current app depth and configure sub-app depth parameters
        self._current_app_depth = _builder_fractal_depth
        self._current_build_depth_for_sub_apps = _builder_fractal_depth
        self._max_build_depth_for_sub_apps = self._global_config.max_fractal_depth

        # Setup logging
        self._logger = logging.getLogger(f"plugginger.app.{app_name}")
        self._logger.setLevel(self._global_config.log_level.value)

    async def run(self, main_coroutine: Callable[[], Any] | None = None) -> None:
        """
        Run the application with graceful shutdown handling.

        This method starts all plugins, runs the main coroutine (if provided),
        and handles graceful shutdown on KeyboardInterrupt or cancellation.

        Args:
            main_coroutine: Optional main application logic to run

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")

        try:
            # Start all plugins
            await self._runtime_facade.setup_all_plugins()
            self._logger.info(f"Application '{self._app_name}' started successfully")

            if main_coroutine is not None:
                # Run the provided main coroutine
                await main_coroutine()
            else:
                # Wait indefinitely for external shutdown signal
                shutdown_event = asyncio.Event()
                self._logger.info("Application running - waiting for shutdown signal...")
                await shutdown_event.wait()

        except (KeyboardInterrupt, asyncio.CancelledError):
            self._logger.info("Shutdown signal received")
        except Exception as e:
            self._logger.error(f"Application error: {e}")
            raise
        finally:
            # Always attempt graceful shutdown
            await self.stop_all_plugins()

    async def start_all_plugins(self) -> None:
        """
        Start all plugins.

        This method sets up all plugins in dependency order.

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        await self._runtime_facade.setup_all_plugins()

    async def stop_all_plugins(self, timeout: float = 30.0) -> None:
        """
        Stop all plugins with timeout handling.

        This method attempts to gracefully stop all plugins within the specified
        timeout. If the timeout is exceeded, it logs a warning and continues
        with executor shutdown.

        Args:
            timeout: Maximum time to wait for plugin teardown in seconds
        """
        if self._runtime_facade is None:
            self._logger.warning("Cannot stop plugins - runtime facade is missing")
            return

        try:
            self._logger.info("Stopping all plugins...")
            await asyncio.wait_for(
                self._runtime_facade.teardown_all_plugins(),
                timeout=timeout
            )
            self._logger.info("All plugins stopped successfully")
        except TimeoutError:
            self._logger.warning(f"Plugin teardown timed out after {timeout} seconds")
        except Exception as e:
            self._logger.error(f"Error during plugin teardown: {e}")
        finally:
            # Always shutdown runtime components
            if self._runtime_facade is not None:
                await self._runtime_facade.shutdown()

    def get_plugin_instance(self, plugin_instance_id: str) -> "PluginBase | None":
        """
        Get a plugin instance by its ID.

        Args:
            plugin_instance_id: Unique instance ID of the plugin

        Returns:
            Plugin instance if found and plugins are started, None otherwise
        """
        if self._runtime_facade is None:
            return None

        if not self._runtime_facade.get_plugins_were_setup_flag():
            return None

        # Delegate to runtime facade
        return self._runtime_facade.get_plugin_by_id(plugin_instance_id)

    # Service delegation methods
    async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
        """
        Call a registered service.

        Args:
            service_name: Name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            Result from the service call

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        return await self._runtime_facade.call_service(service_name, *args, **kwargs)

    def has_service(self, service_name: str) -> bool:
        """
        Check if a service is registered.

        Args:
            service_name: Name of the service to check

        Returns:
            True if service exists, False otherwise
        """
        if self._runtime_facade is None:
            return False
        return self._runtime_facade.has_service(service_name)

    def list_services(self) -> list[str]:
        """
        Get a list of all registered service names.

        Returns:
            List of registered service names
        """
        if self._runtime_facade is None:
            return []
        return self._runtime_facade.list_services()

    # Event delegation methods
    async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
        """
        Emit an event to all matching listeners.

        Args:
            event_type: Type of event to emit
            event_data: Event payload data

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        await self._runtime_facade.emit_event(event_type, event_data)

    def list_event_patterns(self) -> list[str]:
        """
        Get a list of all registered event patterns.

        Returns:
            List of event patterns that have registered listeners
        """
        if self._runtime_facade is None:
            return []
        return self._runtime_facade.list_event_patterns()

    # Background task management
    def create_managed_task(self, coro: Any, *, name: str | None = None) -> asyncio.Task[Any]:
        """
        Create a managed background task.

        This method creates a background task that will be automatically
        cleaned up during application shutdown.

        Args:
            coro: Coroutine to run as a background task
            name: Optional name for the task

        Returns:
            Created asyncio Task

        Raises:
            BackgroundTaskError: If task creation fails
        """
        try:
            task = asyncio.create_task(coro, name=name)
            # TODO: Add task to managed task registry for cleanup
            return task
        except Exception as e:
            raise BackgroundTaskError(f"Failed to create managed task: {e}") from e

    # Executor delegation methods
    def get_executor(self, name: str | None = None) -> Any:
        """
        Get an executor by name.

        Args:
            name: Name of the executor to get, or None for default

        Returns:
            The requested executor

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        return self._runtime_facade.get_executor(name)

    def register_executor(self, name: str, config_or_executor: Any) -> None:
        """
        Register a new executor.

        Args:
            name: Name of the executor
            config_or_executor: Executor configuration or executor instance

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        self._runtime_facade.register_executor(name, config_or_executor)

    def get_runtime_facade(self) -> "RuntimeFacade":
        """
        Get the runtime facade instance.

        Returns:
            The runtime facade

        Raises:
            RuntimeError: If runtime facade is not configured
        """
        if self._runtime_facade is None:
            raise RuntimeError("Application not properly built - runtime facade is missing")
        return self._runtime_facade

    # Properties
    @property
    def app_name(self) -> str:
        """Get the application name."""
        return self._app_name

    @property
    def global_config(self) -> GlobalAppConfig:
        """Get the global application configuration."""
        return self._global_config

    @property
    def logger(self) -> logging.Logger:
        """Get the application logger."""
        return self._logger

    def export_manifests(
        self,
        output_dir: str = "manifests",
        *,
        app_version: str = "1.0.0",
        app_description: str | None = None,
        author: str | None = None,
        license: str | None = None,
        keywords: list[str] | None = None,
        include_individual_manifests: bool = True,
        include_app_manifest: bool = True,
    ) -> dict[str, str]:
        """
        Export plugin and application manifests to YAML files.

        This method generates manifests for all registered plugins and optionally
        creates an application manifest that describes the complete application.

        Args:
            output_dir: Directory to save manifest files (default: "manifests")
            app_version: Version of the application (default: "1.0.0")
            app_description: Description of the application (optional)
            author: Author of plugins/application (optional)
            license: License for plugins/application (optional)
            keywords: Keywords for plugins/application (optional)
            include_individual_manifests: Whether to generate individual plugin manifests
            include_app_manifest: Whether to generate application manifest

        Returns:
            Dictionary mapping file paths to YAML content

        Raises:
            ValueError: If no plugins are registered or output directory is invalid
        """
        from pathlib import Path

        # Import schema functions here to avoid circular imports
        from plugginger.schemas import (
            generate_app_manifest,
            generate_plugin_manifest,
            manifest_to_yaml,
        )

        if not self._registered_plugin_classes:
            raise ValueError("No plugins registered - cannot export manifests")

        if not output_dir or not isinstance(output_dir, str):
            raise ValueError("output_dir must be a non-empty string")

        # Create output directory if it doesn't exist
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        exported_files: dict[str, str] = {}

        # Export individual plugin manifests
        if include_individual_manifests:
            for registration_name, plugin_class in self._registered_plugin_classes.items():
                try:
                    # Generate plugin manifest
                    manifest = generate_plugin_manifest(
                        plugin_class,
                        author=author,
                        license=license,
                        keywords=keywords,
                    )

                    # Convert to YAML
                    yaml_content = manifest_to_yaml(manifest)

                    # Save to file
                    manifest_filename = f"{registration_name}_manifest.yaml"
                    manifest_path = output_path / manifest_filename

                    with open(manifest_path, "w", encoding="utf-8") as f:
                        f.write(yaml_content)

                    exported_files[str(manifest_path)] = yaml_content
                    self._logger.info(f"Exported manifest for plugin '{registration_name}' to {manifest_path}")

                except Exception as e:
                    self._logger.warning(f"Failed to export manifest for plugin '{registration_name}': {e}")

        # Export application manifest
        if include_app_manifest:
            try:
                # Get all plugin classes as a list
                plugin_classes = list(self._registered_plugin_classes.values())

                # Generate application manifest
                app_manifest = generate_app_manifest(
                    app_name=self._app_name,
                    app_version=app_version,
                    plugin_classes=plugin_classes,
                    description=app_description,
                )

                # Convert to YAML
                app_yaml_content = manifest_to_yaml(app_manifest)

                # Save to file
                app_manifest_filename = f"{self._app_name}_app_manifest.yaml"
                app_manifest_path = output_path / app_manifest_filename

                with open(app_manifest_path, "w", encoding="utf-8") as f:
                    f.write(app_yaml_content)

                exported_files[str(app_manifest_path)] = app_yaml_content
                self._logger.info(f"Exported application manifest to {app_manifest_path}")

            except Exception as e:
                self._logger.warning(f"Failed to export application manifest: {e}")

        self._logger.info(f"Manifest export completed. {len(exported_files)} files exported to {output_path}")
        return exported_files

