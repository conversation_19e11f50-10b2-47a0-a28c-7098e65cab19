# RFC-XXX: [Titel der Änderung]

**Status**: Draft | Under Review | Accepted | Rejected  
**Author**: [Name/GitHub Handle]  
**Created**: [YYYY-MM-DD]  
**Updated**: [YYYY-MM-DD]

## Zusammenfassung

[Ein-Satz-Beschreibung der vorgeschlagenen Änderung]

## Motivation

### Problem
[Welches Problem löst diese Änderung? Warum ist es wichtig?]

### Use Cases
[Konkrete Anwendungsfälle, die von dieser Änderung profitieren]

## Detailliertes Design

### Vorgeschlagene Lösung
[Technische Details der Implementierung]

### API-Änderungen
```python
# Beispiel-Code für neue/geänderte APIs
```

### Auswirkungen auf bestehenden Code
[Welche Breaking Changes? Migration-Pfad?]

## Alternativen

### Alternative 1: [Name]
[Beschreibung und warum sie nicht gewählt wurde]

### Alternative 2: [Name]
[Beschreibung und warum sie nicht gewählt wurde]

## Auswirkungen

### Auf Entwickler
[Wie ändert sich die Developer Experience?]

### Auf Performance
[Performance-Implikationen, falls relevant]

### Auf Wartbarkeit
[Langfristige Wartungskosten]

## Offene Fragen

- [ ] [Frage 1]
- [ ] [Frage 2]

## Implementation Plan

### Phase 1
[Erste Schritte]

### Phase 2
[Weitere Schritte]

### Testing Strategy
[Wie wird die Änderung getestet?]

## Referenzen

- [Link zu verwandten Issues]
- [Link zu Diskussionen]
- [Link zu ähnlichen Implementierungen in anderen Frameworks]
