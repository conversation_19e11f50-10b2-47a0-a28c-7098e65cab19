# Plugin Manifest Schema

**Version**: 1.0.0  
**Status**: ✅ Stable Candidate (v0.9.0-alpha)

Plugin manifests are YAML files that describe plugin metadata, services, event listeners, and dependencies in a machine-readable format. This enables AI agents to understand plugin structure without Python imports.

## Overview

Every plugin can have a `manifest.yaml` file that describes:
- **Metadata**: Name, version, author, description
- **Runtime**: Execution mode, Python/Plugginger version requirements
- **Dependencies**: Required services from other plugins
- **Services**: Methods exposed as callable services
- **Event Listeners**: Methods that handle events
- **Configuration**: JSON Schema for plugin configuration

## Schema Structure

### Basic Example

```yaml
manifest_version: "1.0.0"

metadata:
  name: "user_manager"
  version: "1.2.0"
  description: "User management plugin with authentication"
  author: "<PERSON>"
  license: "MIT"
  keywords: ["user", "auth", "database"]

runtime:
  execution_mode: "thread"
  python_version: ">=3.9"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "database"
    version: ">=1.0.0,<2.0.0"
    optional: false
  - name: "logger"
    optional: false

services:
  - name: "get_user"
    method_name: "get_user_by_id"
    description: "Retrieve user by ID"
    timeout_seconds: 5.0
    signature: "(self, user_id: int) -> dict | None"
    parameters:
      - name: "user_id"
        annotation: "int"
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict | None"

event_listeners:
  - patterns: ["user.created"]
    method_name: "on_user_created"
    description: "Handle new user creation"
    priority: 10
    signature: "(self, event_data: dict) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict"
        kind: "POSITIONAL_OR_KEYWORD"

generated_at: "2025-01-15T10:30:00Z"
generated_by: "plugginger-manifest-generator"
```

## Schema Reference

### Root Level

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `manifest_version` | string | Yes | Schema version (currently "1.0.0") |
| `metadata` | object | Yes | Plugin metadata |
| `runtime` | object | Yes | Runtime configuration |
| `dependencies` | array | No | Plugin dependencies |
| `services` | array | No | Services provided by plugin |
| `event_listeners` | array | No | Event listeners in plugin |
| `config_schema` | object | No | JSON Schema for plugin configuration |
| `generated_at` | datetime | Yes | When manifest was generated |
| `generated_by` | string | Yes | Tool that generated manifest |

### Metadata Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Plugin name (valid Python identifier) |
| `version` | string | Yes | Plugin version (PEP 440 compatible) |
| `description` | string | No | Plugin description |
| `author` | string | No | Plugin author |
| `homepage` | string | No | Plugin homepage URL |
| `repository` | string | No | Plugin repository URL |
| `license` | string | No | Plugin license |
| `keywords` | array[string] | No | Plugin keywords |

### Runtime Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `execution_mode` | enum | No | "thread" (default), "process", "external" |
| `python_version` | string | No | Required Python version (PEP 440) |
| `plugginger_version` | string | Yes | Required Plugginger version |

### Dependency Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Dependency name |
| `version` | string | No | Version constraint (SemVer) |
| `optional` | boolean | No | Whether dependency is optional (default: false) |
| `description` | string | No | Dependency description |

### Service Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Service name |
| `method_name` | string | Yes | Python method name |
| `description` | string | No | Service description |
| `timeout_seconds` | number | No | Service timeout |
| `signature` | string | Yes | Full method signature |
| `parameters` | array | Yes | Method parameters (excluding self) |
| `return_annotation` | string | No | Return type annotation |

### Event Listener Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `patterns` | array[string] | Yes | Event patterns to listen for |
| `method_name` | string | Yes | Python method name |
| `description` | string | No | Event listener description |
| `timeout_seconds` | number | No | Event listener timeout |
| `priority` | integer | No | Execution priority (default: 0) |
| `signature` | string | Yes | Full method signature |
| `parameters` | array | Yes | Method parameters (excluding self) |

### Parameter Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Parameter name |
| `annotation` | string | No | Type annotation as string |
| `default` | any | No | Default value if any |
| `kind` | enum | Yes | Parameter kind (see below) |

#### Parameter Kinds

- `POSITIONAL_ONLY`: Can only be passed positionally
- `POSITIONAL_OR_KEYWORD`: Can be passed positionally or as keyword
- `VAR_POSITIONAL`: *args parameter
- `KEYWORD_ONLY`: Can only be passed as keyword
- `VAR_KEYWORD`: **kwargs parameter

## Generation

### Automatic Generation

```python
from plugginger.schemas import generate_plugin_manifest, manifest_to_yaml

# Generate from plugin class
manifest = generate_plugin_manifest(
    MyPlugin,
    author="John Doe",
    license="MIT",
    keywords=["database", "user"],
)

# Convert to YAML
yaml_content = manifest_to_yaml(manifest)

# Save to file
with open("manifest.yaml", "w") as f:
    f.write(yaml_content)
```

### Manual Creation

```python
from plugginger.schemas import PluginManifest, PluginMetadata, PluginRuntime

manifest = PluginManifest(
    metadata=PluginMetadata(
        name="my_plugin",
        version="1.0.0",
        author="John Doe",
    ),
    runtime=PluginRuntime(
        plugginger_version=">=0.9.0",
    ),
)
```

## Validation

### Loading from YAML

```python
from plugginger.schemas import manifest_from_yaml, PluginManifest

# Load and validate
with open("manifest.yaml") as f:
    manifest = manifest_from_yaml(f.read(), PluginManifest)

# Access data
print(f"Plugin: {manifest.metadata.name} v{manifest.metadata.version}")
print(f"Services: {[s.name for s in manifest.services]}")
```

### Validation Errors

The schema validates:
- Plugin names are valid Python identifiers
- Versions follow PEP 440 format
- Service signatures are properly formatted
- Event patterns are non-empty strings
- Parameter kinds are valid enum values

## Best Practices

### 1. Naming Conventions
- Use snake_case for plugin names: `user_manager`, `email_service`
- Use descriptive service names: `get_user_by_email`, `send_notification`
- Use hierarchical event patterns: `user.created`, `order.payment.failed`

### 2. Version Management
- Follow semantic versioning: `1.0.0`, `2.1.3`
- Use version constraints for dependencies: `>=1.0.0,<2.0.0`
- Update manifest version when plugin interface changes

### 3. Documentation
- Provide clear descriptions for services and event listeners
- Include parameter descriptions in docstrings
- Use meaningful keywords for discoverability

### 4. Dependencies
- Declare all required dependencies
- Use optional dependencies sparingly
- Specify version constraints to avoid conflicts

## AI Agent Usage

AI agents can use manifests to:

1. **Understand Plugin Structure** without Python imports
2. **Generate Compatible Plugins** by following existing patterns
3. **Resolve Dependencies** by analyzing dependency graphs
4. **Call Services** using signature information
5. **Handle Events** by understanding event patterns

### Example AI Workflow

```python
# 1. Load existing plugin manifests
manifests = load_all_manifests("plugins/")

# 2. Analyze available services
available_services = {}
for manifest in manifests:
    for service in manifest.services:
        available_services[service.name] = service

# 3. Generate new plugin that uses existing services
new_plugin = generate_plugin_using_services(
    available_services=["database.get_user", "email.send"]
)

# 4. Validate new plugin manifest
validate_manifest(new_plugin.manifest)
```

## Migration Guide

### From Python-only to Manifest

1. **Generate Initial Manifest**:
   ```python
   manifest = generate_plugin_manifest(YourPlugin)
   ```

2. **Review and Enhance**:
   - Add author, license, keywords
   - Add service descriptions
   - Specify version constraints

3. **Save to File**:
   ```python
   with open("manifest.yaml", "w") as f:
       f.write(manifest_to_yaml(manifest))
   ```

4. **Validate**:
   ```python
   # Test loading
   manifest = manifest_from_yaml(yaml_content, PluginManifest)
   ```

## Schema Evolution

The manifest schema follows semantic versioning:
- **Patch versions** (1.0.1): Bug fixes, clarifications
- **Minor versions** (1.1.0): New optional fields
- **Major versions** (2.0.0): Breaking changes

Future enhancements may include:
- Resource requirements (memory, CPU)
- Security permissions
- Plugin categories/tags
- Compatibility matrices

## Links

- [Plugin API Documentation](../core-api/plugin.md)
- [Service API Documentation](../core-api/service.md)
- [Events API Documentation](../core-api/events.md)
- [Manifest Generator Source](../../src/plugginger/schemas/generator.py)
