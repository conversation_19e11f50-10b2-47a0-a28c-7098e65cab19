# Plugginger Branching Strategy & Workflow

## 🎯 **Overview**

This document defines the branching strategy and workflow for the Plugginger repository to maintain a clean, production-ready main branch while enabling efficient development.

## 🌳 **Branch Structure**

### **Main Branch**
- **`main`**: Always production-ready, deployable state
- **Protection**: All quality gates must pass before merge
- **Direct commits**: Prohibited (except for hotfixes)

### **Feature Branches**
- **Naming**: `<sprint>/<task-name>` (e.g., `s2/frontend-ui`, `s3/error-handling`)
- **Lifecycle**: Create → Develop → Merge → Delete
- **Base**: Always branch from latest `main`

### **Backup Branches**
- **Naming**: `backup/YYYY-MM-DD-<description>`
- **Purpose**: Periodic snapshots of main branch
- **Retention**: Keep for major milestones

## 🔄 **Development Workflow**

### **1. Starting New Work**

```bash
# Update main branch
git checkout main
git pull origin main

# Create feature branch
git checkout -b s2/frontend-ui

# Start development
```

### **2. During Development**

```bash
# Commit frequently with conventional commits
git add .
git commit -m "feat(ui): add chat interface component"

# Run quality gates locally
pytest
mypy --strict .
ruff check .

# Push to remote
git push origin s2/frontend-ui
```

### **3. Task Completion**

```bash
# Final quality check
pytest && mypy --strict . && ruff check .

# Merge to main (if all gates pass)
git checkout main
git merge s2/frontend-ui

# Update ROADMAP.md and close GitHub issues
# Push to remote
git push origin main

# Clean up
git branch -D s2/frontend-ui
git push origin --delete s2/frontend-ui
```

## ✅ **Quality Gates**

### **Required Checks (All Must Pass)**
- **pytest**: 0 failures, ≥75% coverage
- **mypy --strict**: 0 errors (including test files)
- **ruff check**: 0 errors/warnings (including test files)

### **Enforcement**
- **Local**: Run before every commit
- **CI/CD**: Automated checks on every push/PR
- **Merge blocking**: No merge to main if gates fail

## 📋 **Conventional Commits**

### **Format**
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### **Types**
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **chore**: Maintenance tasks
- **test**: Test additions/modifications
- **refactor**: Code restructuring

### **Examples**
```bash
git commit -m "feat(ui): add responsive chat interface"
git commit -m "fix(validation): handle empty manifest files"
git commit -m "docs(api): update service documentation"
git commit -m "test(integration): add manifest loading tests"
```

## 🚫 **Prohibited Practices**

### **Never Do**
- Direct commits to `main` branch
- Merge without quality gates passing
- Keep feature branches after merge
- Work on multiple features in one branch
- Skip conventional commit format

### **Discouraged**
- Long-lived feature branches (>1 week)
- Large commits (>50 files changed)
- Merge commits without squashing
- Working on main branch directly

## 🔧 **Branch Management Commands**

### **Create Feature Branch**
```bash
git checkout main
git pull origin main
git checkout -b s3/structured-logging
```

### **Merge and Cleanup**
```bash
# After quality gates pass
git checkout main
git merge s3/structured-logging
git push origin main
git branch -D s3/structured-logging
git push origin --delete s3/structured-logging
```

### **Create Backup**
```bash
git checkout -b backup/2025-06-02-sprint3-complete
git push origin backup/2025-06-02-sprint3-complete
git checkout main
```

### **Emergency Cleanup**
```bash
# Delete multiple local branches
git branch -D branch1 branch2 branch3

# Delete multiple remote branches
git push origin --delete branch1 branch2 branch3
```

## 📊 **Success Metrics**

### **Repository Health**
- **Main branch**: Always deployable
- **Branch count**: ≤5 active feature branches
- **Quality gates**: 100% pass rate
- **Coverage**: Maintained ≥75%

### **Development Velocity**
- **Feature completion**: <1 week per branch
- **Merge frequency**: Daily to main
- **Issue closure**: Same day as merge
- **Documentation**: Updated with every feature

## 🚨 **Emergency Procedures**

### **Hotfix Process**
```bash
# For critical production fixes
git checkout main
git checkout -b hotfix/critical-security-fix
# Make minimal fix
# Run quality gates
git checkout main
git merge hotfix/critical-security-fix
git push origin main
git branch -D hotfix/critical-security-fix
```

### **Rollback Process**
```bash
# If main branch is broken
git checkout backup/YYYY-MM-DD-last-good
git checkout -b main-recovery
# Fix issues
git checkout main
git reset --hard main-recovery
git push origin main --force-with-lease
```

## 📝 **Integration with Autonomous Agents**

### **Agent Workflow Requirements**
1. **Always check quality gates** before any merge
2. **Update ROADMAP.md** with task completion
3. **Close GitHub issues** when tasks complete
4. **Create backup branches** before major changes
5. **Follow conventional commits** for all changes

### **Agent-Friendly Commands**
```bash
# Quality gate check
pytest && mypy --strict . && ruff check . && echo "✅ All gates passed"

# Complete task workflow
git add . && git commit -m "feat(scope): description" && git push origin feature-branch

# Merge and cleanup
git checkout main && git merge feature-branch && git push origin main && git branch -D feature-branch
```

---

**Last Updated**: 2. Juni 2025  
**Next Review**: After Sprint 3 completion  
**Maintainer**: AI-Agents (autonomous workflow)
