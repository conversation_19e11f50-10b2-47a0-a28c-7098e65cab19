# Roadmap-Issue Synchronization Strategy

## Overview

This document defines the strategy for maintaining synchronization between the ROADMAP.md file and GitHub Issues to ensure consistency and enable effective autonomous agent workflow.

## Current State (Post-Audit)

### ✅ Completed Synchronization
- **S1.2 Manifest-Schema**: All tasks marked `done` with completion dates and issue references
- **S1.3 Discovery Command**: All tasks marked `done` with completion dates and issue references
- **GitHub Issues**: All completed issues (#11, #12, #15, #16, #17) are closed
- **Open Issues**: Only active work items remain open (#18 for S1.4)

### 📊 Synchronization Status
| Sprint Task | Roadmap Status | GitHub Issues | Sync Status |
|-------------|----------------|---------------|-------------|
| S1.1 Soft API-Freeze | done | N/A (pre-GitHub) | ✅ Synced |
| S1.2 Manifest-Schema | done | #1, #2, #3 (closed) | ✅ Synced |
| S1.3 Discovery Command | done | #11, #12, #15, #16, #17 (closed) | ✅ Synced |
| S1.4 Reference-App | todo | #18 (open) | ✅ Synced |

## Synchronization Rules

### 1. Roadmap-to-Issue Mapping
- **Every roadmap task** MUST have a corresponding GitHub issue
- **Issue column** in roadmap tables MUST reference the GitHub issue number
- **Completion dates** in roadmap MUST match issue closure dates
- **Status consistency**: `done` in roadmap = `closed` in GitHub

### 2. Issue Lifecycle Management
- **Create issues** for all `todo` roadmap tasks
- **Close issues** immediately when roadmap tasks are marked `done`
- **Update issue descriptions** when roadmap task details change
- **Link PRs** to issues using "Fixes #N" or "Closes #N"

### 3. Autonomous Agent Workflow Compliance
- **One task per issue per branch per PR** - strict enforcement
- **No AGENT_HANDOFF.md files** - use issues and roadmap only
- **Conventional commits** required for all changes
- **Quality gates**: ruff/mypy --strict compliance, ≥90% branch coverage

## Maintenance Procedures

### Daily Synchronization Check
```bash
# 1. Check for roadmap-issue mismatches
grep -E "Status.*done" ROADMAP.md | grep -v "#[0-9]"

# 2. Check for open issues with completed roadmap tasks
gh issue list --state=open --label="sprint-1"

# 3. Verify all done tasks have completion dates
grep -E "done.*-.*-" ROADMAP.md
```

### Weekly Audit Process
1. **Review all open issues** and verify they correspond to `todo` roadmap tasks
2. **Check completion dates** match issue closure dates
3. **Verify issue references** in roadmap tables are correct
4. **Update sprint status** based on completed tasks

### Sprint Transition Process
1. **Complete current sprint audit** - ensure all `done` tasks have closed issues
2. **Create issues for next sprint** - all `todo` tasks need GitHub issues
3. **Update roadmap status** - mark sprint as `done` when all tasks complete
4. **Archive completed issues** - add appropriate labels and close

## Automation Opportunities

### GitHub Actions (Future Implementation)
- **Roadmap Sync Check**: Action that validates roadmap-issue consistency
- **Auto-Issue Creation**: Create issues for new `todo` roadmap tasks
- **Completion Validation**: Verify `done` tasks have completion dates and closed issues
- **Sprint Gate Check**: Block sprint transitions until synchronization is complete

### Pre-commit Hooks
- **Roadmap Lint**: Validate table format and required fields
- **Issue Reference Check**: Ensure all `done` tasks have issue references
- **Date Format Validation**: Verify completion dates follow YYYY-MM-DD format

## Error Recovery Procedures

### Roadmap-Issue Mismatch
1. **Identify discrepancy** using audit commands
2. **Determine source of truth** (usually the actual implementation state)
3. **Update roadmap** to match reality
4. **Close/open issues** as needed
5. **Document changes** in commit message

### Missing Issue References
1. **Create retroactive issues** for audit trail
2. **Update roadmap** with issue references
3. **Close issues immediately** if task is already complete
4. **Add completion dates** based on git history

### Orphaned Issues
1. **Review issue content** and determine current relevance
2. **Link to roadmap task** if still relevant
3. **Close with explanation** if no longer needed
4. **Create roadmap task** if issue represents new work

## Quality Gates

### Before Sprint Completion
- [ ] All `done` tasks have completion dates
- [ ] All `done` tasks have closed GitHub issues
- [ ] All `todo` tasks have open GitHub issues
- [ ] No orphaned open issues exist
- [ ] CHANGELOG.md updated for user-relevant changes

### Before New Sprint Start
- [ ] Previous sprint fully synchronized
- [ ] All new sprint tasks have GitHub issues
- [ ] Issue descriptions match roadmap requirements
- [ ] Sprint KPIs clearly defined and measurable

## Monitoring and Metrics

### Synchronization Health Metrics
- **Sync Ratio**: (Synced Tasks / Total Tasks) × 100%
- **Issue Orphan Rate**: (Orphaned Issues / Total Issues) × 100%
- **Completion Lag**: Average days between task completion and roadmap update

### Target Metrics
- **Sync Ratio**: ≥95%
- **Issue Orphan Rate**: ≤5%
- **Completion Lag**: ≤1 day

## Next Steps

### Immediate Actions (Next Agent)
1. **Implement S1.4 Reference-App** using issue #18
2. **Follow one-task-per-issue rule** strictly
3. **Update roadmap** when task is complete
4. **Close issue #18** when S1.4 is done

### Medium-term Improvements
1. **Implement GitHub Actions** for automated sync checking
2. **Create pre-commit hooks** for roadmap validation
3. **Develop sync dashboard** for visual monitoring
4. **Document lessons learned** from sync process

## Conclusion

This strategy ensures that the Plugginger project maintains perfect synchronization between planning (ROADMAP.md) and execution (GitHub Issues), enabling effective autonomous agent workflow while maintaining project transparency and accountability.

The current state is fully synchronized, and future agents must follow these procedures to maintain consistency and project health.
