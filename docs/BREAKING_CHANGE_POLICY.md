# Plugginger Breaking Change Policy

**Version**: v0.9.0-alpha  
**Status**: Active  
**Last Updated**: 2025-01-15

## Overview

This document defines what changes are considered "breaking" in the Plugginger framework and establishes clear policies for managing API stability during the transition from alpha to stable release.

## API Stability Levels

### ✅ Stable Candidates (v0.9.0-alpha)

**Modules**: `plugginger.api.*`

These APIs are considered **stable candidates** and follow strict backward compatibility rules:

- **`plugginger.api.plugin`** - Plugin creation and registration
- **`plugginger.api.service`** - Service decoration and calling
- **`plugginger.api.events`** - Event listeners and emission
- **`plugginger.api.builder`** - Application building
- **`plugginger.api.app`** - Application runtime interface
- **`plugginger.api.depends`** - Dependency injection

#### Breaking Change Policy for Stable Candidates

1. **No Breaking Changes** without major version bump (v1.0.0 → v2.0.0)
2. **Deprecation Warnings** must be issued for 1 minor version before removal
3. **Migration Guides** required for all breaking changes
4. **Backward Compatibility** maintained within major version

### 🧪 Experimental APIs

**Modules**: `plugginger.experimental.*`

These APIs are explicitly marked as experimental and **may break without notice**:

- **`plugginger.experimental.fractal`** - Advanced fractal composition
- **`plugginger.experimental.events`** - Event sourcing patterns
- **`plugginger.experimental.registry`** - Plugin marketplace features

#### Breaking Change Policy for Experimental APIs

1. **No Stability Guarantees** - APIs may change or be removed
2. **No Deprecation Warnings** required
3. **No Migration Guides** required
4. **Warning on Import** - All experimental modules issue FutureWarning

### 🔧 Internal APIs

**Modules**: `plugginger._internal.*`, `plugginger.core.*`, `plugginger.implementations.*`

These APIs are internal implementation details:

- **No Public API Guarantees** - May change without notice
- **Not Covered by SemVer** - Changes don't trigger version bumps
- **Import at Your Own Risk** - No support for external usage

## What Constitutes a Breaking Change

### ❌ Breaking Changes (Require Major Version Bump)

#### 1. Function/Method Signature Changes
```python
# BREAKING: Removing parameters
@service()
async def get_user(self, user_id: int) -> dict:  # Before
async def get_user(self) -> dict:                # After - BREAKING

# BREAKING: Changing parameter types
@service()
async def get_user(self, user_id: int) -> dict:  # Before
async def get_user(self, user_id: str) -> dict:  # After - BREAKING

# BREAKING: Changing return types
@service()
async def get_user(self, user_id: int) -> dict:        # Before
async def get_user(self, user_id: int) -> list[dict]:  # After - BREAKING
```

#### 2. Decorator Parameter Changes
```python
# BREAKING: Removing decorator parameters
@plugin(name="test", version="1.0.0", config_schema=Config)  # Before
@plugin(name="test", version="1.0.0")                       # After - BREAKING

# BREAKING: Changing required parameters
@service(name="get_user")           # Before
@service(name="get_user", timeout_seconds=5.0)  # After - BREAKING if timeout becomes required
```

#### 3. Class Interface Changes
```python
# BREAKING: Removing public methods
class PluginBase:
    def setup(self) -> None: pass     # Before
    # setup method removed           # After - BREAKING

# BREAKING: Changing inheritance requirements
class MyPlugin(PluginBase): pass    # Before
class MyPlugin(NewBase): pass       # After - BREAKING if NewBase is incompatible
```

#### 4. Exception Changes
```python
# BREAKING: Changing exception types
def validate_plugin():
    raise ValueError("Invalid")     # Before
    raise TypeError("Invalid")      # After - BREAKING

# BREAKING: Removing exceptions from documented interface
def get_user():
    """May raise UserNotFoundError"""
    # Before: raises UserNotFoundError
    # After: returns None instead    # BREAKING - changes contract
```

#### 5. Configuration Schema Changes
```python
# BREAKING: Removing required config fields
class Config(BaseModel):
    api_key: str        # Before
    timeout: int = 30
    # api_key removed   # After - BREAKING

# BREAKING: Changing field types
class Config(BaseModel):
    timeout: int = 30   # Before
    timeout: str = "30" # After - BREAKING
```

### ✅ Non-Breaking Changes (Allowed in Minor Versions)

#### 1. Adding Optional Parameters
```python
# OK: Adding optional parameters with defaults
@service()
async def get_user(self, user_id: int) -> dict:                    # Before
async def get_user(self, user_id: int, include_profile: bool = False) -> dict:  # After - OK
```

#### 2. Adding New Methods/Functions
```python
# OK: Adding new methods to classes
class PluginBase:
    def setup(self) -> None: pass     # Before
    def setup(self) -> None: pass     # After
    def new_method(self) -> None: pass  # After - OK
```

#### 3. Adding New Decorator Parameters
```python
# OK: Adding optional decorator parameters
@service(name="get_user")                              # Before
@service(name="get_user", description="Get user by ID")  # After - OK
```

#### 4. Expanding Return Types (Covariant)
```python
# OK: Making return types more specific (covariant)
async def get_data(self) -> Any:        # Before
async def get_data(self) -> dict:       # After - OK (more specific)
```

#### 5. Adding Configuration Fields
```python
# OK: Adding optional config fields
class Config(BaseModel):
    api_key: str        # Before
    timeout: int = 30
    
class Config(BaseModel):
    api_key: str        # After
    timeout: int = 30
    retries: int = 3    # After - OK (optional with default)
```

## Deprecation Process

### 1. Deprecation Warning Phase

When a breaking change is planned:

```python
import warnings

@deprecated("get_user_by_id is deprecated, use get_user instead. Will be removed in v2.0.0")
async def get_user_by_id(self, user_id: int) -> dict:
    warnings.warn(
        "get_user_by_id is deprecated, use get_user instead. "
        "Will be removed in v2.0.0",
        DeprecationWarning,
        stacklevel=2
    )
    return await self.get_user(user_id)
```

### 2. Migration Guide

For each breaking change, provide:

1. **What Changed**: Clear description of the change
2. **Why Changed**: Rationale for the breaking change
3. **How to Migrate**: Step-by-step migration instructions
4. **Code Examples**: Before/after code samples

### 3. Timeline

- **v0.9.x**: Deprecation warnings issued
- **v1.0.0**: Deprecated features removed
- **Minimum Notice**: 1 minor version (e.g., deprecated in v0.9.0, removed in v1.0.0)

## Version Numbering

Following [Semantic Versioning 2.0.0](https://semver.org/):

### Major Version (X.0.0)
- Breaking changes to stable APIs
- Removal of deprecated features
- Major architectural changes

### Minor Version (0.X.0)
- New features (backward compatible)
- New optional parameters
- New methods/classes
- Deprecation warnings

### Patch Version (0.0.X)
- Bug fixes
- Performance improvements
- Documentation updates
- Internal refactoring

### Pre-release Versions
- **Alpha** (0.9.0-alpha): Feature development, API may change
- **Beta** (0.9.0-beta): Feature freeze, only bug fixes
- **RC** (0.9.0-rc.1): Release candidate, final testing

## Current Status (v0.9.0-alpha)

### What Can Still Break

During the alpha phase, the following changes are **allowed without major version bump**:

1. **Stable Candidate APIs** may receive minor breaking changes if critical issues are found
2. **Experimental APIs** may change or be removed without notice
3. **Internal APIs** may change without notice
4. **Configuration schemas** may be adjusted for critical fixes

### What Cannot Break

Even in alpha, these are **protected from breaking changes**:

1. **Core decorator signatures**: `@plugin`, `@service`, `@on_event`
2. **Basic plugin patterns**: `PluginBase` inheritance, `needs` declaration
3. **Fundamental concepts**: Dependency injection, event emission, service calling

## Migration Support

### Automated Migration Tools

Future versions will include:

1. **`plugginger migrate`** - Automated code migration tool
2. **Deprecation Scanner** - Find deprecated API usage
3. **Compatibility Checker** - Verify plugin compatibility

### Documentation

All breaking changes will be documented in:

1. **CHANGELOG.md** - Detailed change log
2. **Migration Guides** - Step-by-step migration instructions
3. **API Documentation** - Updated with new patterns

## Enforcement

### CI/CD Checks

1. **API Compatibility Tests** - Verify backward compatibility
2. **Deprecation Warnings** - Ensure proper warning implementation
3. **Documentation Updates** - Require migration guides for breaking changes

### Review Process

1. **RFC Required** - All breaking changes require RFC discussion
2. **Community Input** - 48-hour minimum discussion period
3. **Maintainer Approval** - Final approval from core maintainers

## Contact

For questions about breaking changes or API stability:

1. **Create an Issue** with "RFC:" prefix
2. **Use RFC Template** at `docs/RFC_TEMPLATE.md`
3. **Tag as Breaking Change** for review priority

---

**This policy is effective as of v0.9.0-alpha and will be updated as the framework evolves toward v1.0.0.**
