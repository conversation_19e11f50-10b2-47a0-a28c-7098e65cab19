# Events API - Stable Core API

**Status**: ✅ Stable Candidate (v0.9.0-alpha)  
**Module**: `plugginger.api.events`

The Events API provides the `@on_event` decorator for creating event listeners and the event dispatch system. This API is considered a **stable candidate** and will be maintained with backward compatibility.

## Overview

Events enable loose coupling between plugins through an asynchronous publish-subscribe pattern. Event listeners:
1. Must be async methods on plugin classes
2. Must be decorated with `@on_event`
3. Are automatically registered and called when matching events occur

## Core Components

### `@on_event` Decorator

Marks a plugin method as an event listener.

```python
from plugginger.api.events import on_event

@on_event("user.created", timeout_seconds=5.0, priority=10)
async def on_user_created(self, event_data: dict) -> None:
    """Handle user creation event"""
    user_id = event_data["user_id"]
    await self.send_welcome_email(user_id)
```

#### Parameters

- **`patterns`** (required): Event pattern(s) to listen for (string or list)
- **`timeout_seconds`** (optional): Timeout for event listener execution
- **`description`** (optional): Human-readable description
- **`priority`** (optional): Execution priority (higher = earlier, default: 0)

#### Validation Rules

- Method must be async (`async def`)
- Method must be an instance method (first parameter is `self`)
- Method must return None
- Patterns must be non-empty strings

## Event Patterns

### 1. Exact Patterns

```python
@on_event("user.created")
async def on_user_created(self, event_data: dict) -> None:
    """Listen for exact event type"""
    pass
```

### 2. Wildcard Patterns

```python
@on_event("user.*")
async def on_any_user_event(self, event_data: dict, event_type: str) -> None:
    """Listen for any user-related event"""
    if event_type == "user.created":
        await self.handle_creation(event_data)
    elif event_type == "user.deleted":
        await self.handle_deletion(event_data)
```

### 3. Multiple Patterns

```python
@on_event(["user.created", "user.updated", "admin.user.*"])
async def on_user_changes(self, event_data: dict, event_type: str) -> None:
    """Listen for multiple event patterns"""
    await self.log_user_activity(event_type, event_data)
```

## Event Listener Signatures

### 1. Single Parameter (event_data only)

```python
@on_event("order.completed")
async def on_order_completed(self, event_data: dict) -> None:
    """Handle order completion with event data only"""
    order_id = event_data["order_id"]
    await self.send_confirmation(order_id)
```

### 2. Two Parameters (event_data + event_type)

```python
@on_event("payment.*")
async def on_payment_event(self, event_data: dict, event_type: str) -> None:
    """Handle payment events with type information"""
    if event_type == "payment.success":
        await self.process_success(event_data)
    elif event_type == "payment.failed":
        await self.handle_failure(event_data)
```

## Complete Example

```python
from plugginger.api.plugin import plugin, PluginBase
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.api.depends import Depends
from typing import List

@plugin(name="notification_service", version="1.0.0")
class NotificationPlugin(PluginBase):
    needs: List[Depends] = [
        Depends("email_service"),
        Depends("sms_service"),
        Depends("user_service")
    ]
    
    # High priority listener for critical events
    @on_event("user.created", priority=100, timeout_seconds=10.0)
    async def on_user_created(self, event_data: dict) -> None:
        """Send welcome notification to new users"""
        user_id = event_data["user_id"]
        user = await self.user_service.get_user(user_id)
        
        if user:
            await self.email_service.send_welcome_email(
                user["email"], 
                user["name"]
            )
    
    # Listen for any user event with lower priority
    @on_event("user.*", priority=10)
    async def on_user_activity(self, event_data: dict, event_type: str) -> None:
        """Log all user activity"""
        await self.log_activity(event_type, event_data)
    
    # Listen for multiple order-related events
    @on_event(["order.created", "order.completed", "order.cancelled"])
    async def on_order_events(self, event_data: dict, event_type: str) -> None:
        """Handle order lifecycle events"""
        order_id = event_data["order_id"]
        
        if event_type == "order.created":
            await self.send_order_confirmation(order_id)
        elif event_type == "order.completed":
            await self.send_completion_notification(order_id)
        elif event_type == "order.cancelled":
            await self.send_cancellation_notice(order_id)
    
    # Application lifecycle events
    @on_event("app.startup", priority=50)
    async def on_app_startup(self, event_data: dict) -> None:
        """Initialize notification service"""
        self.logger.info("Notification service starting up")
        await self.initialize_templates()
    
    @on_event("app.shutdown", priority=50)
    async def on_app_shutdown(self, event_data: dict) -> None:
        """Clean up notification service"""
        self.logger.info("Notification service shutting down")
        await self.cleanup_resources()
    
    @service()
    async def send_custom_notification(self, user_id: int, message: str) -> bool:
        """Send custom notification and emit event"""
        user = await self.user_service.get_user(user_id)
        if not user:
            return False
        
        # Send notification
        success = await self.email_service.send_email(
            user["email"], 
            "Custom Notification", 
            message
        )
        
        # Emit event for other plugins to react
        if success:
            await self.app.emit_event("notification.sent", {
                "user_id": user_id,
                "type": "custom",
                "message": message
            })
        
        return success
```

## Event Emission

Plugins can emit events through the app instance:

```python
@service()
async def create_user(self, user_data: dict) -> dict:
    """Create user and emit creation event"""
    # Create user
    user = await self.database.create_user(user_data)
    
    # Emit event for other plugins
    await self.app.emit_event("user.created", {
        "user_id": user["id"],
        "email": user["email"],
        "created_at": user["created_at"]
    })
    
    return user
```

## Priority and Execution Order

Event listeners are executed in priority order (highest first):

```python
@on_event("user.created", priority=100)  # Executed first
async def critical_handler(self, event_data: dict) -> None:
    pass

@on_event("user.created", priority=50)   # Executed second
async def important_handler(self, event_data: dict) -> None:
    pass

@on_event("user.created", priority=0)    # Executed last (default)
async def normal_handler(self, event_data: dict) -> None:
    pass
```

## Utility Functions

### `get_event_listener_metadata(method)`

Extract event listener metadata.

```python
from plugginger.api.events import get_event_listener_metadata

metadata = get_event_listener_metadata(NotificationPlugin.on_user_created)
# Returns: {
#     "patterns": ["user.created"],
#     "timeout_seconds": 10.0,
#     "priority": 100,
#     "method_name": "on_user_created",
#     ...
# }
```

### `is_event_listener_method(method)`

Check if a method is a valid event listener.

```python
from plugginger.api.events import is_event_listener_method

if is_event_listener_method(NotificationPlugin.on_user_created):
    print("Valid event listener")
```

### `extract_event_listeners(plugin_instance)`

Get all event listeners from a plugin.

```python
from plugginger.api.events import extract_event_listeners

notification_plugin = NotificationPlugin(app)
listeners = extract_event_listeners(notification_plugin)
# Returns: {"on_user_created": <bound method>, ...}
```

### `get_listener_patterns(plugin_instance)`

Get all patterns and handlers with metadata.

```python
from plugginger.api.events import get_listener_patterns

patterns = get_listener_patterns(notification_plugin)
# Returns: [("user.created", <handler>, <metadata>), ...]
```

## Best Practices

### 1. Event Naming
- Use hierarchical naming: `user.created`, `order.payment.failed`
- Be specific: `email.sent` not `notification`
- Use past tense: `user.created` not `user.create`

### 2. Event Data Structure
- Use consistent data structures
- Include relevant IDs and timestamps
- Keep payloads lightweight

```python
# ✅ Good event data
{
    "user_id": 123,
    "email": "<EMAIL>",
    "created_at": "2025-01-15T10:30:00Z",
    "source": "registration_form"
}

# ❌ Avoid large payloads
{
    "user_id": 123,
    "full_user_object": {...},  # Too much data
    "entire_request": {...}     # Unnecessary context
}
```

### 3. Error Handling
- Handle exceptions gracefully in listeners
- Don't let one listener failure affect others
- Log errors appropriately

```python
@on_event("user.created")
async def on_user_created(self, event_data: dict) -> None:
    try:
        await self.send_welcome_email(event_data["user_id"])
    except EmailServiceError as e:
        self.logger.error(f"Failed to send welcome email: {e}")
        # Don't re-raise - let other listeners continue
    except Exception as e:
        self.logger.exception(f"Unexpected error in user creation handler: {e}")
```

### 4. Performance
- Keep listeners fast and lightweight
- Use appropriate timeouts
- Consider async operations for I/O

### 5. Testing
- Test event listeners independently
- Mock event emissions in tests
- Verify event data structures

## Error Handling

The Events API raises specific exceptions:

- **`EventDefinitionError`**: Invalid event listener definition
- **`ValidationError`**: Invalid method signature

```python
from plugginger.core.exceptions import EventDefinitionError

try:
    @on_event("user.created")
    def sync_handler(self, event_data):  # Missing 'async'
        pass
except EventDefinitionError as e:
    print(f"Event definition error: {e}")
```

## API Stability

This API is marked as a **stable candidate** in v0.9.0-alpha:

- ✅ Core event listener functionality is stable
- ✅ Event pattern matching is stable
- ✅ Priority-based execution is stable
- ⚠️ Advanced event features may be added before v1.0.0
