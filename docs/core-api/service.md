# Service API - Stable Core API

**Status**: ✅ Stable Candidate (v0.9.0-alpha)  
**Module**: `plugginger.api.service`

The Service API provides the `@service` decorator for exposing plugin methods as callable services. This API is considered a **stable candidate** and will be maintained with backward compatibility.

## Overview

Services are the primary way plugins expose functionality to other plugins. Every service:
1. Must be an async method on a plugin class
2. Must be decorated with `@service`
3. Is automatically registered and callable via dependency injection

## Core Components

### `@service` Decorator

Marks a plugin method as a callable service.

```python
from plugginger.api.service import service

@service(name="get_user", timeout_seconds=5.0, description="Retrieve user by ID")
async def get_user_by_id(self, user_id: int) -> dict:
    """Get user from database"""
    return {"id": user_id, "name": "<PERSON>"}
```

#### Parameters

- **`name`** (optional): Custom service name (defaults to method name)
- **`timeout_seconds`** (optional): Timeout for service calls
- **`description`** (optional): Human-readable description

#### Validation Rules

- Method must be async (`async def`)
- Method must be an instance method (first parameter is `self`)
- Service name must be a non-empty string
- Timeout must be a positive number

## Service Method Requirements

### 1. Async Methods Only

```python
# ✅ Correct - async method
@service()
async def fetch_data(self, query: str) -> list:
    return await self.database.query(query)

# ❌ Wrong - sync method
@service()
def fetch_data(self, query: str) -> list:  # Missing 'async'
    return self.database.query(query)
```

### 2. Instance Methods

```python
# ✅ Correct - instance method
@service()
async def process_data(self, data: dict) -> dict:
    return self.transform(data)

# ❌ Wrong - static method
@staticmethod
@service()
async def process_data(data: dict) -> dict:  # Missing 'self'
    return transform(data)
```

### 3. Type Hints Recommended

```python
# ✅ Recommended - full type hints
@service()
async def calculate_score(self, user_id: int, factors: list[str]) -> float:
    """Calculate user score based on factors"""
    return 85.5

# ⚠️ Acceptable - minimal type hints
@service()
async def calculate_score(self, user_id, factors):
    return 85.5
```

## Complete Example

```python
from plugginger.api.plugin import plugin, PluginBase
from plugginger.api.service import service
from plugginger.api.depends import Depends
from typing import List, Optional
import asyncio

@plugin(name="user_service", version="1.0.0")
class UserServicePlugin(PluginBase):
    needs: List[Depends] = [Depends("database"), Depends("cache")]
    
    @service(name="get_user", timeout_seconds=3.0)
    async def get_user_by_id(self, user_id: int) -> Optional[dict]:
        """Retrieve user by ID with caching"""
        # Check cache first
        cached = await self.cache.get(f"user:{user_id}")
        if cached:
            return cached
        
        # Fetch from database
        user = await self.database.fetch_user(user_id)
        if user:
            await self.cache.set(f"user:{user_id}", user, ttl=300)
        
        return user
    
    @service(name="create_user", timeout_seconds=10.0)
    async def create_user(self, user_data: dict) -> dict:
        """Create a new user"""
        # Validate user data
        if not user_data.get("email"):
            raise ValueError("Email is required")
        
        # Create user in database
        user_id = await self.database.create_user(user_data)
        
        # Return created user
        return await self.get_user_by_id(user_id)
    
    @service(name="search_users")
    async def search_users(self, query: str, limit: int = 10) -> list[dict]:
        """Search users by query"""
        return await self.database.search_users(query, limit)
    
    @service(name="batch_get_users", timeout_seconds=15.0)
    async def get_users_batch(self, user_ids: list[int]) -> list[dict]:
        """Get multiple users efficiently"""
        # Use asyncio.gather for concurrent fetching
        tasks = [self.get_user_by_id(uid) for uid in user_ids]
        users = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and None values
        return [user for user in users if isinstance(user, dict)]
```

## Service Calling

Services are called through dependency injection proxies:

```python
@plugin(name="notification_service", version="1.0.0")
class NotificationPlugin(PluginBase):
    needs: List[Depends] = [Depends("user_service")]
    
    @service()
    async def send_notification(self, user_id: int, message: str) -> bool:
        """Send notification to user"""
        # Call user_service.get_user service
        user = await self.user_service.get_user(user_id)
        if not user:
            return False
        
        # Send notification logic here
        await self.send_email(user["email"], message)
        return True
```

## Utility Functions

### `get_service_metadata(method)`

Extract service metadata from a decorated method.

```python
from plugginger.api.service import get_service_metadata

metadata = get_service_metadata(UserServicePlugin.get_user_by_id)
# Returns: {
#     "name": "get_user",
#     "timeout_seconds": 3.0,
#     "description": None,
#     "method_name": "get_user_by_id",
#     "signature": "(self, user_id: int) -> Optional[dict]",
#     "parameters": [...]
# }
```

### `is_service_method(method)`

Check if a method is a valid service.

```python
from plugginger.api.service import is_service_method

if is_service_method(UserServicePlugin.get_user_by_id):
    print("Valid service method")
```

### `extract_service_methods(plugin_instance)`

Get all services from a plugin instance.

```python
from plugginger.api.service import extract_service_methods

user_plugin = UserServicePlugin(app)
services = extract_service_methods(user_plugin)
# Returns: {"get_user": <bound method>, "create_user": <bound method>, ...}
```

## Best Practices

### 1. Service Naming
- Use descriptive names: `get_user_by_email`, `calculate_tax`
- Follow verb_noun pattern: `create_order`, `delete_file`
- Avoid generic names: `process`, `handle`, `do`

### 2. Error Handling
- Raise specific exceptions for different error cases
- Use appropriate HTTP-like status concepts
- Document expected exceptions in docstrings

```python
@service()
async def get_user(self, user_id: int) -> dict:
    """Get user by ID.
    
    Raises:
        ValueError: If user_id is invalid
        UserNotFoundError: If user doesn't exist
        DatabaseError: If database is unavailable
    """
    if user_id <= 0:
        raise ValueError("User ID must be positive")
    
    user = await self.database.get_user(user_id)
    if not user:
        raise UserNotFoundError(f"User {user_id} not found")
    
    return user
```

### 3. Timeouts
- Set reasonable timeouts for all services
- Consider downstream dependencies
- Use shorter timeouts for simple operations

```python
@service(timeout_seconds=1.0)  # Fast cache lookup
async def get_cached_data(self, key: str) -> Optional[dict]:
    return await self.cache.get(key)

@service(timeout_seconds=30.0)  # Complex calculation
async def generate_report(self, params: dict) -> dict:
    return await self.heavy_computation(params)
```

### 4. Type Hints
- Always use type hints for parameters and return values
- Use Optional[] for nullable returns
- Use Union[] for multiple possible types

### 5. Documentation
- Write clear docstrings for all services
- Document parameters, return values, and exceptions
- Include usage examples for complex services

## Error Handling

The Service API raises specific exceptions:

- **`ServiceDefinitionError`**: Invalid service definition
- **`ValidationError`**: Invalid method signature

```python
from plugginger.core.exceptions import ServiceDefinitionError

try:
    @service()
    def sync_method(self):  # Missing 'async'
        pass
except ServiceDefinitionError as e:
    print(f"Service definition error: {e}")
```

## Performance Considerations

### 1. Async Best Practices
- Use `await` for I/O operations
- Avoid blocking operations in service methods
- Use `asyncio.gather()` for concurrent operations

### 2. Caching
- Cache expensive computations
- Use appropriate TTL values
- Consider cache invalidation strategies

### 3. Resource Management
- Close connections and files properly
- Use context managers where appropriate
- Monitor memory usage for large operations

## API Stability

This API is marked as a **stable candidate** in v0.9.0-alpha:

- ✅ Core decorator functionality is stable
- ✅ Service registration and calling is stable
- ✅ Breaking changes will be documented
- ⚠️ Timeout and error handling may be enhanced before v1.0.0
