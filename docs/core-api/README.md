# Plugginger Core API Documentation

**Status**: ✅ Stable Candidates (v0.9.0-alpha)

This directory contains documentation for Plugginger's **stable candidate** APIs. These APIs are considered stable and will be maintained with backward compatibility as the framework moves toward v1.0.0.

## API Stability Levels

### ✅ Stable Candidates (v0.9.0-alpha)
APIs marked as stable candidates are:
- Feature-complete for core use cases
- Thoroughly tested with comprehensive test coverage
- Unlikely to have breaking changes
- Will receive deprecation warnings before removal
- Safe to use in production applications

### 🧪 Experimental APIs
APIs in `plugginger.experimental.*` are:
- Subject to change without notice
- May be removed in future versions
- Not covered by semantic versioning guarantees
- Should not be used in production

## Core API Components

### 1. [Plugin API](plugin.md) - `plugginger.api.plugin`

Foundation for creating plugins in Plugginger.

**Key Components:**
- `PluginBase` - Base class for all plugins
- `@plugin` - Decorator for plugin registration
- `get_plugin_metadata()` - Extract plugin information
- `is_plugin_class()` - Validate plugin classes

**Example:**
```python
from plugginger.api.plugin import plugin, PluginBase

@plugin(name="my_service", version="1.0.0")
class MyServicePlugin(PluginBase):
    needs = [Depends("logger")]
```

### 2. [Service API](service.md) - `plugginger.api.service`

Expose plugin methods as callable services.

**Key Components:**
- `@service` - Decorator for service methods
- `get_service_metadata()` - Extract service information
- `is_service_method()` - Validate service methods
- `extract_service_methods()` - Get all services from plugin

**Example:**
```python
from plugginger.api.service import service

@service(name="get_user", timeout_seconds=5.0)
async def get_user_by_id(self, user_id: int) -> dict:
    return await self.database.fetch_user(user_id)
```

### 3. [Events API](events.md) - `plugginger.api.events`

Create event listeners for asynchronous communication.

**Key Components:**
- `@on_event` - Decorator for event listeners
- `get_event_listener_metadata()` - Extract listener information
- `is_event_listener_method()` - Validate event listeners
- `extract_event_listeners()` - Get all listeners from plugin

**Example:**
```python
from plugginger.api.events import on_event

@on_event("user.created", priority=10)
async def on_user_created(self, event_data: dict) -> None:
    await self.send_welcome_email(event_data["user_id"])
```

## Quick Start Guide

### 1. Create a Basic Plugin

```python
from plugginger.api.plugin import plugin, PluginBase
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.api.depends import Depends
from typing import List

@plugin(name="user_manager", version="1.0.0")
class UserManagerPlugin(PluginBase):
    needs: List[Depends] = [Depends("database"), Depends("logger")]
    
    async def setup(self, plugin_config) -> None:
        """Initialize the plugin"""
        self.logger.info("User manager starting up")
    
    @service(name="create_user", timeout_seconds=10.0)
    async def create_user(self, user_data: dict) -> dict:
        """Create a new user"""
        user = await self.database.create_user(user_data)
        
        # Emit event for other plugins
        await self.app.emit_event("user.created", {
            "user_id": user["id"],
            "email": user["email"]
        })
        
        return user
    
    @service(name="get_user")
    async def get_user(self, user_id: int) -> dict:
        """Get user by ID"""
        return await self.database.get_user(user_id)
    
    @on_event("app.shutdown")
    async def on_shutdown(self, event_data: dict) -> None:
        """Clean up on shutdown"""
        self.logger.info("User manager shutting down")
```

### 2. Build and Run Application

```python
from plugginger.api.builder import PluggingerAppBuilder

# Build application with plugins
app = (
    PluggingerAppBuilder()
    .add_plugin(UserManagerPlugin)
    .add_plugin(DatabasePlugin)
    .add_plugin(LoggerPlugin)
    .build()
)

# Start the application
await app.start()

# Use services
user = await app.call_service("user_manager", "create_user", {
    "name": "John Doe",
    "email": "<EMAIL>"
})

# Emit events
await app.emit_event("custom.event", {"data": "value"})

# Shutdown
await app.shutdown()
```

## Common Patterns

### 1. Service Dependencies

```python
@plugin(name="notification_service", version="1.0.0")
class NotificationPlugin(PluginBase):
    needs = [Depends("user_manager"), Depends("email_service")]
    
    @service()
    async def notify_user(self, user_id: int, message: str) -> bool:
        # Call another service
        user = await self.user_manager.get_user(user_id)
        if user:
            return await self.email_service.send_email(
                user["email"], 
                "Notification", 
                message
            )
        return False
```

### 2. Event-Driven Architecture

```python
@plugin(name="audit_logger", version="1.0.0")
class AuditLoggerPlugin(PluginBase):
    needs = [Depends("database")]
    
    @on_event("user.*", priority=5)
    async def log_user_events(self, event_data: dict, event_type: str) -> None:
        """Log all user-related events"""
        await self.database.log_event(event_type, event_data)
    
    @on_event(["order.created", "order.completed"], priority=10)
    async def log_order_events(self, event_data: dict, event_type: str) -> None:
        """Log important order events with high priority"""
        await self.database.log_critical_event(event_type, event_data)
```

### 3. Configuration with Validation

```python
from pydantic import BaseModel

class EmailConfig(BaseModel):
    smtp_host: str
    smtp_port: int = 587
    username: str
    password: str
    use_tls: bool = True

@plugin(name="email_service", version="1.0.0", config_schema=EmailConfig)
class EmailServicePlugin(PluginBase):
    async def setup(self, plugin_config: EmailConfig) -> None:
        """Setup with validated configuration"""
        self.smtp_host = plugin_config.smtp_host
        self.smtp_port = plugin_config.smtp_port
        # ... initialize SMTP connection
```

## Best Practices Summary

### Plugin Design
- ✅ Use descriptive plugin names (snake_case)
- ✅ Follow semantic versioning
- ✅ Declare all dependencies in `needs`
- ✅ Use Pydantic models for configuration
- ✅ Keep plugins focused and cohesive

### Service Design
- ✅ Make all service methods async
- ✅ Use type hints for parameters and returns
- ✅ Set appropriate timeouts
- ✅ Handle errors gracefully
- ✅ Write clear docstrings

### Event Design
- ✅ Use hierarchical event names (user.created)
- ✅ Keep event payloads lightweight
- ✅ Set appropriate priorities
- ✅ Handle exceptions in listeners
- ✅ Use past tense for event names

### Error Handling
- ✅ Raise specific exceptions
- ✅ Log errors appropriately
- ✅ Don't let failures cascade
- ✅ Provide helpful error messages

## Migration from Experimental APIs

If you're using experimental APIs, migrate to stable APIs:

1. **Replace imports:**
   ```python
   # Old
   from plugginger.experimental.plugin import ExperimentalPlugin
   
   # New
   from plugginger.api.plugin import plugin, PluginBase
   ```

2. **Update decorators:**
   ```python
   # Old
   @experimental_plugin(name="test")
   
   # New
   @plugin(name="test", version="1.0.0")
   ```

3. **Follow new patterns:**
   - Use `PluginBase` inheritance
   - Declare dependencies with `Depends()`
   - Use async methods for services and events

## API Reference Links

- **[Plugin API Reference](plugin.md)** - Complete plugin creation guide
- **[Service API Reference](service.md)** - Service decoration and calling
- **[Events API Reference](events.md)** - Event listeners and emission
- **[Main Documentation](../README.md)** - Framework overview and guides

## Support and Feedback

For questions about the Core APIs:
1. Check the individual API documentation files
2. Look at the examples in each section
3. Review the test files for usage patterns
4. Create an issue for bugs or feature requests

**Remember**: These APIs are stable candidates and safe for production use in v0.9.0-alpha!
