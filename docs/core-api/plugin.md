# Plugin API - Stable Core API

**Status**: ✅ Stable Candidate (v0.9.0-alpha)  
**Module**: `plugginger.api.plugin`

The Plugin API provides the foundation for creating plugins in the Plugginger framework. This API is considered a **stable candidate** and will be maintained with backward compatibility.

## Overview

Every plugin in Plugginger must:
1. Inherit from `PluginBase`
2. Be decorated with `@plugin`
3. Follow the dependency injection pattern

## Core Components

### `PluginBase` Class

Base class that all plugins must inherit from.

```python
from plugginger.api.plugin import PluginBase
from plugginger.api.depends import Depends
from typing import List

class MyPlugin(PluginBase):
    needs: List[Depends] = [Depends("logger")]
    
    async def setup(self, plugin_config) -> None:
        """Optional setup hook"""
        pass
    
    async def teardown(self) -> None:
        """Optional teardown hook"""
        pass
```

#### Key Attributes

- **`needs`**: List of dependencies this plugin requires
- **`app`**: Reference to the PluggingerAppInstance (set by builder)
- **`_plugginger_plugin_name`**: Plugin name (set by @plugin decorator)
- **`_plugginger_plugin_version`**: Plugin version (set by @plugin decorator)

#### Lifecycle Hooks

- **`setup(plugin_config)`**: Called after dependency injection, before service registration
- **`teardown()`**: Called during shutdown, before service unregistration

### `@plugin` Decorator

Marks a class as a Plugginger plugin and adds metadata.

```python
from plugginger.api.plugin import plugin
from pydantic import BaseModel

class MyConfig(BaseModel):
    api_key: str
    timeout: int = 30

@plugin(name="data_fetcher", version="1.0.0", config_schema=MyConfig)
class DataFetcherPlugin(PluginBase):
    pass
```

#### Parameters

- **`name`** (required): Plugin identifier (snake_case, valid Python identifier)
- **`version`** (required): Version string (PEP 440 compatible)
- **`config_schema`** (optional): Pydantic model for configuration validation

#### Validation Rules

- Name must be lowercase, valid Python identifier, no leading/trailing underscores
- Version must start with digit or 'v' + digit
- Config schema must be a Pydantic BaseModel subclass

## Complete Example

```python
from plugginger.api.plugin import plugin, PluginBase
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.api.depends import Depends
from pydantic import BaseModel
from typing import List, Optional

class DatabaseConfig(BaseModel):
    connection_string: str
    pool_size: int = 10
    timeout: float = 30.0

@plugin(name="database", version="2.1.0", config_schema=DatabaseConfig)
class DatabasePlugin(PluginBase):
    needs: List[Depends] = [
        Depends("logger"),
        Depends("metrics", optional=True)
    ]
    
    async def setup(self, plugin_config: DatabaseConfig) -> None:
        """Initialize database connection"""
        self.connection_string = plugin_config.connection_string
        self.pool_size = plugin_config.pool_size
        self.logger.info(f"Setting up database with pool size {self.pool_size}")
    
    async def teardown(self) -> None:
        """Clean up database connections"""
        self.logger.info("Closing database connections")
    
    @service(name="get_user", timeout_seconds=5.0)
    async def get_user_by_id(self, user_id: int) -> Optional[dict]:
        """Retrieve user by ID from database"""
        # Implementation here
        return {"id": user_id, "name": "John Doe"}
    
    @on_event("app.shutdown")
    async def on_app_shutdown(self, event_data: dict) -> None:
        """Handle application shutdown"""
        await self.close_connections()
```

## Utility Functions

### `get_plugin_metadata(plugin_class)`

Extract metadata from a plugin class.

```python
from plugginger.api.plugin import get_plugin_metadata

metadata = get_plugin_metadata(DatabasePlugin)
# Returns: {
#     "name": "database",
#     "version": "2.1.0", 
#     "config_schema": DatabaseConfig,
#     "class_name": "DatabasePlugin",
#     "module": "__main__"
# }
```

### `is_plugin_class(cls)`

Check if a class is a valid plugin.

```python
from plugginger.api.plugin import is_plugin_class

if is_plugin_class(DatabasePlugin):
    print("Valid plugin class")
```

## Best Practices

### 1. Plugin Naming
- Use snake_case: `user_manager`, `email_service`
- Be descriptive: `oauth_authenticator` not `auth`
- Avoid generic names: `helper`, `utils`, `manager`

### 2. Versioning
- Follow semantic versioning: `1.0.0`, `2.1.3`
- Use pre-release tags: `1.0.0-alpha`, `2.0.0-beta.1`
- Increment appropriately for breaking changes

### 3. Configuration
- Always use Pydantic models for config validation
- Provide sensible defaults
- Document all configuration options

### 4. Dependencies
- Declare all dependencies in `needs`
- Use optional dependencies sparingly
- Prefer composition over inheritance

### 5. Lifecycle Management
- Use `setup()` for initialization that might fail
- Use `teardown()` for cleanup (connections, files, etc.)
- Keep lifecycle methods simple and fast

## Error Handling

The Plugin API raises specific exceptions:

- **`PluginRegistrationError`**: Invalid plugin definition
- **`TypeError`**: Invalid config_schema type

```python
from plugginger.core.exceptions import PluginRegistrationError

try:
    @plugin(name="invalid-name", version="1.0.0")  # Invalid name
    class BadPlugin(PluginBase):
        pass
except PluginRegistrationError as e:
    print(f"Plugin registration failed: {e}")
```

## Migration Guide

When upgrading from experimental APIs:

1. Replace `plugginger.experimental.plugin` imports with `plugginger.api.plugin`
2. Ensure all plugins inherit from `PluginBase`
3. Add `@plugin` decorator with required parameters
4. Update dependency declarations to use `Depends()`

## API Stability

This API is marked as a **stable candidate** in v0.9.0-alpha:

- ✅ Core functionality is stable
- ✅ Breaking changes will be documented
- ✅ Deprecation warnings for removed features
- ⚠️ Minor additions may occur before v1.0.0
