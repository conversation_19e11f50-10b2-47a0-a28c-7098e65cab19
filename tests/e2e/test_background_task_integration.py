# tests/e2e/test_background_task_integration.py

"""
End-to-End tests for Background Task Management in Plugginger framework.

Tests comprehensive background task lifecycle, managed task creation,
automatic cleanup, concurrent execution, and error handling scenarios.

Priority 2: Background Task Management - Target: +10% coverage (52% → 62%)
"""

from __future__ import annotations

import asyncio
from typing import Any, cast

import pytest

# Direct imports to avoid mypy issues with lazy loading
from plugginger.api.app import PluggingerAppInstance
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


# Test Plugin Classes for Background Task Management
@plugin(name="task_manager", version="1.0.0")
class TaskManagerPlugin(PluginBase):
    """Plugin that manages background tasks for testing."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.tasks_created: list[str] = []
        self.tasks_completed: list[str] = []
        self.tasks_cancelled: list[str] = []
        self.task_results: dict[str, Any] = {}
        self.active_tasks: dict[str, asyncio.Task[Any]] = {}

    @service()
    async def create_simple_task(self, task_name: str, duration: float = 0.1) -> str:
        """Create a simple background task."""
        async def simple_task() -> str:
            await asyncio.sleep(duration)
            result = f"completed_{task_name}"
            self.tasks_completed.append(task_name)
            self.task_results[task_name] = result
            return result

        task = self.app.create_managed_task(simple_task())
        self.active_tasks[task_name] = task
        self.tasks_created.append(task_name)
        return f"created_task_{task_name}"

    @service()
    async def create_long_running_task(self, task_name: str, duration: float = 10.0) -> str:
        """Create a long-running background task."""
        async def long_task() -> str:
            try:
                await asyncio.sleep(duration)
                result = f"long_completed_{task_name}"
                self.tasks_completed.append(task_name)
                self.task_results[task_name] = result
                return result
            except asyncio.CancelledError:
                self.tasks_cancelled.append(task_name)
                raise

        task = self.app.create_managed_task(long_task())
        self.active_tasks[task_name] = task
        self.tasks_created.append(task_name)
        return f"created_long_task_{task_name}"

    @service()
    async def create_failing_task(self, task_name: str, error_message: str = "Task failed") -> str:
        """Create a background task that fails."""
        async def failing_task() -> str:
            await asyncio.sleep(0.05)  # Small delay before failing
            raise RuntimeError(error_message)

        task = self.app.create_managed_task(failing_task())
        self.active_tasks[task_name] = task
        self.tasks_created.append(task_name)
        return f"created_failing_task_{task_name}"

    @service()
    async def get_task_stats(self) -> dict[str, Any]:
        """Get statistics about managed tasks."""
        return {
            "created": len(self.tasks_created),
            "completed": len(self.tasks_completed),
            "cancelled": len(self.tasks_cancelled),
            "active": len([t for t in self.active_tasks.values() if not t.done()]),
            "task_results": self.task_results.copy(),
            "created_tasks": self.tasks_created.copy(),
            "completed_tasks": self.tasks_completed.copy(),
            "cancelled_tasks": self.tasks_cancelled.copy()
        }

    @service()
    async def wait_for_task(self, task_name: str, timeout: float = 1.0) -> str:
        """Wait for a specific task to complete."""
        if task_name not in self.active_tasks:
            return f"task_{task_name}_not_found"

        task = self.active_tasks[task_name]
        try:
            result = await asyncio.wait_for(task, timeout=timeout)
            return f"task_{task_name}_result_{result}"
        except TimeoutError:
            return f"task_{task_name}_timeout"
        except Exception as e:
            return f"task_{task_name}_error_{type(e).__name__}"


@plugin(name="concurrent_task_manager", version="1.0.0")
class ConcurrentTaskManagerPlugin(PluginBase):
    """Plugin that tests concurrent task management."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.concurrent_results: list[str] = []
        self.concurrent_tasks: list[asyncio.Task[Any]] = []

    @service()
    async def create_concurrent_tasks(self, count: int, base_duration: float = 0.1) -> str:
        """Create multiple concurrent background tasks."""
        async def concurrent_task(task_id: int) -> str:
            await asyncio.sleep(base_duration + (task_id * 0.01))  # Slight variation
            result = f"concurrent_task_{task_id}_done"
            self.concurrent_results.append(result)
            return result

        # Create multiple tasks concurrently
        for i in range(count):
            task = self.app.create_managed_task(concurrent_task(i))
            self.concurrent_tasks.append(task)

        return f"created_{count}_concurrent_tasks"

    @service()
    async def get_concurrent_stats(self) -> dict[str, Any]:
        """Get statistics about concurrent tasks."""
        completed_count = len([t for t in self.concurrent_tasks if t.done() and not t.cancelled()])
        cancelled_count = len([t for t in self.concurrent_tasks if t.cancelled()])
        pending_count = len([t for t in self.concurrent_tasks if not t.done()])

        return {
            "total_tasks": len(self.concurrent_tasks),
            "completed": completed_count,
            "cancelled": cancelled_count,
            "pending": pending_count,
            "results_count": len(self.concurrent_results),
            "results": self.concurrent_results.copy()
        }


@plugin(name="task_lifecycle_monitor", version="1.0.0")
class TaskLifecycleMonitorPlugin(PluginBase):
    """Plugin that monitors task lifecycle events."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.lifecycle_events: list[str] = []
        self.monitored_tasks: dict[str, asyncio.Task[Any]] = {}

    async def setup(self, plugin_config: Any) -> None:
        """Setup lifecycle monitoring."""
        self.lifecycle_events.append("plugin_setup_complete")

    async def teardown(self) -> None:
        """Teardown and cleanup monitoring."""
        self.lifecycle_events.append("plugin_teardown_start")

        # Cancel any remaining monitored tasks
        for task_name, task in self.monitored_tasks.items():
            if not task.done():
                task.cancel()
                self.lifecycle_events.append(f"cancelled_task_{task_name}")

        self.lifecycle_events.append("plugin_teardown_complete")

    @service()
    async def create_monitored_task(self, task_name: str, duration: float = 0.2) -> str:
        """Create a task with lifecycle monitoring."""
        async def monitored_task() -> str:
            self.lifecycle_events.append(f"task_{task_name}_started")
            try:
                await asyncio.sleep(duration)
                self.lifecycle_events.append(f"task_{task_name}_completed")
                return f"monitored_result_{task_name}"
            except asyncio.CancelledError:
                self.lifecycle_events.append(f"task_{task_name}_cancelled")
                raise
            except Exception as e:
                self.lifecycle_events.append(f"task_{task_name}_failed_{type(e).__name__}")
                raise

        task = self.app.create_managed_task(monitored_task())
        self.monitored_tasks[task_name] = task
        self.lifecycle_events.append(f"created_monitored_task_{task_name}")
        return f"monitoring_task_{task_name}"

    @service()
    async def get_lifecycle_events(self) -> list[str]:
        """Get all recorded lifecycle events."""
        return self.lifecycle_events.copy()


class TestBackgroundTaskIntegration:
    """Test suite for Background Task Management Integration."""

    @pytest.mark.asyncio
    async def test_basic_task_creation_and_completion(self) -> None:
        """Test basic background task creation and completion."""
        builder = PluggingerAppBuilder(app_name="task_basic_test")
        builder.include(TaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create a simple task
        result = await app.call_service("task_manager.create_simple_task",
                                      task_name="test_task", duration=0.05)
        assert result == "created_task_test_task"

        # Wait for task completion
        await asyncio.sleep(0.1)

        # Check task statistics
        stats = await app.call_service("task_manager.get_task_stats")
        assert stats["created"] >= 1
        assert stats["completed"] >= 1
        assert "test_task" in stats["completed_tasks"]
        assert stats["task_results"]["test_task"] == "completed_test_task"

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self) -> None:
        """Test concurrent execution of multiple background tasks."""
        builder = PluggingerAppBuilder(app_name="task_concurrent_test")
        builder.include(ConcurrentTaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create multiple concurrent tasks
        task_count = 5
        result = await app.call_service("concurrent_task_manager.create_concurrent_tasks",
                                      count=task_count, base_duration=0.05)
        assert result == f"created_{task_count}_concurrent_tasks"

        # Wait for tasks to complete
        await asyncio.sleep(0.2)

        # Check concurrent task statistics
        stats = await app.call_service("concurrent_task_manager.get_concurrent_stats")
        assert stats["total_tasks"] == task_count
        assert stats["completed"] >= task_count
        assert stats["results_count"] >= task_count
        assert len(stats["results"]) >= task_count

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_task_lifecycle_and_cleanup(self) -> None:
        """Test task lifecycle management and automatic cleanup."""
        builder = PluggingerAppBuilder(app_name="task_lifecycle_test")
        builder.include(TaskLifecycleMonitorPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create monitored tasks
        await app.call_service("task_lifecycle_monitor.create_monitored_task",
                              task_name="lifecycle_task", duration=0.1)

        # Wait for task completion
        await asyncio.sleep(0.15)

        # Check lifecycle events before teardown
        events_before = await app.call_service("task_lifecycle_monitor.get_lifecycle_events")
        assert "plugin_setup_complete" in events_before
        assert "created_monitored_task_lifecycle_task" in events_before
        assert "task_lifecycle_task_started" in events_before
        assert "task_lifecycle_task_completed" in events_before

        # Get plugin reference before shutdown
        monitor_plugin = cast(TaskLifecycleMonitorPlugin,
                            app.get_plugin_instance("task_lifecycle_test:task_lifecycle_monitor"))

        # Stop plugins and verify cleanup
        await app.stop_all_plugins()

        # Get final lifecycle events (plugin reference is still valid)
        final_events = monitor_plugin.lifecycle_events

        assert "plugin_teardown_start" in final_events
        assert "plugin_teardown_complete" in final_events

    @pytest.mark.asyncio
    async def test_task_error_handling_and_isolation(self) -> None:
        """Test error handling and isolation in background tasks."""
        builder = PluggingerAppBuilder(app_name="task_error_test")
        builder.include(TaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create a failing task
        result = await app.call_service("task_manager.create_failing_task",
                                      task_name="error_task", error_message="Test error")
        assert result == "created_failing_task_error_task"

        # Create a successful task to ensure isolation
        await app.call_service("task_manager.create_simple_task",
                              task_name="success_task", duration=0.05)

        # Wait for tasks to complete/fail
        await asyncio.sleep(0.1)

        # Check that successful task completed despite failing task
        stats = await app.call_service("task_manager.get_task_stats")
        assert stats["created"] >= 2
        assert stats["completed"] >= 1  # At least the successful task
        assert "success_task" in stats["completed_tasks"]
        assert stats["task_results"]["success_task"] == "completed_success_task"

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_task_cancellation_during_shutdown(self) -> None:
        """Test that long-running tasks are cancelled during plugin shutdown."""
        builder = PluggingerAppBuilder(app_name="task_cancellation_test")
        builder.include(TaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create a long-running task
        result = await app.call_service("task_manager.create_long_running_task",
                                      task_name="long_task", duration=5.0)
        assert result == "created_long_task_long_task"

        # Verify task was created
        stats = await app.call_service("task_manager.get_task_stats")
        assert stats["created"] >= 1
        assert stats["active"] >= 1

        # Get plugin reference before shutdown
        task_manager = cast(TaskManagerPlugin,
                          app.get_plugin_instance("task_cancellation_test:task_manager"))

        # Stop plugins (should cancel the long-running task)
        await app.stop_all_plugins()

        # Verify task was cancelled (plugin reference is still valid)
        final_stats = await task_manager.get_task_stats()

        # Task should either be cancelled or still pending (but not completed)
        assert final_stats["created"] >= 1
        # The long task should not have completed in the short time
        assert "long_task" not in final_stats["completed_tasks"]

    @pytest.mark.asyncio
    async def test_task_timeout_handling(self) -> None:
        """Test task timeout handling and waiting mechanisms."""
        builder = PluggingerAppBuilder(app_name="task_timeout_test")
        builder.include(TaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create a task and wait for it with timeout
        await app.call_service("task_manager.create_simple_task",
                              task_name="timeout_task", duration=0.05)

        # Wait with sufficient timeout (should succeed)
        result1 = await app.call_service("task_manager.wait_for_task",
                                        task_name="timeout_task", timeout=0.2)
        assert "task_timeout_task_result_completed_timeout_task" in result1

        # Create another task and wait with insufficient timeout
        await app.call_service("task_manager.create_simple_task",
                              task_name="slow_task", duration=0.5)

        # Wait with insufficient timeout (should timeout)
        result2 = await app.call_service("task_manager.wait_for_task",
                                        task_name="slow_task", timeout=0.1)
        assert result2 == "task_slow_task_timeout"

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_task_memory_management(self) -> None:
        """Test that completed tasks are properly cleaned up from memory."""
        builder = PluggingerAppBuilder(app_name="task_memory_test")
        builder.include(TaskManagerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create multiple short tasks
        task_count = 20
        for i in range(task_count):
            await app.call_service("task_manager.create_simple_task",
                                  task_name=f"memory_task_{i}", duration=0.01)

        # Wait for all tasks to complete
        await asyncio.sleep(0.1)

        # Check that tasks completed
        stats = await app.call_service("task_manager.get_task_stats")
        assert stats["created"] >= task_count
        assert stats["completed"] >= task_count
        assert stats["active"] == 0  # All tasks should be done

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_mixed_task_scenarios(self) -> None:
        """Test mixed scenarios with different task types and behaviors."""
        builder = PluggingerAppBuilder(app_name="task_mixed_test")
        builder.include(TaskManagerPlugin)
        builder.include(ConcurrentTaskManagerPlugin)
        builder.include(TaskLifecycleMonitorPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Create various types of tasks
        await app.call_service("task_manager.create_simple_task",
                              task_name="mixed_simple", duration=0.05)
        await app.call_service("task_manager.create_failing_task",
                              task_name="mixed_failing", error_message="Mixed test error")
        await app.call_service("concurrent_task_manager.create_concurrent_tasks",
                              count=3, base_duration=0.03)
        await app.call_service("task_lifecycle_monitor.create_monitored_task",
                              task_name="mixed_monitored", duration=0.04)

        # Wait for tasks to complete
        await asyncio.sleep(0.15)

        # Verify all plugins handled their tasks
        task_stats = await app.call_service("task_manager.get_task_stats")
        concurrent_stats = await app.call_service("concurrent_task_manager.get_concurrent_stats")
        lifecycle_events = await app.call_service("task_lifecycle_monitor.get_lifecycle_events")

        assert task_stats["created"] >= 2  # simple + failing
        assert task_stats["completed"] >= 1  # at least simple
        assert concurrent_stats["total_tasks"] >= 3
        assert concurrent_stats["completed"] >= 3
        assert "created_monitored_task_mixed_monitored" in lifecycle_events
        assert "task_mixed_monitored_completed" in lifecycle_events

        await app.stop_all_plugins()
