# tests/e2e/test_fractal_composition_integration.py

"""
End-to-End tests for Fractal Composition in Plugginger framework.

Tests comprehensive fractal composition functionality including AppPlugin nested structures,
event bridging between internal/external apps, configuration inheritance, and depth management.

Priority 3: Fractal Composition - Target: +8% coverage (47% → 55%)
"""

from __future__ import annotations

import asyncio
from typing import Any

import pytest
from pydantic import BaseModel

# Direct imports to avoid mypy issues with lazy loading
from plugginger.api.app import PluggingerAppInstance
from plugginger.api.app_plugin import AppPluginBase
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


# Simple Test Plugins for Fractal Composition
@plugin(name="simple_service", version="1.0.0")
class SimpleServicePlugin(PluginBase):
    """Simple service plugin for fractal testing."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.service_calls: list[str] = []
        self.events_received: list[str] = []

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the simple service."""
        pass

    @on_event("simple.*")
    async def handle_simple_event(self, event_data: dict[str, Any]) -> None:
        """Handle simple events."""
        event_id = event_data.get("id", "unknown")
        self.events_received.append(f"simple_event:{event_id}")

    @service()
    async def process_request(self, request_id: str) -> str:
        """Process a simple request."""
        result = f"processed_request_{request_id}"
        self.service_calls.append(request_id)

        # Emit an event
        await self.app.emit_event("simple.request_processed", {
            "id": request_id,
            "result": result
        })

        return result

    @service()
    async def get_stats(self) -> dict[str, Any]:
        """Get service statistics."""
        return {
            "service_calls": len(self.service_calls),
            "events_received": len(self.events_received),
            "calls_list": self.service_calls.copy(),
            "events_list": self.events_received.copy()
        }


@plugin(name="data_processor", version="1.0.0")
class DataProcessorPlugin(PluginBase):
    """Data processor plugin for fractal testing."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.processed_items: list[str] = []
        self.events_received: list[str] = []

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the data processor."""
        pass

    @on_event("data.*")
    async def handle_data_event(self, event_data: dict[str, Any]) -> None:
        """Handle data events."""
        item_id = event_data.get("item_id", "unknown")
        self.events_received.append(f"data_event:{item_id}")

    @service()
    async def process_data(self, item_id: str, data_size: int) -> str:
        """Process data item."""
        result = f"processed_data_{item_id}_size_{data_size}"
        self.processed_items.append(item_id)

        # Emit data event
        await self.app.emit_event("data.item_processed", {
            "item_id": item_id,
            "size": data_size,
            "result": result
        })

        return result

    @service()
    async def get_processing_stats(self) -> dict[str, Any]:
        """Get processing statistics."""
        return {
            "processed_items": len(self.processed_items),
            "events_received": len(self.events_received),
            "items_list": self.processed_items.copy(),
            "events_list": self.events_received.copy()
        }


# Simple AppPlugin for Fractal Composition Testing
@plugin(name="simple_app_plugin", version="1.0.0")
class SimpleAppPlugin(AppPluginBase):
    """Simple AppPlugin that creates an internal application."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app, **injected_dependencies)
        self.service_calls: list[str] = []
        self.events_bridged: list[str] = []

    def _configure_internal_app(self) -> None:
        """Configure the internal application."""
        # Import here to avoid circular import
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder(
            app_name=f"{self.__class__.__name__}_internal",
            parent_app_plugin_context=self,
            _current_depth=self.app._current_build_depth_for_sub_apps,
            _max_depth_from_config=self.app._max_build_depth_for_sub_apps
        )

        # Include simple plugins in the internal app
        builder.include(SimpleServicePlugin)
        builder.include(DataProcessorPlugin)

        self._internal_app = builder.build()

    def _configure_event_bridges(self) -> None:
        """Configure event bridges between internal and external apps."""
        # Bridge internal events to external app
        self.bridge_internal_event_pattern("simple.*", "internal_simple")
        self.bridge_internal_event_pattern("data.*", "internal_data")

    @service()
    async def process_external_request(self, request_id: str) -> str:
        """Process a request using internal services."""
        if not self._internal_app:
            return f"error:internal_app_not_available_for_{request_id}"

        # Call internal simple service
        simple_result = await self._internal_app.call_service(
            "simple_service.process_request",
            request_id
        )

        # Call internal data processor
        data_result = await self._internal_app.call_service(
            "data_processor.process_data",
            request_id,
            100
        )

        result = f"external_request_{request_id}_simple_{simple_result}_data_{data_result}"
        self.service_calls.append(request_id)

        return result

    @service()
    async def get_internal_stats(self) -> dict[str, Any]:
        """Get statistics from internal application."""
        if not self._internal_app:
            return {"error": "internal_app_not_available"}

        # Get stats from internal plugins
        simple_stats = await self._internal_app.call_service("simple_service.get_stats")
        data_stats = await self._internal_app.call_service("data_processor.get_processing_stats")

        return {
            "service_calls": len(self.service_calls),
            "events_bridged": len(self.events_bridged),
            "internal_simple": simple_stats,
            "internal_data": data_stats,
            "internal_services": self._internal_app.list_services() if self._internal_app else []
        }

class TestFractalCompositionIntegration:
    """Test suite for Fractal Composition Integration."""

    @pytest.mark.asyncio
    async def test_basic_fractal_app_creation_and_lifecycle(self) -> None:
        """Test basic fractal app creation and lifecycle management."""
        builder = PluggingerAppBuilder(app_name="fractal_basic_test")

        # Use include_app for AppPlugin
        builder.include_app(SimpleAppPlugin, "simple_app")

        app = builder.build()
        await app.start_all_plugins()

        # Verify the fractal app was created and configured
        stats = await app.call_service("simple_app.get_internal_stats")

        assert stats["service_calls"] == 0  # No service calls yet
        assert stats["events_bridged"] == 0  # No events bridged yet
        assert "internal_simple" in stats
        assert "internal_data" in stats

        # Verify internal services are available
        internal_services = stats["internal_services"]
        assert "simple_service.process_request" in internal_services
        assert "simple_service.get_stats" in internal_services
        assert "data_processor.process_data" in internal_services
        assert "data_processor.get_processing_stats" in internal_services

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_fractal_service_calls_and_delegation(self) -> None:
        """Test service calls delegation to internal fractal applications."""
        builder = PluggingerAppBuilder(app_name="fractal_service_test")

        # Include the fractal app plugin
        builder.include_app(SimpleAppPlugin, "simple_app")

        app = builder.build()
        await app.start_all_plugins()

        # Test fractal service calls
        result = await app.call_service(
            "simple_app.process_external_request",
            "test_request_123"
        )

        # Verify the result contains expected components
        assert "external_request_test_request_123" in result
        assert "simple_processed_request_test_request_123" in result
        assert "data_processed_data_test_request_123_size_100" in result

        # Verify service call statistics
        stats = await app.call_service("simple_app.get_internal_stats")
        assert stats["service_calls"] >= 1

        # Verify internal plugin statistics
        internal_simple = stats["internal_simple"]
        assert internal_simple["service_calls"] >= 1
        assert "test_request_123" in internal_simple["calls_list"]

        internal_data = stats["internal_data"]
        assert internal_data["processed_items"] >= 1
        assert "test_request_123" in internal_data["items_list"]

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_fractal_event_bridging(self) -> None:
        """Test event bridging between internal and external applications."""
        builder = PluggingerAppBuilder(app_name="fractal_event_test")

        # Include the fractal app plugin
        builder.include_app(SimpleAppPlugin, "simple_app")

        # Include a listener plugin in the outer app
        @plugin(name="external_listener", version="1.0.0")
        class ExternalListenerPlugin(PluginBase):
            def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
                super().__init__(app, **injected_dependencies)
                self.events_received: list[str] = []

            async def setup(self, plugin_config: BaseModel) -> None:
                pass

            @on_event("internal_simple.*")
            async def handle_internal_simple_event(self, event_data: dict[str, Any]) -> None:
                event_id = event_data.get("id", "unknown")
                self.events_received.append(f"internal_simple_event:{event_id}")

            @on_event("internal_data.*")
            async def handle_internal_data_event(self, event_data: dict[str, Any]) -> None:
                item_id = event_data.get("item_id", "unknown")
                self.events_received.append(f"internal_data_event:{item_id}")

            @service()
            async def get_external_events(self) -> list[str]:
                return self.events_received.copy()

        builder.include(ExternalListenerPlugin)

        app = builder.build()
        await app.start_all_plugins()

        # Trigger internal events by calling services
        await app.call_service("simple_app.process_external_request", "event_test_456")

        # Wait for event processing
        await asyncio.sleep(0.1)

        # Check that events were bridged to external app
        external_events = await app.call_service("external_listener.get_external_events")

        # Should have received bridged events from internal app
        assert len(external_events) >= 2  # At least simple and data events

        # Check for specific bridged events
        simple_events = [e for e in external_events if "internal_simple_event" in e]
        data_events = [e for e in external_events if "internal_data_event" in e]

        assert len(simple_events) >= 1
        assert len(data_events) >= 1

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_multiple_fractal_apps(self) -> None:
        """Test multiple fractal applications in the same outer app."""
        builder = PluggingerAppBuilder(app_name="multi_fractal_test")

        # Include multiple fractal app plugins
        builder.include_app(SimpleAppPlugin, "app1")
        builder.include_app(SimpleAppPlugin, "app2")

        app = builder.build()
        await app.start_all_plugins()

        # Test that both fractal apps work independently
        result1 = await app.call_service("app1.process_external_request", "multi_test_1")
        result2 = await app.call_service("app2.process_external_request", "multi_test_2")

        assert "external_request_multi_test_1" in result1
        assert "external_request_multi_test_2" in result2

        # Verify independent statistics
        stats1 = await app.call_service("app1.get_internal_stats")
        stats2 = await app.call_service("app2.get_internal_stats")

        assert stats1["service_calls"] >= 1
        assert stats2["service_calls"] >= 1

        # Each should have their own internal services
        assert len(stats1["internal_services"]) >= 4
        assert len(stats2["internal_services"]) >= 4

        await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_fractal_depth_management(self) -> None:
        """Test fractal depth management and limits."""
        # Test that fractal apps can be created within depth limits
        builder = PluggingerAppBuilder(app_name="depth_test", _max_depth_from_config=3)

        builder.include_app(SimpleAppPlugin, "depth_app")

        app = builder.build()
        await app.start_all_plugins()

        # Verify the fractal app was created successfully
        stats = await app.call_service("depth_app.get_internal_stats")
        assert "internal_simple" in stats
        assert "internal_data" in stats

        await app.stop_all_plugins()
