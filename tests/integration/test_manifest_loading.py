"""
Integration tests for YAML manifest loading during plugin registration.

These tests verify that the PluggingerAppBuilder correctly loads and validates
YAML manifests when manifest loading is enabled.
"""

from pathlib import Path

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.core.exceptions import PluginRegistrationError


@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for manifest loading tests."""
    pass


@plugin(name="another_plugin", version="2.0.0")
class AnotherTestPlugin(PluginBase):
    """Another test plugin for manifest loading tests."""
    pass


class TestManifestLoading:
    """Test manifest loading functionality in PluggingerAppBuilder."""

    def test_manifest_loading_disabled_by_default(self) -> None:
        """Test that manifest loading is disabled by default."""
        builder = PluggingerAppBuilder("test_app")

        # Should not attempt to load manifests
        builder.include(TestPlugin)

        # Should build successfully without manifests
        app = builder.build()
        assert app is not None

    def test_enable_manifest_loading(self) -> None:
        """Test enabling manifest loading."""
        builder = PluggingerAppBuilder("test_app")

        # Enable manifest loading
        result = builder.enable_manifest_loading()

        # Should return builder for chaining
        assert result is builder
        assert builder._manifest_loading_enabled is True
        assert builder._require_manifests is False

    def test_enable_manifest_loading_with_requirement(self) -> None:
        """Test enabling manifest loading with requirement."""
        builder = PluggingerAppBuilder("test_app")

        # Enable manifest loading with requirement
        result = builder.enable_manifest_loading(require_manifests=True)

        # Should return builder for chaining
        assert result is builder
        assert builder._manifest_loading_enabled is True
        assert builder._require_manifests is True

    def test_disable_manifest_loading(self) -> None:
        """Test disabling manifest loading."""
        builder = PluggingerAppBuilder("test_app")

        # Enable then disable
        builder.enable_manifest_loading(require_manifests=True)
        result = builder.disable_manifest_loading()

        # Should return builder for chaining
        assert result is builder
        assert builder._manifest_loading_enabled is False
        assert builder._require_manifests is False

    def test_manifest_loading_optional_no_manifest_found(self) -> None:
        """Test optional manifest loading when no manifest is found."""
        builder = PluggingerAppBuilder("test_app")
        builder.enable_manifest_loading(require_manifests=False)

        # Should succeed even without manifest
        builder.include(TestPlugin)
        app = builder.build()
        assert app is not None

    def test_manifest_loading_required_no_manifest_found(self) -> None:
        """Test required manifest loading when no manifest is found."""
        builder = PluggingerAppBuilder("test_app")
        builder.enable_manifest_loading(require_manifests=True)

        # Should fail when manifest is required but not found
        with pytest.raises(PluginRegistrationError, match="Required manifest file not found"):
            builder.include(TestPlugin)

    def test_manifest_loading_with_valid_manifest(self, tmp_path: Path) -> None:
        """Test manifest loading with a valid manifest file."""
        # Create a valid manifest file
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
  description: "Test plugin"
runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        # Create manifest file in plugin module directory
        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        # Mock the plugin module to point to our temp directory
        import sys
        import types

        # Create a fake module
        fake_module = types.ModuleType("fake_test_module")
        fake_module.__file__ = str(tmp_path / "fake_plugin.py")
        sys.modules["fake_test_module"] = fake_module

        # Create a plugin class in the fake module
        @plugin(name="test_plugin", version="1.0.0")
        class FakeTestPlugin(PluginBase):
            pass

        FakeTestPlugin.__module__ = "fake_test_module"

        try:
            builder = PluggingerAppBuilder("test_app")
            builder.enable_manifest_loading(require_manifests=True)

            # Should succeed with valid manifest
            builder.include(FakeTestPlugin)
            app = builder.build()
            assert app is not None

        finally:
            # Clean up
            if "fake_test_module" in sys.modules:
                del sys.modules["fake_test_module"]

    def test_manifest_loading_with_invalid_manifest(self, tmp_path: Path) -> None:
        """Test manifest loading with an invalid manifest file."""
        # Create an invalid manifest file
        manifest_content = """
invalid_yaml: [
  missing_closing_bracket
"""

        # Create manifest file in plugin module directory
        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        # Mock the plugin module to point to our temp directory
        import sys
        import types

        # Create a fake module
        fake_module = types.ModuleType("fake_invalid_module")
        fake_module.__file__ = str(tmp_path / "fake_plugin.py")
        sys.modules["fake_invalid_module"] = fake_module

        # Create a plugin class in the fake module
        @plugin(name="invalid_plugin", version="1.0.0")
        class FakeInvalidPlugin(PluginBase):
            pass

        FakeInvalidPlugin.__module__ = "fake_invalid_module"

        try:
            builder = PluggingerAppBuilder("test_app")
            builder.enable_manifest_loading(require_manifests=True)

            # Should fail with invalid manifest
            with pytest.raises(PluginRegistrationError, match="Invalid manifest file"):
                builder.include(FakeInvalidPlugin)

        finally:
            # Clean up
            if "fake_invalid_module" in sys.modules:
                del sys.modules["fake_invalid_module"]

    def test_manifest_loading_schema_validation_failure(self, tmp_path: Path) -> None:
        """Test manifest loading with schema validation failure."""
        # Create a manifest with invalid schema
        manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "invalid_name!"  # Invalid Python identifier
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
dependencies: []
services: []
event_listeners: []
generated_at: "2025-06-01T16:00:00Z"
generated_by: "test"
"""

        # Create manifest file in plugin module directory
        manifest_file = tmp_path / "manifest.yaml"
        manifest_file.write_text(manifest_content)

        # Mock the plugin module to point to our temp directory
        import sys
        import types

        # Create a fake module
        fake_module = types.ModuleType("fake_schema_module")
        fake_module.__file__ = str(tmp_path / "fake_plugin.py")
        sys.modules["fake_schema_module"] = fake_module

        # Create a plugin class in the fake module
        @plugin(name="schema_plugin", version="1.0.0")
        class FakeSchemaPlugin(PluginBase):
            pass

        FakeSchemaPlugin.__module__ = "fake_schema_module"

        try:
            builder = PluggingerAppBuilder("test_app")
            builder.enable_manifest_loading(require_manifests=True)

            # Should fail with schema validation error
            with pytest.raises(PluginRegistrationError, match="Invalid manifest file"):
                builder.include(FakeSchemaPlugin)

        finally:
            # Clean up
            if "fake_schema_module" in sys.modules:
                del sys.modules["fake_schema_module"]
