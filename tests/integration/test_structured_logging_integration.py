# tests/integration/test_structured_logging_integration.py

"""Integration tests for structured logging with PluggingerAppBuilder."""

from __future__ import annotations

import json
import logging
import tempfile
from io import StringIO
from pathlib import Path
from typing import Any

from plugginger.api.builder import Plugginger<PERSON><PERSON><PERSON>uilder
from plugginger.api.plugin import <PERSON>lug<PERSON><PERSON><PERSON>, plugin
from plugginger.logging import Log<PERSON><PERSON><PERSON><PERSON>


@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Simple test plugin for structured logging integration test."""

    def __init__(self, app: Any) -> None:
        """Initialize the test plugin."""
        super().__init__(app=app)


def test_structured_logging_integration() -> None:
    """Test that structured logging works end-to-end with PluggingerAppBuilder."""
    # Create a string buffer to capture log output
    log_buffer = StringIO()
    handler = logging.StreamHandler(log_buffer)
    handler.setLevel(logging.INFO)

    # Create logger and add handler
    logger = logging.getLogger("plugginger.builder")  # Use the correct logger name
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    try:
        # Create builder with structured logging enabled
        builder = PluggingerAppBuilder(
            app_name="test_structured_logging_app",
            enable_structured_logging=True
        )

        # Include a test plugin
        builder.include(TestPlugin)

        # Build the app (this should generate structured logs)
        app = builder.build()

        # Verify app was built successfully
        assert app is not None
        assert app.app_name == "test_structured_logging_app"

        # Get the log output
        log_output = log_buffer.getvalue()

        # Verify structured logs were generated
        assert log_output is not None
        assert len(log_output) > 0

        # Parse JSON log entries
        json_logs = []
        for line in log_output.split('\n'):
            if line.strip() and line.startswith('{'):
                try:
                    json_logs.append(json.loads(line.strip()))
                except json.JSONDecodeError:
                    continue

        # Verify we have structured log entries
        assert len(json_logs) > 0

        # Verify log structure
        for log_entry in json_logs:
            assert "timestamp" in log_entry
            assert "correlation_id" in log_entry
            assert "logger_name" in log_entry
            assert "event_type" in log_entry
            assert "level" in log_entry

        # Verify specific events were logged
        event_types = [log.get("event_type") for log in json_logs]
        assert "build_started" in event_types
        assert "plugin_discovery" in event_types
        assert "plugin_loaded" in event_types
        assert "build_completed" in event_types

        # Test log analysis
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            for log_entry in json_logs:
                f.write(json.dumps(log_entry) + '\n')
            temp_log_file = f.name

        try:
            # Parse logs with analyzer
            parsed_logs = LogAnalyzer.parse_structured_logs(temp_log_file)
            assert len(parsed_logs) == len(json_logs)

            # Generate performance report
            report = LogAnalyzer.generate_performance_report(parsed_logs)

            # Verify report structure
            assert "summary" in report
            assert "performance" in report
            assert "issues" in report
            assert "recommendations" in report

            # Verify summary data
            summary = report["summary"]
            assert summary["total_events"] > 0
            assert summary["performance_events"] >= 0

        finally:
            Path(temp_log_file).unlink()

    finally:
        # Clean up logger
        logger.removeHandler(handler)


def test_structured_logging_disabled() -> None:
    """Test that structured logging can be disabled."""
    # Create a string buffer to capture log output
    log_buffer = StringIO()
    handler = logging.StreamHandler(log_buffer)
    handler.setLevel(logging.INFO)

    # Create logger and add handler
    logger = logging.getLogger("plugginger.builder")  # Use the correct logger name
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    try:
        # Create builder with structured logging disabled
        builder = PluggingerAppBuilder(
            app_name="test_no_structured_logging_app",
            enable_structured_logging=False
        )

        # Include a test plugin
        builder.include(TestPlugin)

        # Build the app
        app = builder.build()

        # Verify app was built successfully
        assert app is not None

        # Get the log output
        log_output = log_buffer.getvalue()

        # Verify traditional logs were generated (not JSON)
        assert log_output is not None
        assert len(log_output) > 0

        # Verify no JSON logs were generated
        json_logs = []
        for line in log_output.split('\n'):
            if line.strip() and line.startswith('{'):
                try:
                    json_logs.append(json.loads(line.strip()))
                except json.JSONDecodeError:
                    continue

        # Should have no structured JSON logs
        assert len(json_logs) == 0

    finally:
        # Clean up logger
        logger.removeHandler(handler)
