# tests/integration/test_centralized_error_handling_integration.py

"""
Integration tests for the centralized error handling system.

Tests the integration of ErrorHandler with PluggingerAppBuilder and
demonstrates enhanced error reporting in real-world scenarios.
"""

from __future__ import annotations

from typing import TYPE_CHECKING

import pytest

from plugginger.api.builder import Plugginger<PERSON>pp<PERSON>uilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.core.exceptions import (
    ErrorCategory,
    PluggingerError,
    PluginRegistrationError,
)

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance


@plugin(name="valid_test_plugin", version="1.0.0")
class ValidTestPlugin(PluginBase):
    """A valid test plugin for integration testing."""

    def __init__(self, app: PluggingerAppInstance) -> None:
        """Initialize the valid test plugin."""
        super().__init__(app=app)


class InvalidTestPlugin(PluginBase):
    """An invalid test plugin without @plugin decorator."""

    def __init__(self, app: PluggingerAppInstance) -> None:
        """Initialize the invalid test plugin."""
        super().__init__(app=app)


@plugin(name="failing_test_plugin", version="1.0.0")
class FailingTestPlugin(PluginBase):
    """A test plugin that fails during initialization."""

    def __init__(self, app: PluggingerAppInstance) -> None:
        """Initialize the failing test plugin."""
        super().__init__(app=app)
        raise RuntimeError("Simulated plugin initialization failure")


class TestCentralizedErrorHandlingIntegration:
    """Integration tests for centralized error handling."""

    def test_successful_plugin_inclusion(self) -> None:
        """Test that successful plugin inclusion works without errors."""
        builder = PluggingerAppBuilder("test_app")
        
        # This should work without any errors
        builder.include(ValidTestPlugin)
        
        # Verify plugin was registered
        registered_plugins = builder.get_registered_plugin_classes()
        assert "valid_test_plugin" in registered_plugins
        assert registered_plugins["valid_test_plugin"] == ValidTestPlugin

    def test_plugin_registration_error_handling(self) -> None:
        """Test enhanced error handling for plugin registration failures."""
        builder = PluggingerAppBuilder("test_app")
        
        with pytest.raises(PluginRegistrationError) as exc_info:
            builder.include(InvalidTestPlugin)  # type: ignore
        
        error = exc_info.value
        
        # Verify enhanced error information
        assert isinstance(error, PluggingerError)
        assert error.error_code == "PLUGIN_REGISTRATION_FAILED"
        assert error.category == ErrorCategory.PLUGIN_ERROR
        assert "InvalidTestPlugin" in str(error)
        assert error.suggestion is not None
        assert "PluginBase" in error.suggestion

    def test_plugin_initialization_error_handling(self) -> None:
        """Test enhanced error handling for plugin initialization failures."""
        builder = PluggingerAppBuilder("test_app")
        builder.include(FailingTestPlugin)
        
        with pytest.raises(PluggingerError) as exc_info:
            builder.build()
        
        error = exc_info.value
        
        # Verify enhanced error information
        assert isinstance(error, PluggingerError)
        # The error context depends on where the error occurs in the build process
        # For plugin instantiation errors, the context may not include "app_build"
        # but should include plugin-related information
        assert error.context is not None

        # Verify that ErrorHandler was called during build process
        error_summary = builder._error_handler.get_error_summary()
        assert error_summary["total_errors"] > 0

    def test_error_handler_integration_with_builder(self) -> None:
        """Test that ErrorHandler is properly integrated with PluggingerAppBuilder."""
        builder = PluggingerAppBuilder("test_app")
        
        # Verify error handler is initialized
        assert hasattr(builder, '_error_handler')
        assert builder._error_handler is not None
        
        # Test error handler functionality through builder
        try:
            builder.include(InvalidTestPlugin)  # type: ignore
        except PluggingerError:
            pass  # Expected
        
        # Verify error was recorded in error handler
        error_summary = builder._error_handler.get_error_summary()
        assert error_summary["total_errors"] > 0
        assert "plugin_error" in error_summary["by_category"]

    def test_multiple_errors_accumulation(self) -> None:
        """Test that multiple errors are properly accumulated and analyzed."""
        builder = PluggingerAppBuilder("test_app")
        
        # Try to include multiple invalid plugins
        invalid_plugins = [InvalidTestPlugin] * 3
        
        for _i, plugin_class in enumerate(invalid_plugins):
            try:
                builder.include(plugin_class)  # type: ignore
            except PluggingerError:
                pass  # Expected
        
        # Check error accumulation
        error_summary = builder._error_handler.get_error_summary()
        assert error_summary["total_errors"] == 3
        assert error_summary["by_category"]["plugin_error"] == 3
        
        # Check suggestions are generated
        suggestions = error_summary["suggestions"]
        assert len(suggestions) > 0
        assert any("plugin" in suggestion.lower() for suggestion in suggestions)

    def test_error_context_preservation(self) -> None:
        """Test that error context is properly preserved and enhanced."""
        builder = PluggingerAppBuilder("test_app")
        
        # Add a valid plugin first
        builder.include(ValidTestPlugin)
        
        # Try to add an invalid plugin
        with pytest.raises(PluggingerError) as exc_info:
            builder.include(InvalidTestPlugin)  # type: ignore
        
        error = exc_info.value
        
        # Verify context includes information about existing plugins
        assert error.context is not None
        assert "current_plugins" in error.context
        assert "valid_test_plugin" in error.context["current_plugins"]

    def test_error_categorization_accuracy(self) -> None:
        """Test that errors are correctly categorized."""
        builder = PluggingerAppBuilder("test_app")
        
        # Test plugin error categorization
        with pytest.raises(PluggingerError) as exc_info:
            builder.include(InvalidTestPlugin)  # type: ignore
        
        assert exc_info.value.category == ErrorCategory.PLUGIN_ERROR
        
        # Test build error categorization
        builder_with_failing_plugin = PluggingerAppBuilder("test_app")
        builder_with_failing_plugin.include(FailingTestPlugin)
        
        with pytest.raises(PluggingerError) as exc_info:
            builder_with_failing_plugin.build()
        
        # Build errors should be categorized appropriately
        assert exc_info.value.category in [
            ErrorCategory.RUNTIME_ERROR,
            ErrorCategory.PLUGIN_ERROR,
            ErrorCategory.FRAMEWORK_BUG
        ]

    def test_error_suggestions_quality(self) -> None:
        """Test that error suggestions are helpful and actionable."""
        builder = PluggingerAppBuilder("test_app")
        
        with pytest.raises(PluggingerError) as exc_info:
            builder.include(InvalidTestPlugin)  # type: ignore
        
        error = exc_info.value
        
        # Verify suggestion quality
        assert error.suggestion is not None
        assert len(error.suggestion) > 10  # Should be substantial
        assert any(keyword in error.suggestion.lower() for keyword in [
            "plugin", "decorator", "@plugin", "inherit", "pluginbase"
        ])

    def test_structured_error_data_format(self) -> None:
        """Test that errors can be converted to structured format for AI analysis."""
        builder = PluggingerAppBuilder("test_app")
        
        with pytest.raises(PluggingerError) as exc_info:
            builder.include(InvalidTestPlugin)  # type: ignore
        
        error = exc_info.value
        error_dict = error.to_dict()
        
        # Verify structured format
        required_fields = [
            "error_code", "category", "message", "context",
            "suggestion", "timestamp"
        ]
        
        for field in required_fields:
            assert field in error_dict
        
        # Verify data types
        assert isinstance(error_dict["error_code"], str)
        assert isinstance(error_dict["category"], str)
        assert isinstance(error_dict["message"], str)
        assert isinstance(error_dict["context"], dict)
        assert isinstance(error_dict["timestamp"], str)

    def test_error_handler_summary_completeness(self) -> None:
        """Test that error handler summary provides comprehensive information."""
        builder = PluggingerAppBuilder("test_app")
        
        # Generate some errors
        for _ in range(2):
            try:
                builder.include(InvalidTestPlugin)  # type: ignore
            except PluggingerError:
                pass
        
        summary = builder._error_handler.get_error_summary()
        
        # Verify summary completeness
        required_summary_fields = [
            "total_errors", "by_category", "recent_errors",
            "suggestions", "most_common_errors"
        ]
        
        for field in required_summary_fields:
            assert field in summary
        
        # Verify data quality
        assert summary["total_errors"] == 2
        assert isinstance(summary["by_category"], dict)
        assert isinstance(summary["recent_errors"], list)
        assert isinstance(summary["suggestions"], list)
        assert isinstance(summary["most_common_errors"], list)

    def test_error_handler_clear_functionality(self) -> None:
        """Test that error handler can be cleared properly."""
        builder = PluggingerAppBuilder("test_app")
        
        # Generate an error
        try:
            builder.include(InvalidTestPlugin)  # type: ignore
        except PluggingerError:
            pass
        
        # Verify error was recorded
        assert builder._error_handler.get_error_summary()["total_errors"] > 0
        
        # Clear and verify
        builder._error_handler.clear_history()
        summary = builder._error_handler.get_error_summary()
        
        assert summary["total_errors"] == 0
        assert len(summary["recent_errors"]) == 0
        assert len(summary["by_category"]) == 0
