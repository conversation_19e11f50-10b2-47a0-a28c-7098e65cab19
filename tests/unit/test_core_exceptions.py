# tests/unit/test_core_exceptions.py

"""
Unit tests for plugginger.core.exceptions module.

Tests all exception classes to ensure they have the correct inheritance,
attributes, and behavior.
"""

from __future__ import annotations

from collections.abc import Sequence

from plugginger.core.exceptions import (
    AppPluginError,
    BackgroundTaskError,
    BackgroundTaskQueueError,
    CircularDependencyError,
    ConfigurationError,
    DependencyError,
    DependencyResolutionError,
    DependencyVersionConflictError,
    DIContainerError,
    EventDefinitionError,
    EventListenerError,
    EventListenerTimeoutError,
    EventListenerUnhandledError,
    EventPayloadValidationError,
    FreezeConflictError,
    LockfileError,
    MissingDependencyError,
    MissingTypeAnnotationForDIError,
    PluggingerError,
    PluginRegistrationError,
    PluginTeardownError,
    ServiceDefinitionError,
    ServiceExecutionError,
    ServiceNameConflictError,
    ServiceNotFoundError,
    ValidationError,
)


class TestPluggingerError:
    """Test the base PluggingerError class."""

    def test_inheritance(self) -> None:
        """Test that PluggingerError inherits from Exception."""
        assert issubclass(PluggingerError, Exception)

    def test_initialization(self) -> None:
        """Test PluggingerError initialization."""
        message = "Test error message"
        error = PluggingerError(message)
        assert str(error) == message

    def test_initialization_with_args(self) -> None:
        """Test PluggingerError initialization with additional args."""
        message = "Test error"
        additional_arg = "extra info"
        error = PluggingerError(message, additional_arg)
        assert str(error) == f"('{message}', '{additional_arg}')"


class TestConfigurationError:
    """Test ConfigurationError class."""

    def test_inheritance(self) -> None:
        """Test that ConfigurationError inherits from PluggingerError."""
        assert issubclass(ConfigurationError, PluggingerError)

    def test_initialization_basic(self) -> None:
        """Test basic ConfigurationError initialization."""
        message = "Configuration validation failed"
        error = ConfigurationError(message)
        assert str(error) == message
        assert error.validation_errors is None

    def test_initialization_with_validation_errors(self) -> None:
        """Test ConfigurationError initialization with validation errors."""
        message = "Configuration validation failed"
        validation_errors = {"field": "error details"}
        error = ConfigurationError(message, validation_errors=validation_errors)
        assert str(error) == message
        assert error.validation_errors == validation_errors

    def test_validation_errors_attribute(self) -> None:
        """Test that validation_errors attribute is properly set."""
        error = ConfigurationError("test")
        assert hasattr(error, "validation_errors")
        assert error.validation_errors is None


class TestDependencyErrors:
    """Test dependency-related exception classes."""

    def test_dependency_error_inheritance(self) -> None:
        """Test that DependencyError inherits from PluggingerError."""
        assert issubclass(DependencyError, PluggingerError)

    def test_missing_dependency_error_inheritance(self) -> None:
        """Test that MissingDependencyError inherits from DependencyError."""
        assert issubclass(MissingDependencyError, DependencyError)

    def test_circular_dependency_error_inheritance(self) -> None:
        """Test that CircularDependencyError inherits from DependencyError."""
        assert issubclass(CircularDependencyError, DependencyError)

    def test_dependency_version_conflict_error_inheritance(self) -> None:
        """Test that DependencyVersionConflictError inherits from DependencyError."""
        assert issubclass(DependencyVersionConflictError, DependencyError)

    def test_missing_type_annotation_error_inheritance(self) -> None:
        """Test that MissingTypeAnnotationForDIError inherits from DependencyError."""
        assert issubclass(MissingTypeAnnotationForDIError, DependencyError)

    def test_di_container_error_inheritance(self) -> None:
        """Test that DIContainerError inherits from DependencyError."""
        assert issubclass(DIContainerError, DependencyError)

    def test_dependency_resolution_error_inheritance(self) -> None:
        """Test that DependencyResolutionError inherits from DIContainerError."""
        assert issubclass(DependencyResolutionError, DIContainerError)


class TestMissingTypeAnnotationForDIError:
    """Test MissingTypeAnnotationForDIError class."""

    def test_initialization_basic(self) -> None:
        """Test basic initialization."""
        message = "Missing type annotation"
        error = MissingTypeAnnotationForDIError(message)
        assert str(error) == message
        assert error.class_name is None
        assert error.parameter_name is None

    def test_initialization_with_details(self) -> None:
        """Test initialization with class and parameter details."""
        message = "Missing type annotation"
        class_name = "TestClass"
        parameter_name = "test_param"
        error = MissingTypeAnnotationForDIError(
            message, class_name=class_name, parameter_name=parameter_name
        )
        assert str(error) == message
        assert error.class_name == class_name
        assert error.parameter_name == parameter_name

    def test_attributes_exist(self) -> None:
        """Test that required attributes exist."""
        error = MissingTypeAnnotationForDIError("test")
        assert hasattr(error, "class_name")
        assert hasattr(error, "parameter_name")


class TestDependencyResolutionError:
    """Test DependencyResolutionError class."""

    def test_initialization_basic(self) -> None:
        """Test basic initialization."""
        message = "Cannot resolve dependency"
        error = DependencyResolutionError(message)
        assert str(error) == message
        assert error.target_class is None
        assert error.dependency_type is None
        assert error.parameter_name is None

    def test_initialization_with_details(self) -> None:
        """Test initialization with full details."""
        message = "Cannot resolve dependency"
        target_class = "TargetClass"
        dependency_type = "DependencyType"
        parameter_name = "param"
        error = DependencyResolutionError(
            message,
            target_class=target_class,
            dependency_type=dependency_type,
            parameter_name=parameter_name,
        )
        assert str(error) == message
        assert error.target_class == target_class
        assert error.dependency_type == dependency_type
        assert error.parameter_name == parameter_name

    def test_attributes_exist(self) -> None:
        """Test that required attributes exist."""
        error = DependencyResolutionError("test")
        assert hasattr(error, "target_class")
        assert hasattr(error, "dependency_type")
        assert hasattr(error, "parameter_name")


class TestServiceErrors:
    """Test service-related exception classes."""

    def test_service_definition_error_inheritance(self) -> None:
        """Test that ServiceDefinitionError inherits from PluggingerError."""
        assert issubclass(ServiceDefinitionError, PluggingerError)

    def test_service_name_conflict_error_inheritance(self) -> None:
        """Test that ServiceNameConflictError inherits from ServiceDefinitionError."""
        assert issubclass(ServiceNameConflictError, ServiceDefinitionError)

    def test_service_not_found_error_inheritance(self) -> None:
        """Test that ServiceNotFoundError inherits from PluggingerError."""
        assert issubclass(ServiceNotFoundError, PluggingerError)

    def test_service_execution_error_inheritance(self) -> None:
        """Test that ServiceExecutionError inherits from PluggingerError."""
        assert issubclass(ServiceExecutionError, PluggingerError)


class TestEventErrors:
    """Test event-related exception classes."""

    def test_event_definition_error_inheritance(self) -> None:
        """Test that EventDefinitionError inherits from PluggingerError."""
        assert issubclass(EventDefinitionError, PluggingerError)

    def test_event_listener_error_inheritance(self) -> None:
        """Test that EventListenerError inherits from PluggingerError."""
        assert issubclass(EventListenerError, PluggingerError)

    def test_event_listener_unhandled_error_inheritance(self) -> None:
        """Test that EventListenerUnhandledError inherits from EventListenerError."""
        assert issubclass(EventListenerUnhandledError, EventListenerError)

    def test_event_listener_timeout_error_inheritance(self) -> None:
        """Test that EventListenerTimeoutError inherits from EventListenerError."""
        assert issubclass(EventListenerTimeoutError, EventListenerError)

    def test_event_payload_validation_error_inheritance(self) -> None:
        """Test that EventPayloadValidationError inherits from EventDefinitionError."""
        assert issubclass(EventPayloadValidationError, EventDefinitionError)


class TestEventPayloadValidationError:
    """Test EventPayloadValidationError class."""

    def test_initialization_basic(self) -> None:
        """Test basic initialization."""
        message = "Payload validation failed"
        error = EventPayloadValidationError(message)
        assert str(error) == message
        assert error.event_type is None
        assert error.listener_name is None
        assert error.validation_errors is None

    def test_initialization_with_details(self) -> None:
        """Test initialization with full details."""
        message = "Payload validation failed"
        event_type = "test.event"
        listener_name = "test_listener"
        validation_errors = {"field": "error"}
        error = EventPayloadValidationError(
            message,
            event_type=event_type,
            listener_name=listener_name,
            validation_errors=validation_errors,
        )
        assert str(error) == message
        assert error.event_type == event_type
        assert error.listener_name == listener_name
        assert error.validation_errors == validation_errors

    def test_attributes_exist(self) -> None:
        """Test that required attributes exist."""
        error = EventPayloadValidationError("test")
        assert hasattr(error, "event_type")
        assert hasattr(error, "listener_name")
        assert hasattr(error, "validation_errors")


class TestPluginTeardownError:
    """Test PluginTeardownError class."""

    def test_inheritance(self) -> None:
        """Test that PluginTeardownError inherits from PluggingerError."""
        assert issubclass(PluginTeardownError, PluggingerError)

    def test_initialization_basic(self) -> None:
        """Test basic initialization."""
        message = "Plugin teardown failed"
        error = PluginTeardownError(message)
        assert str(error) == message
        assert error.individual_errors == []

    def test_initialization_with_individual_errors(self) -> None:
        """Test initialization with individual errors."""
        message = "Plugin teardown failed"
        individual_errors = [ValueError("error1"), RuntimeError("error2")]
        error = PluginTeardownError(message, individual_errors=individual_errors)
        assert str(error) == message
        assert error.individual_errors == individual_errors

    def test_individual_errors_attribute_type(self) -> None:
        """Test that individual_errors attribute has correct type."""
        error = PluginTeardownError("test")
        assert hasattr(error, "individual_errors")
        assert isinstance(error.individual_errors, Sequence)


class TestBackgroundTaskErrors:
    """Test background task related exception classes."""

    def test_background_task_error_inheritance(self) -> None:
        """Test that BackgroundTaskError inherits from PluggingerError."""
        assert issubclass(BackgroundTaskError, PluggingerError)

    def test_background_task_queue_error_inheritance(self) -> None:
        """Test that BackgroundTaskQueueError inherits from BackgroundTaskError."""
        assert issubclass(BackgroundTaskQueueError, BackgroundTaskError)


class TestLockfileErrors:
    """Test lockfile-related exception classes."""

    def test_lockfile_error_inheritance(self) -> None:
        """Test that LockfileError inherits from PluggingerError."""
        assert issubclass(LockfileError, PluggingerError)

    def test_freeze_conflict_error_inheritance(self) -> None:
        """Test that FreezeConflictError inherits from LockfileError."""
        assert issubclass(FreezeConflictError, LockfileError)


class TestOtherErrors:
    """Test other exception classes."""

    def test_plugin_registration_error_inheritance(self) -> None:
        """Test that PluginRegistrationError inherits from PluggingerError."""
        assert issubclass(PluginRegistrationError, PluggingerError)

    def test_validation_error_inheritance(self) -> None:
        """Test that ValidationError inherits from PluggingerError."""
        assert issubclass(ValidationError, PluggingerError)

    def test_app_plugin_error_inheritance(self) -> None:
        """Test that AppPluginError inherits from PluggingerError."""
        assert issubclass(AppPluginError, PluggingerError)


class TestExceptionHierarchy:
    """Test the overall exception hierarchy."""

    def test_all_exceptions_inherit_from_plugginger_error(self) -> None:
        """Test that all custom exceptions inherit from PluggingerError."""
        exception_classes = [
            ConfigurationError,
            PluginRegistrationError,
            DependencyError,
            MissingDependencyError,
            CircularDependencyError,
            DependencyVersionConflictError,
            MissingTypeAnnotationForDIError,
            DIContainerError,
            DependencyResolutionError,
            ServiceDefinitionError,
            ServiceNameConflictError,
            ServiceNotFoundError,
            ServiceExecutionError,
            EventDefinitionError,
            EventListenerError,
            EventListenerUnhandledError,
            EventListenerTimeoutError,
            EventPayloadValidationError,
            PluginTeardownError,
            BackgroundTaskError,
            BackgroundTaskQueueError,
            LockfileError,
            FreezeConflictError,
            ValidationError,
            AppPluginError,
        ]

        for exception_class in exception_classes:
            assert issubclass(exception_class, PluggingerError), f"{exception_class.__name__} should inherit from PluggingerError"

    def test_plugginger_error_inherits_from_exception(self) -> None:
        """Test that PluggingerError inherits from Exception."""
        assert issubclass(PluggingerError, Exception)

    def test_exception_names_are_descriptive(self) -> None:
        """Test that exception names follow naming conventions."""
        exception_classes = [
            ConfigurationError,
            PluginRegistrationError,
            DependencyError,
            MissingDependencyError,
            CircularDependencyError,
            DependencyVersionConflictError,
            MissingTypeAnnotationForDIError,
            DIContainerError,
            DependencyResolutionError,
            ServiceDefinitionError,
            ServiceNameConflictError,
            ServiceNotFoundError,
            ServiceExecutionError,
            EventDefinitionError,
            EventListenerError,
            EventListenerUnhandledError,
            EventListenerTimeoutError,
            EventPayloadValidationError,
            PluginTeardownError,
            BackgroundTaskError,
            BackgroundTaskQueueError,
            LockfileError,
            FreezeConflictError,
            ValidationError,
            AppPluginError,
        ]

        for exception_class in exception_classes:
            name = exception_class.__name__
            assert name.endswith("Error"), f"{name} should end with 'Error'"
            assert len(name) > 5, f"{name} should be descriptive"
