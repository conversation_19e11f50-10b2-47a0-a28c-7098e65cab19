# tests/unit/test_core_error_handling.py

"""
Unit tests for the centralized error handling system.

Tests the enhanced PluggingerError class, ErrorCategory enum, and ErrorHandler
functionality including error categorization, context handling, and AI-Agent
compatible error reporting.
"""

from __future__ import annotations

import logging
from datetime import datetime
from unittest.mock import Mock

import pytest

from plugginger.core.error_handler import <PERSON>rrorHandler
from plugginger.core.exceptions import (
    ConfigurationError,
    ErrorCategory,
    MissingDependencyError,
    PluggingerError,
    PluginRegistrationError,
)


class TestErrorCategory:
    """Test the ErrorCategory enum."""

    def test_error_category_values(self) -> None:
        """Test that <PERSON>rror<PERSON>ategor<PERSON> has expected values."""
        assert ErrorCategory.USER_INPUT.value == "user_input"
        assert ErrorCategory.PLUGIN_ERROR.value == "plugin_error"
        assert ErrorCategory.FRAMEWORK_BUG.value == "framework_bug"
        assert ErrorCategory.DEPENDENCY_ERROR.value == "dependency"
        assert ErrorCategory.CONFIGURATION.value == "configuration"
        assert ErrorCategory.RUNTIME_ERROR.value == "runtime"


class TestPluggingerError:
    """Test the enhanced PluggingerError base class."""

    def test_basic_error_creation(self) -> None:
        """Test basic error creation with minimal parameters."""
        error = PluggingerError("Test error message")

        assert str(error) == "Test error message"
        assert error.error_code == "GENERIC_ERROR"
        assert error.category == ErrorCategory.RUNTIME_ERROR
        assert error.context == {}
        assert error.suggestion is None
        assert error.related_docs is None
        assert isinstance(error.timestamp, datetime)

    def test_enhanced_error_creation(self) -> None:
        """Test error creation with all enhanced parameters."""
        context = {"plugin_name": "test_plugin", "operation": "build"}

        error = PluggingerError(
            "Enhanced error message",
            error_code="TEST_ERROR",
            category=ErrorCategory.PLUGIN_ERROR,
            context=context,
            suggestion="Try restarting the plugin",
            related_docs="https://docs.example.com/errors"
        )

        assert str(error) == "Enhanced error message"
        assert error.error_code == "TEST_ERROR"
        assert error.category == ErrorCategory.PLUGIN_ERROR
        assert error.context == context
        assert error.suggestion == "Try restarting the plugin"
        assert error.related_docs == "https://docs.example.com/errors"

    def test_to_dict_conversion(self) -> None:
        """Test conversion of error to dictionary format."""
        context = {"plugin_name": "test_plugin"}

        error = PluggingerError(
            "Test error",
            error_code="TEST_ERROR",
            category=ErrorCategory.USER_INPUT,
            context=context,
            suggestion="Check input"
        )

        error_dict = error.to_dict()

        assert error_dict["error_code"] == "TEST_ERROR"
        assert error_dict["category"] == "user_input"
        assert error_dict["message"] == "Test error"
        assert error_dict["context"] == context
        assert error_dict["suggestion"] == "Check input"
        assert "timestamp" in error_dict
        assert isinstance(error_dict["timestamp"], str)


class TestEnhancedExceptions:
    """Test enhanced exception classes with new context features."""

    def test_configuration_error_enhanced(self) -> None:
        """Test ConfigurationError with enhanced context."""
        error = ConfigurationError(
            "Invalid configuration",
            plugin_name="test_plugin",
            config_key="database_url",
            validation_errors=["Field required"]
        )

        assert error.error_code == "CONFIGURATION_ERROR"
        assert error.category == ErrorCategory.CONFIGURATION
        assert error.context["plugin_name"] == "test_plugin"
        assert error.context["config_key"] == "database_url"
        assert error.context["validation_errors"] == ["Field required"]
        assert error.suggestion is not None and "Check configuration format" in error.suggestion

    def test_plugin_registration_error_enhanced(self) -> None:
        """Test PluginRegistrationError with enhanced context."""
        error = PluginRegistrationError(
            "Plugin registration failed",
            plugin_name="test_plugin",
            plugin_class="TestPlugin"
        )

        assert error.error_code == "PLUGIN_REGISTRATION_FAILED"
        assert error.category == ErrorCategory.PLUGIN_ERROR
        assert error.context["plugin_name"] == "test_plugin"
        assert error.context["plugin_class"] == "TestPlugin"
        assert error.suggestion is not None and "PluginBase" in error.suggestion

    def test_missing_dependency_error_enhanced(self) -> None:
        """Test MissingDependencyError with enhanced context."""
        missing_deps = ["logger", "database"]

        error = MissingDependencyError(
            "Missing dependencies",
            plugin_name="test_plugin",
            missing_dependencies=missing_deps
        )

        assert error.error_code == "MISSING_DEPENDENCY"
        assert error.category == ErrorCategory.DEPENDENCY_ERROR
        assert error.context["plugin_name"] == "test_plugin"
        assert error.context["missing_dependencies"] == missing_deps
        assert error.suggestion is not None and "logger, database" in error.suggestion


class TestErrorHandler:
    """Test the ErrorHandler class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.logger = Mock(spec=logging.Logger)
        self.error_handler = ErrorHandler(self.logger)

    def test_error_handler_initialization(self) -> None:
        """Test ErrorHandler initialization."""
        assert self.error_handler.logger is self.logger
        assert self.error_handler.error_history == []
        assert len(self.error_handler._error_counts) == 0

    def test_handle_plugginger_error(self) -> None:
        """Test handling of PluggingerError instances."""
        error = PluggingerError(
            "Test error",
            error_code="TEST_ERROR",
            category=ErrorCategory.USER_INPUT
        )

        with pytest.raises(PluggingerError) as exc_info:
            self.error_handler.handle_error(error, "test_operation")

        # Should re-raise the same error
        assert exc_info.value is error

        # Should log the error
        self.logger.warning.assert_called_once()

        # Should store in history
        assert len(self.error_handler.error_history) == 1
        stored_error = self.error_handler.error_history[0]
        assert stored_error["operation"] == "test_operation"
        assert stored_error["error_code"] == "TEST_ERROR"

    def test_handle_generic_error(self) -> None:
        """Test handling of generic Python exceptions."""
        generic_error = ValueError("Invalid value")
        context = {"key": "value"}

        with pytest.raises(PluggingerError) as exc_info:
            self.error_handler.handle_error(generic_error, "test_operation", context)

        # Should wrap in PluggingerError
        wrapped_error = exc_info.value
        assert isinstance(wrapped_error, PluggingerError)
        assert wrapped_error.error_code == "GENERIC_ERROR"
        assert wrapped_error.category == ErrorCategory.RUNTIME_ERROR
        assert "ValueError" in wrapped_error.context["original_error_type"]

        # Should store in history
        assert len(self.error_handler.error_history) == 1

    def test_error_categorization(self) -> None:
        """Test automatic categorization of generic errors."""
        # Configuration error
        config_error = ValueError("Invalid config")
        with pytest.raises(PluggingerError):
            self.error_handler.handle_error(config_error, "config_validation")

        # Import error
        import_error = ImportError("Module not found")
        with pytest.raises(PluggingerError):
            self.error_handler.handle_error(import_error, "plugin_loading")

        # Check categorization in history
        assert len(self.error_handler.error_history) == 2
        assert self.error_handler.error_history[0]["category"] == "configuration"
        assert self.error_handler.error_history[1]["category"] == "dependency"

    def test_error_summary_generation(self) -> None:
        """Test error summary generation."""
        # Add some errors
        errors = [
            PluggingerError("Error 1", category=ErrorCategory.USER_INPUT),
            PluggingerError("Error 2", category=ErrorCategory.USER_INPUT),
            PluggingerError("Error 3", category=ErrorCategory.DEPENDENCY_ERROR),
        ]

        for i, error in enumerate(errors):
            try:
                self.error_handler.handle_error(error, f"operation_{i}")
            except PluggingerError:
                pass  # Expected

        summary = self.error_handler.get_error_summary()

        assert summary["total_errors"] == 3
        assert summary["by_category"]["user_input"] == 2
        assert summary["by_category"]["dependency"] == 1
        assert len(summary["recent_errors"]) == 3
        assert len(summary["suggestions"]) > 0

    def test_suggestion_generation(self) -> None:
        """Test suggestion generation for different error types."""
        # Test import error suggestion
        import_error = ImportError("No module named 'test'")
        with pytest.raises(PluggingerError):
            self.error_handler.handle_error(import_error, "plugin_loading")

        summary = self.error_handler.get_error_summary()
        suggestions = summary["suggestions"]

        # Should have dependency-related suggestion
        assert any("dependencies" in suggestion.lower() for suggestion in suggestions)

    def test_clear_history(self) -> None:
        """Test clearing error history."""
        # Add an error
        error = PluggingerError("Test error")
        try:
            self.error_handler.handle_error(error, "test_operation")
        except PluggingerError:
            pass

        assert len(self.error_handler.error_history) == 1

        # Clear history
        self.error_handler.clear_history()

        assert len(self.error_handler.error_history) == 0
        assert len(self.error_handler._error_counts) == 0

    def test_most_common_errors(self) -> None:
        """Test most common errors tracking."""
        # Add multiple errors with same code
        for _ in range(3):
            error = PluggingerError("Test error", error_code="COMMON_ERROR")
            try:
                self.error_handler.handle_error(error, "test_operation")
            except PluggingerError:
                pass

        # Add different error
        error = PluggingerError("Other error", error_code="RARE_ERROR")
        try:
            self.error_handler.handle_error(error, "test_operation")
        except PluggingerError:
            pass

        summary = self.error_handler.get_error_summary()
        most_common = summary["most_common_errors"]

        assert len(most_common) == 2
        assert most_common[0]["error_code"] == "COMMON_ERROR"
        assert most_common[0]["count"] == 3
        assert most_common[1]["error_code"] == "RARE_ERROR"
        assert most_common[1]["count"] == 1
