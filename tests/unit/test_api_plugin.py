# tests/unit/test_api_plugin.py

"""
Unit tests for plugginger.api.plugin module.

Tests the PluginBase class, @plugin decorator, and utility functions
for plugin registration and metadata handling.
"""

from __future__ import annotations

import inspect
from typing import Any
from unittest.mock import Mock

import pytest
from pydantic import BaseModel

from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, get_plugin_metadata, is_plugin_class, plugin
from plugginger.core.constants import PLUGIN_METADATA_KEY
from plugginger.core.exceptions import PluginRegistrationError


# Test configuration models
class TestConfig(BaseModel):
    """Test configuration model."""
    api_key: str
    timeout: int = 30


class InvalidConfig:
    """Invalid configuration class (not BaseModel)."""
    pass


# Test plugin classes
@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin with minimal configuration."""
    needs: list[Depends] = [Depends("logger")]

    async def setup(self, plugin_config: BaseModel) -> None:
        """Test setup method."""
        self.setup_called = True

    async def teardown(self) -> None:
        """Test teardown method."""
        self.teardown_called = True


@plugin(name="config_plugin", version="2.1.0", config_schema=TestConfig)
class ConfigPlugin(PluginBase):
    """Test plugin with configuration schema."""
    needs: list[Depends] = [Depends("database"), Depends("cache")]


@plugin(name="minimal_plugin", version="0.1.0")
class MinimalPlugin(PluginBase):
    """Minimal plugin without dependencies."""
    pass


class UnDecoratedPlugin(PluginBase):
    """Plugin class without @plugin decorator."""
    pass


class NonPluginClass:
    """Regular class that doesn't inherit from PluginBase."""
    pass


class TestPluginBase:
    """Test PluginBase class functionality."""

    def test_plugin_base_is_abstract(self) -> None:
        """Test that PluginBase uses ABCMeta."""
        assert PluginBase.__class__.__name__ == "ABCMeta"

    def test_plugin_base_initialization(self) -> None:
        """Test PluginBase initialization with dependencies."""
        # Create mock app
        mock_app = Mock()

        # Create plugin instance with dependencies
        plugin_instance = TestPlugin(
            app=mock_app,
            logger=Mock(),
            database=Mock()
        )

        # Check app assignment
        assert plugin_instance.app is mock_app

        # Check dependency injection
        assert hasattr(plugin_instance, "logger")
        assert hasattr(plugin_instance, "database")
        assert plugin_instance.logger is not None
        assert plugin_instance.database is not None

    def test_plugin_base_initialization_no_dependencies(self) -> None:
        """Test PluginBase initialization without dependencies."""
        mock_app = Mock()

        plugin_instance = MinimalPlugin(app=mock_app)

        assert plugin_instance.app is mock_app

    def test_plugin_base_initialization_empty_dependencies(self) -> None:
        """Test PluginBase initialization with empty dependencies dict."""
        mock_app = Mock()

        plugin_instance = TestPlugin(app=mock_app)

        assert plugin_instance.app is mock_app

    def test_plugin_base_setup_method_default(self) -> None:
        """Test that default setup method does nothing."""
        mock_app = Mock()
        plugin_instance = MinimalPlugin(app=mock_app)

        # Should not raise any exception
        import asyncio

        # Create a simple config model instance
        class EmptyConfig(BaseModel):
            pass

        asyncio.run(plugin_instance.setup(EmptyConfig()))

    def test_plugin_base_teardown_method_default(self) -> None:
        """Test that default teardown method does nothing."""
        mock_app = Mock()
        plugin_instance = MinimalPlugin(app=mock_app)

        # Should not raise any exception
        import asyncio
        asyncio.run(plugin_instance.teardown())

    def test_plugin_base_setup_method_override(self) -> None:
        """Test that overridden setup method is called."""
        mock_app = Mock()
        plugin_instance = TestPlugin(app=mock_app)

        import asyncio

        # Create a simple config model instance
        class EmptyConfig(BaseModel):
            pass

        asyncio.run(plugin_instance.setup(EmptyConfig()))

        assert hasattr(plugin_instance, "setup_called")
        assert plugin_instance.setup_called is True

    def test_plugin_base_teardown_method_override(self) -> None:
        """Test that overridden teardown method is called."""
        mock_app = Mock()
        plugin_instance = TestPlugin(app=mock_app)

        import asyncio
        asyncio.run(plugin_instance.teardown())

        assert hasattr(plugin_instance, "teardown_called")
        assert plugin_instance.teardown_called is True

    def test_plugin_base_needs_attribute(self) -> None:
        """Test that needs attribute is properly set."""
        assert hasattr(TestPlugin, "needs")
        assert isinstance(TestPlugin.needs, list)
        assert len(TestPlugin.needs) == 1
        assert isinstance(TestPlugin.needs[0], Depends)
        assert TestPlugin.needs[0].dependency == "logger"

    def test_plugin_base_needs_attribute_multiple(self) -> None:
        """Test plugin with multiple dependencies."""
        assert hasattr(ConfigPlugin, "needs")
        assert isinstance(ConfigPlugin.needs, list)
        assert len(ConfigPlugin.needs) == 2

        dep_names = [dep.dependency for dep in ConfigPlugin.needs]
        assert "database" in dep_names
        assert "cache" in dep_names

    def test_plugin_base_needs_attribute_empty(self) -> None:
        """Test plugin with no dependencies."""
        assert hasattr(MinimalPlugin, "needs")
        assert isinstance(MinimalPlugin.needs, list)
        assert len(MinimalPlugin.needs) == 0

    def test_plugin_base_metadata_attributes(self) -> None:
        """Test that metadata attributes are set by decorator."""
        assert hasattr(TestPlugin, "_plugginger_plugin_name")
        assert hasattr(TestPlugin, "_plugginger_plugin_version")
        assert hasattr(TestPlugin, "_plugginger_config_schema")

        assert TestPlugin._plugginger_plugin_name == "test_plugin"
        assert TestPlugin._plugginger_plugin_version == "1.0.0"
        assert TestPlugin._plugginger_config_schema is None

    def test_plugin_base_metadata_attributes_with_config(self) -> None:
        """Test metadata attributes with config schema."""
        assert ConfigPlugin._plugginger_plugin_name == "config_plugin"
        assert ConfigPlugin._plugginger_plugin_version == "2.1.0"
        assert ConfigPlugin._plugginger_config_schema is TestConfig

    def test_plugin_base_metadata_key(self) -> None:
        """Test that plugin metadata key is set."""
        assert hasattr(TestPlugin, PLUGIN_METADATA_KEY)
        assert getattr(TestPlugin, PLUGIN_METADATA_KEY) is True


class TestPluginDecorator:
    """Test @plugin decorator functionality."""

    def test_plugin_decorator_basic(self) -> None:
        """Test basic plugin decorator usage."""
        @plugin(name="basic_test", version="1.0.0")
        class BasicTestPlugin(PluginBase):
            pass

        assert BasicTestPlugin._plugginger_plugin_name == "basic_test"
        assert BasicTestPlugin._plugginger_plugin_version == "1.0.0"
        assert BasicTestPlugin._plugginger_config_schema is None
        assert hasattr(BasicTestPlugin, PLUGIN_METADATA_KEY)

    def test_plugin_decorator_with_config_schema(self) -> None:
        """Test plugin decorator with config schema."""
        @plugin(name="config_test", version="2.0.0", config_schema=TestConfig)
        class ConfigTestPlugin(PluginBase):
            pass

        assert ConfigTestPlugin._plugginger_plugin_name == "config_test"
        assert ConfigTestPlugin._plugginger_plugin_version == "2.0.0"
        assert ConfigTestPlugin._plugginger_config_schema is TestConfig

    def test_plugin_decorator_non_class_error(self) -> None:
        """Test that decorator raises error for non-class objects."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            # Apply decorator to a function instead of a class
            plugin_decorator = plugin(name="test", version="1.0.0")

            def not_a_class() -> None:
                pass

            plugin_decorator(not_a_class)  # type: ignore[arg-type]

        assert "can only be applied to classes" in str(exc_info.value)

    def test_plugin_decorator_non_plugin_base_error(self) -> None:
        """Test that decorator raises error for non-PluginBase classes."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            @plugin(name="test", version="1.0.0")
            class NotPluginBase:
                pass

        assert "must inherit from PluginBase" in str(exc_info.value)
        assert "NotPluginBase" in str(exc_info.value)

    def test_plugin_decorator_empty_name_error(self) -> None:
        """Test that decorator raises error for empty name."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            @plugin(name="", version="1.0.0")
            class EmptyNamePlugin(PluginBase):
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_plugin_decorator_none_name_error(self) -> None:
        """Test that decorator raises error for None name."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            @plugin(name=None, version="1.0.0")  # type: ignore[arg-type]
            class NoneNamePlugin(PluginBase):
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_plugin_decorator_invalid_identifier_name_error(self) -> None:
        """Test that decorator raises error for invalid identifier names."""
        invalid_names = ["123invalid", "invalid-name", "invalid.name", "invalid name"]

        for invalid_name in invalid_names:
            with pytest.raises(PluginRegistrationError) as exc_info:
                @plugin(name=invalid_name, version="1.0.0")
                class InvalidNamePlugin(PluginBase):
                    pass

            assert "must be a valid Python identifier" in str(exc_info.value)

    def test_plugin_decorator_invalid_case_name_error(self) -> None:
        """Test that decorator raises error for non-lowercase names."""
        invalid_names = ["CamelCase", "UPPERCASE", "mixedCase"]

        for invalid_name in invalid_names:
            with pytest.raises(PluginRegistrationError) as exc_info:
                @plugin(name=invalid_name, version="1.0.0")
                class InvalidCasePlugin(PluginBase):
                    pass

            assert "should be lowercase" in str(exc_info.value)

    def test_plugin_decorator_underscore_name_error(self) -> None:
        """Test that decorator raises error for names with leading/trailing underscores."""
        invalid_names = ["_leading", "trailing_", "_both_"]

        for invalid_name in invalid_names:
            with pytest.raises(PluginRegistrationError) as exc_info:
                @plugin(name=invalid_name, version="1.0.0")
                class UnderscorePlugin(PluginBase):
                    pass

            assert "without leading/trailing underscores" in str(exc_info.value)

    def test_plugin_decorator_empty_version_error(self) -> None:
        """Test that decorator raises error for empty version."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            @plugin(name="test", version="")
            class EmptyVersionPlugin(PluginBase):
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_plugin_decorator_none_version_error(self) -> None:
        """Test that decorator raises error for None version."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            @plugin(name="test", version=None)  # type: ignore[arg-type]
            class NoneVersionPlugin(PluginBase):
                pass

        assert "must be a non-empty string" in str(exc_info.value)

    def test_plugin_decorator_invalid_version_error(self) -> None:
        """Test that decorator raises error for invalid version strings."""
        invalid_versions = ["invalid", "version.string", "abc.1.0"]

        for invalid_version in invalid_versions:
            with pytest.raises(PluginRegistrationError) as exc_info:
                @plugin(name="test", version=invalid_version)
                class InvalidVersionPlugin(PluginBase):
                    pass

            assert "does not appear to be a valid version string" in str(exc_info.value)

    def test_plugin_decorator_valid_versions(self) -> None:
        """Test that decorator accepts valid version strings."""
        valid_versions = ["1.0.0", "0.1.0", "2.3.4", "v1.0.0", "v2.1.3", "1.0.0a1", "1.0.0b2", "1.0.0rc1"]

        for i, valid_version in enumerate(valid_versions):
            @plugin(name=f"test{i}", version=valid_version)
            class ValidVersionPlugin(PluginBase):
                pass

            assert ValidVersionPlugin._plugginger_plugin_version == valid_version

    def test_plugin_decorator_invalid_config_schema_error(self) -> None:
        """Test that decorator raises error for invalid config schema."""
        with pytest.raises(TypeError) as exc_info:
            @plugin(name="test", version="1.0.0", config_schema=InvalidConfig)  # type: ignore[arg-type]
            class InvalidConfigPlugin(PluginBase):
                pass

        assert "must be a subclass of pydantic.BaseModel" in str(exc_info.value)

    def test_plugin_decorator_non_class_config_schema_error(self) -> None:
        """Test that decorator raises error for non-class config schema."""
        with pytest.raises(TypeError) as exc_info:
            @plugin(name="test", version="1.0.0", config_schema="not_a_class")  # type: ignore[arg-type]
            class NonClassConfigPlugin(PluginBase):
                pass

        assert "must be a subclass of pydantic.BaseModel" in str(exc_info.value)

    def test_plugin_decorator_returns_same_class(self) -> None:
        """Test that decorator returns the same class object."""
        class OriginalPlugin(PluginBase):
            pass

        decorated_plugin = plugin(name="test", version="1.0.0")(OriginalPlugin)

        assert decorated_plugin is OriginalPlugin

    def test_plugin_decorator_preserves_class_attributes(self) -> None:
        """Test that decorator preserves existing class attributes."""
        @plugin(name="test", version="1.0.0")
        class AttributePlugin(PluginBase):
            custom_attribute = "test_value"

            def custom_method(self) -> str:
                return "test"

        assert AttributePlugin.custom_attribute == "test_value"
        assert hasattr(AttributePlugin, "custom_method")

        # Test instance creation with required app parameter
        mock_app = Mock()
        instance = AttributePlugin(app=mock_app)
        assert instance.custom_method() == "test"

    def test_plugin_decorator_keyword_only_arguments(self) -> None:
        """Test that decorator requires keyword-only arguments."""
        # This test verifies the function signature, not runtime behavior
        sig = inspect.signature(plugin)

        for param in sig.parameters.values():
            assert param.kind == inspect.Parameter.KEYWORD_ONLY


class TestGetPluginMetadata:
    """Test get_plugin_metadata function."""

    def test_get_plugin_metadata_valid_plugin(self) -> None:
        """Test getting metadata from valid plugin."""
        metadata = get_plugin_metadata(TestPlugin)

        assert metadata["name"] == "test_plugin"
        assert metadata["version"] == "1.0.0"
        assert metadata["config_schema"] is None
        assert metadata["class_name"] == "TestPlugin"
        assert metadata["module"] == TestPlugin.__module__

    def test_get_plugin_metadata_with_config_schema(self) -> None:
        """Test getting metadata from plugin with config schema."""
        metadata = get_plugin_metadata(ConfigPlugin)

        assert metadata["name"] == "config_plugin"
        assert metadata["version"] == "2.1.0"
        assert metadata["config_schema"] is TestConfig
        assert metadata["class_name"] == "ConfigPlugin"
        assert metadata["module"] == ConfigPlugin.__module__

    def test_get_plugin_metadata_invalid_plugin_error(self) -> None:
        """Test that function raises error for invalid plugin."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            get_plugin_metadata(UnDecoratedPlugin)

        assert "is not a valid plugin" in str(exc_info.value)
        assert "missing @plugin decorator" in str(exc_info.value)
        assert "UnDecoratedPlugin" in str(exc_info.value)

    def test_get_plugin_metadata_non_plugin_class_error(self) -> None:
        """Test that function raises error for non-plugin class."""
        with pytest.raises(PluginRegistrationError) as exc_info:
            get_plugin_metadata(NonPluginClass)

        assert "is not a valid plugin" in str(exc_info.value)
        assert "NonPluginClass" in str(exc_info.value)

    def test_get_plugin_metadata_missing_attributes(self) -> None:
        """Test metadata extraction with missing attributes."""
        # Create a class with metadata key but missing some attributes
        class PartialPlugin(PluginBase):
            pass

        setattr(PartialPlugin, PLUGIN_METADATA_KEY, True)
        # Don't set the metadata attributes

        metadata = get_plugin_metadata(PartialPlugin)

        assert metadata["name"] == "unknown"
        assert metadata["version"] == "unknown"
        assert metadata["config_schema"] is None
        assert metadata["class_name"] == "PartialPlugin"


class TestIsPluginClass:
    """Test is_plugin_class function."""

    def test_is_plugin_class_valid_plugin(self) -> None:
        """Test that function returns True for valid plugin."""
        assert is_plugin_class(TestPlugin) is True
        assert is_plugin_class(ConfigPlugin) is True
        assert is_plugin_class(MinimalPlugin) is True

    def test_is_plugin_class_undecorated_plugin(self) -> None:
        """Test that function returns False for undecorated plugin."""
        assert is_plugin_class(UnDecoratedPlugin) is False

    def test_is_plugin_class_non_plugin_class(self) -> None:
        """Test that function returns False for non-plugin class."""
        assert is_plugin_class(NonPluginClass) is False

    def test_is_plugin_class_non_class(self) -> None:
        """Test that function returns False for non-class objects."""
        assert is_plugin_class("not a class") is False  # type: ignore[arg-type]
        assert is_plugin_class(123) is False  # type: ignore[arg-type]
        assert is_plugin_class(None) is False  # type: ignore[arg-type]

        # Test with lambda function
        lambda_func = lambda: None  # noqa: E731
        assert is_plugin_class(lambda_func) is False  # type: ignore[arg-type]

    def test_is_plugin_class_abstract_plugin(self) -> None:
        """Test that function returns False for abstract plugin classes."""
        from abc import abstractmethod

        @plugin(name="abstract_test", version="1.0.0")
        class AbstractPlugin(PluginBase):
            @abstractmethod
            def abstract_method(self) -> None:
                pass

        assert is_plugin_class(AbstractPlugin) is False

    def test_is_plugin_class_plugin_base_itself(self) -> None:
        """Test that function returns False for PluginBase itself."""
        assert is_plugin_class(PluginBase) is False

    def test_is_plugin_class_with_metadata_key_only(self) -> None:
        """Test function with class that only has metadata key."""
        class MetadataOnlyClass:
            pass

        setattr(MetadataOnlyClass, PLUGIN_METADATA_KEY, True)

        assert is_plugin_class(MetadataOnlyClass) is False


class TestPluginIntegration:
    """Test integration scenarios for plugin functionality."""

    def test_plugin_lifecycle_integration(self) -> None:
        """Test complete plugin lifecycle."""
        @plugin(name="lifecycle_test", version="1.0.0", config_schema=TestConfig)
        class LifecyclePlugin(PluginBase):
            needs: list[Depends] = [Depends("logger")]

            def __init__(self, app: Any, **injected_dependencies: Any) -> None:
                super().__init__(app, **injected_dependencies)
                self.initialized = True

            async def setup(self, plugin_config: BaseModel) -> None:
                self.setup_config = plugin_config

            async def teardown(self) -> None:
                self.torn_down = True

        # Test metadata
        assert is_plugin_class(LifecyclePlugin)
        metadata = get_plugin_metadata(LifecyclePlugin)
        assert metadata["name"] == "lifecycle_test"
        assert metadata["config_schema"] is TestConfig

        # Test instantiation
        mock_app = Mock()
        mock_logger = Mock()
        plugin_instance = LifecyclePlugin(app=mock_app, logger=mock_logger)

        assert plugin_instance.initialized is True
        assert plugin_instance.app is mock_app
        assert hasattr(plugin_instance, "logger")
        assert plugin_instance.logger is mock_logger

        # Test lifecycle methods
        import asyncio
        test_config = TestConfig(api_key="test_key", timeout=60)
        asyncio.run(plugin_instance.setup(test_config))
        assert plugin_instance.setup_config is test_config

        asyncio.run(plugin_instance.teardown())
        assert plugin_instance.torn_down is True

    def test_multiple_plugins_same_module(self) -> None:
        """Test multiple plugins in the same module."""
        @plugin(name="first_plugin", version="1.0.0")
        class FirstPlugin(PluginBase):
            pass

        @plugin(name="second_plugin", version="2.0.0")
        class SecondPlugin(PluginBase):
            pass

        # Both should be valid plugins
        assert is_plugin_class(FirstPlugin)
        assert is_plugin_class(SecondPlugin)

        # Metadata should be different
        first_metadata = get_plugin_metadata(FirstPlugin)
        second_metadata = get_plugin_metadata(SecondPlugin)

        assert first_metadata["name"] != second_metadata["name"]
        assert first_metadata["version"] != second_metadata["version"]

    def test_plugin_inheritance_chain(self) -> None:
        """Test plugin inheritance from other plugins."""
        @plugin(name="base_plugin", version="1.0.0")
        class BasePlugin(PluginBase):
            base_attribute = "base"

        @plugin(name="derived_plugin", version="1.1.0")
        class DerivedPlugin(BasePlugin):
            derived_attribute = "derived"

        # Both should be valid plugins
        assert is_plugin_class(BasePlugin)
        assert is_plugin_class(DerivedPlugin)

        # Derived plugin should have both attributes
        mock_app = Mock()
        derived_instance = DerivedPlugin(app=mock_app)
        assert derived_instance.base_attribute == "base"
        assert derived_instance.derived_attribute == "derived"

        # Metadata should be specific to each class
        base_metadata = get_plugin_metadata(BasePlugin)
        derived_metadata = get_plugin_metadata(DerivedPlugin)

        assert base_metadata["name"] == "base_plugin"
        assert derived_metadata["name"] == "derived_plugin"
