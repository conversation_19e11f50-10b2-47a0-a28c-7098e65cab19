# tests/unit/internal/runtime/test_executors.py

"""
Unit tests for plugginger._internal.runtime.executors module.

Tests the ExecutorRegistry class for managing ThreadPoolExecutor instances
and background task execution.
"""

from __future__ import annotations

# Import the actual module to test for proper coverage
import importlib.util
import sys
from concurrent.futures import Executor, ThreadPoolExecutor
from pathlib import Path
from unittest.mock import Mock

import pytest

# Load the executors module directly from file
executors_path = Path(__file__).parent.parent.parent.parent.parent / "src" / "plugginger" / "_internal" / "runtime" / "executors.py"
spec = importlib.util.spec_from_file_location("executors", executors_path)
if spec is None or spec.loader is None:
    raise ImportError("Could not load executors module")

executors_module = importlib.util.module_from_spec(spec)
sys.modules["executors"] = executors_module
spec.loader.exec_module(executors_module)

ExecutorRegistry = executors_module.ExecutorRegistry

# Import after dynamic loading to avoid import order issues
from plugginger.config.models import ExecutorConfig  # noqa: E402
from plugginger.core.constants import DEFAULT_EXECUTOR_NAME  # noqa: E402


class TestExecutorRegistryInitialization:
    """Test ExecutorRegistry initialization."""

    def test_initialization_with_default_executor(self) -> None:
        """Test initialization creates default executor."""
        logger = Mock()
        config = ExecutorConfig(name="default")

        registry = ExecutorRegistry(config, logger)

        assert registry._logger is logger
        assert isinstance(registry._executors, dict)
        assert DEFAULT_EXECUTOR_NAME in registry._executors
        assert isinstance(registry._executors[DEFAULT_EXECUTOR_NAME], ThreadPoolExecutor)

    def test_initialization_logs_default_executor_creation(self) -> None:
        """Test that default executor creation is logged."""
        logger = Mock()
        config = ExecutorConfig(name="default", max_workers=4)

        ExecutorRegistry(config, logger)

        # Should log the creation of the default executor
        logger.assert_called()
        log_calls = [call[0][0] for call in logger.call_args_list]
        assert any("Created ThreadPoolExecutor" in call and "default" in call for call in log_calls)

    def test_initialization_with_custom_config(self) -> None:
        """Test initialization with custom executor configuration."""
        logger = Mock()
        config = ExecutorConfig(
            name="custom",
            max_workers=8,
            thread_name_prefix="custom-thread"
        )

        registry = ExecutorRegistry(config, logger)

        # Should have the default executor registered
        assert DEFAULT_EXECUTOR_NAME in registry._executors
        executor = registry._executors[DEFAULT_EXECUTOR_NAME]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_initialization_with_default_max_workers(self) -> None:
        """Test initialization with default max_workers from Pydantic model."""
        logger = Mock()
        config = ExecutorConfig(name="default")  # Uses default max_workers=4

        registry = ExecutorRegistry(config, logger)

        # Should create executor with default max_workers from model
        assert DEFAULT_EXECUTOR_NAME in registry._executors
        executor = registry._executors[DEFAULT_EXECUTOR_NAME]
        assert isinstance(executor, ThreadPoolExecutor)


class TestRegisterExecutorWithConfig:
    """Test register_executor method with ExecutorConfig."""

    def test_register_executor_with_config(self) -> None:
        """Test registering executor with ExecutorConfig."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        new_config = ExecutorConfig(name="test", max_workers=4)
        registry.register_executor("test", new_config)

        assert "test" in registry._executors
        assert isinstance(registry._executors["test"], ThreadPoolExecutor)

    def test_register_executor_logs_creation(self) -> None:
        """Test that executor creation is logged."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        logger.reset_mock()  # Clear initialization logs

        new_config = ExecutorConfig(name="test", max_workers=4)
        registry.register_executor("test", new_config)

        logger.assert_called()
        log_message = logger.call_args[0][0]
        assert "Created ThreadPoolExecutor 'test' with 4 workers" in log_message

    def test_register_executor_with_default_max_workers(self) -> None:
        """Test registering executor with default max_workers from Pydantic model."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # ExecutorConfig uses default max_workers=4 from Pydantic model
        new_config = ExecutorConfig(name="test")  # Uses default max_workers=4
        registry.register_executor("test", new_config)

        assert "test" in registry._executors
        executor = registry._executors["test"]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_register_executor_with_explicit_max_workers(self) -> None:
        """Test registering executor with explicit max_workers."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        new_config = ExecutorConfig(name="test", max_workers=8)
        registry.register_executor("test", new_config)

        assert "test" in registry._executors
        executor = registry._executors["test"]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_register_executor_with_custom_thread_prefix(self) -> None:
        """Test registering executor with custom thread name prefix."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        new_config = ExecutorConfig(
            name="test",
            max_workers=4,
            thread_name_prefix="custom-prefix"
        )
        registry.register_executor("test", new_config)

        assert "test" in registry._executors
        executor = registry._executors["test"]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_register_executor_without_thread_prefix(self) -> None:
        """Test registering executor without thread name prefix uses default."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        new_config = ExecutorConfig(name="test", max_workers=4)
        registry.register_executor("test", new_config)

        assert "test" in registry._executors
        executor = registry._executors["test"]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_register_executor_with_pydantic_validation(self) -> None:
        """Test that Pydantic validation works for ExecutorConfig."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Test that Pydantic validates max_workers >= 1
        from pydantic import ValidationError

        with pytest.raises(ValidationError):
            ExecutorConfig(name="test", max_workers=0)

        with pytest.raises(ValidationError):
            ExecutorConfig(name="test", max_workers=-5)

        # Valid config should work
        valid_config = ExecutorConfig(name="test", max_workers=2)
        registry.register_executor("test", valid_config)
        assert "test" in registry._executors


class TestRegisterExecutorWithInstance:
    """Test register_executor method with Executor instance."""

    def test_register_executor_with_instance(self) -> None:
        """Test registering pre-configured executor instance."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        custom_executor = ThreadPoolExecutor(max_workers=2)
        registry.register_executor("custom", custom_executor)

        assert "custom" in registry._executors
        assert registry._executors["custom"] is custom_executor

    def test_register_executor_instance_logs_registration(self) -> None:
        """Test that executor instance registration is logged."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        logger.reset_mock()  # Clear initialization logs

        custom_executor = ThreadPoolExecutor(max_workers=2)
        registry.register_executor("custom", custom_executor)

        logger.assert_called_once_with("[Plugginger] Registered custom executor 'custom'")

    def test_register_different_executor_types(self) -> None:
        """Test registering different types of executors."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register ThreadPoolExecutor
        thread_executor = ThreadPoolExecutor(max_workers=2)
        registry.register_executor("thread", thread_executor)

        # Register mock executor (any Executor subclass)
        mock_executor = Mock(spec=Executor)
        registry.register_executor("mock", mock_executor)

        assert registry._executors["thread"] is thread_executor
        assert registry._executors["mock"] is mock_executor


class TestRegisterExecutorErrors:
    """Test error cases for register_executor method."""

    def test_register_executor_duplicate_name_raises_error(self) -> None:
        """Test that registering duplicate name raises ValueError."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Try to register another executor with the default name
        new_config = ExecutorConfig(name="test", max_workers=4)

        with pytest.raises(ValueError) as exc_info:
            registry.register_executor(DEFAULT_EXECUTOR_NAME, new_config)

        assert f"Executor with name '{DEFAULT_EXECUTOR_NAME}' already exists" in str(exc_info.value)

    def test_register_executor_duplicate_name_logs_error(self) -> None:
        """Test that duplicate name error is logged."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        new_config = ExecutorConfig(name="test", max_workers=4)

        with pytest.raises(ValueError):
            registry.register_executor(DEFAULT_EXECUTOR_NAME, new_config)

        # Check that error was logged
        log_calls = [call[0][0] for call in logger.call_args_list]
        assert any("ERROR" in call and "already exists" in call for call in log_calls)

    def test_register_executor_invalid_type_raises_error(self) -> None:
        """Test that invalid type raises TypeError."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        with pytest.raises(TypeError) as exc_info:
            registry.register_executor("test", "invalid")

        error_msg = str(exc_info.value)
        assert "config_or_executor must be ExecutorConfig or Executor" in error_msg
        assert "got <class 'str'>" in error_msg

    def test_register_executor_with_various_invalid_types(self) -> None:
        """Test various invalid types for config_or_executor."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        invalid_types = [123, [], {}, None, True, 3.14]

        for invalid_type in invalid_types:
            with pytest.raises(TypeError):
                registry.register_executor("test", invalid_type)


class TestGetExecutor:
    """Test get_executor method."""

    def test_get_default_executor(self) -> None:
        """Test getting the default executor."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        executor = registry.get_executor()

        assert executor is registry._executors[DEFAULT_EXECUTOR_NAME]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_get_executor_by_name(self) -> None:
        """Test getting executor by specific name."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register a custom executor
        custom_config = ExecutorConfig(name="custom", max_workers=4)
        registry.register_executor("custom", custom_config)

        executor = registry.get_executor("custom")

        assert executor is registry._executors["custom"]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_get_executor_explicit_default_name(self) -> None:
        """Test getting executor with explicit default name."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        executor = registry.get_executor(DEFAULT_EXECUTOR_NAME)

        assert executor is registry._executors[DEFAULT_EXECUTOR_NAME]
        assert isinstance(executor, ThreadPoolExecutor)

    def test_get_nonexistent_executor_raises_error(self) -> None:
        """Test that getting nonexistent executor raises KeyError."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        with pytest.raises(KeyError) as exc_info:
            registry.get_executor("nonexistent")

        assert "No executor registered with name 'nonexistent'" in str(exc_info.value)

    def test_get_nonexistent_executor_logs_error(self) -> None:
        """Test that getting nonexistent executor logs error."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        with pytest.raises(KeyError):
            registry.get_executor("nonexistent")

        # Check that error was logged
        log_calls = [call[0][0] for call in logger.call_args_list]
        assert any("ERROR" in call and "No executor registered" in call for call in log_calls)

    def test_get_multiple_executors(self) -> None:
        """Test getting multiple different executors."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register multiple executors
        config1 = ExecutorConfig(name="exec1", max_workers=2)
        config2 = ExecutorConfig(name="exec2", max_workers=4)
        registry.register_executor("exec1", config1)
        registry.register_executor("exec2", config2)

        # Get all executors
        default_exec = registry.get_executor()
        exec1 = registry.get_executor("exec1")
        exec2 = registry.get_executor("exec2")

        # All should be different instances
        assert default_exec is not exec1
        assert default_exec is not exec2
        assert exec1 is not exec2

        # All should be ThreadPoolExecutors
        assert isinstance(default_exec, ThreadPoolExecutor)
        assert isinstance(exec1, ThreadPoolExecutor)
        assert isinstance(exec2, ThreadPoolExecutor)


class TestShutdownExecutors:
    """Test shutdown_executors method."""

    def test_shutdown_executors_default_wait(self) -> None:
        """Test shutting down executors with default wait=True."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Mock the executor's shutdown method
        executor = registry._executors[DEFAULT_EXECUTOR_NAME]
        executor.shutdown = Mock()

        registry.shutdown_executors()

        executor.shutdown.assert_called_once_with(wait=True)

    def test_shutdown_executors_wait_false(self) -> None:
        """Test shutting down executors with wait=False."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Mock the executor's shutdown method
        executor = registry._executors[DEFAULT_EXECUTOR_NAME]
        executor.shutdown = Mock()

        registry.shutdown_executors(wait=False)

        executor.shutdown.assert_called_once_with(wait=False)

    def test_shutdown_multiple_executors(self) -> None:
        """Test shutting down multiple executors."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register additional executors
        config1 = ExecutorConfig(name="exec1", max_workers=2)
        config2 = ExecutorConfig(name="exec2", max_workers=4)
        registry.register_executor("exec1", config1)
        registry.register_executor("exec2", config2)

        # Mock all shutdown methods
        for executor in registry._executors.values():
            executor.shutdown = Mock()

        registry.shutdown_executors()

        # All executors should have been shut down
        for executor in registry._executors.values():
            executor.shutdown.assert_called_once_with(wait=True)

    def test_shutdown_executors_logs_process(self) -> None:
        """Test that shutdown process is logged."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        logger.reset_mock()  # Clear initialization logs

        registry.shutdown_executors()

        log_calls = [call[0][0] for call in logger.call_args_list]

        # Should log start, individual shutdowns, and completion
        assert any("Shutting down all executors" in call for call in log_calls)
        assert any("Shutting down executor" in call and DEFAULT_EXECUTOR_NAME in call for call in log_calls)
        assert any("All executor shutdown attempts completed" in call for call in log_calls)

    def test_shutdown_executors_handles_exceptions(self) -> None:
        """Test that shutdown handles exceptions gracefully."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register additional executor
        config1 = ExecutorConfig(name="exec1", max_workers=2)
        registry.register_executor("exec1", config1)

        # Make one executor raise an exception during shutdown
        default_executor = registry._executors[DEFAULT_EXECUTOR_NAME]
        exec1 = registry._executors["exec1"]

        default_executor.shutdown = Mock(side_effect=RuntimeError("Shutdown failed"))
        exec1.shutdown = Mock()

        logger.reset_mock()  # Clear initialization logs

        # Should not raise exception
        registry.shutdown_executors()

        # Both shutdown methods should have been called
        default_executor.shutdown.assert_called_once()
        exec1.shutdown.assert_called_once()

        # Error should be logged
        log_calls = [call[0][0] for call in logger.call_args_list]
        assert any("ERROR" in call and "Failed to shutdown executor" in call for call in log_calls)

    def test_shutdown_executors_handles_multiple_exceptions(self) -> None:
        """Test that shutdown handles multiple exceptions."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register additional executors
        config1 = ExecutorConfig(name="exec1", max_workers=2)
        config2 = ExecutorConfig(name="exec2", max_workers=4)
        registry.register_executor("exec1", config1)
        registry.register_executor("exec2", config2)

        # Make all executors raise exceptions
        for name, executor in registry._executors.items():
            executor.shutdown = Mock(side_effect=RuntimeError(f"Shutdown failed for {name}"))

        logger.reset_mock()  # Clear initialization logs

        # Should not raise exception
        registry.shutdown_executors()

        # All shutdown methods should have been called
        for executor in registry._executors.values():
            executor.shutdown.assert_called_once()

        # All errors should be logged
        log_calls = [call[0][0] for call in logger.call_args_list]
        error_logs = [call for call in log_calls if "ERROR" in call and "Failed to shutdown executor" in call]
        assert len(error_logs) == 3  # One for each executor

    def test_shutdown_executors_different_exception_types(self) -> None:
        """Test shutdown with different types of exceptions."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register additional executors
        config1 = ExecutorConfig(name="exec1", max_workers=2)
        config2 = ExecutorConfig(name="exec2", max_workers=4)
        registry.register_executor("exec1", config1)
        registry.register_executor("exec2", config2)

        # Make executors raise different exception types
        executors = list(registry._executors.values())
        executors[0].shutdown = Mock(side_effect=RuntimeError("Runtime error"))
        executors[1].shutdown = Mock(side_effect=AttributeError("Attribute error"))
        executors[2].shutdown = Mock(side_effect=ValueError("Value error"))

        logger.reset_mock()  # Clear initialization logs

        # Should not raise exception
        registry.shutdown_executors()

        # All shutdown methods should have been called
        for executor in executors:
            executor.shutdown.assert_called_once()

        # All errors should be logged with proper exception representation
        log_calls = [call[0][0] for call in logger.call_args_list]
        error_logs = [call for call in log_calls if "ERROR" in call and "Failed to shutdown executor" in call]
        assert len(error_logs) == 3


class TestExecutorRegistryIntegration:
    """Test integration scenarios for ExecutorRegistry."""

    def test_complete_lifecycle(self) -> None:
        """Test complete lifecycle: create, register, get, shutdown."""
        logger = Mock()
        default_config = ExecutorConfig(name="default", max_workers=2)
        registry = ExecutorRegistry(default_config, logger)

        # Register additional executor
        custom_config = ExecutorConfig(name="custom", max_workers=4, thread_name_prefix="custom")
        registry.register_executor("custom", custom_config)

        # Get executors
        default_exec = registry.get_executor()
        custom_exec = registry.get_executor("custom")

        assert isinstance(default_exec, ThreadPoolExecutor)
        assert isinstance(custom_exec, ThreadPoolExecutor)
        assert default_exec is not custom_exec

        # Shutdown all
        registry.shutdown_executors()

        # Verify logging occurred at each step
        log_calls = [call[0][0] for call in logger.call_args_list]
        assert any("Created ThreadPoolExecutor" in call for call in log_calls)
        assert any("Shutting down all executors" in call for call in log_calls)

    def test_mixed_executor_types(self) -> None:
        """Test registry with mixed executor types."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Register config-based executor
        config_executor_config = ExecutorConfig(name="config", max_workers=2)
        registry.register_executor("config", config_executor_config)

        # Register instance-based executor
        instance_executor = ThreadPoolExecutor(max_workers=4)
        registry.register_executor("instance", instance_executor)

        # Get all executors
        default_exec = registry.get_executor()
        config_exec = registry.get_executor("config")
        instance_exec = registry.get_executor("instance")

        assert isinstance(default_exec, ThreadPoolExecutor)
        assert isinstance(config_exec, ThreadPoolExecutor)
        assert instance_exec is instance_executor

        # All should be different
        assert default_exec is not config_exec
        assert default_exec is not instance_exec
        assert config_exec is not instance_exec

    def test_error_recovery(self) -> None:
        """Test that registry continues working after errors."""
        logger = Mock()
        default_config = ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_config, logger)

        # Try to register invalid executor (should fail)
        with pytest.raises(TypeError):
            registry.register_executor("invalid", "not an executor")

        # Try to register duplicate name (should fail)
        duplicate_config = ExecutorConfig(name="duplicate")
        with pytest.raises(ValueError):
            registry.register_executor(DEFAULT_EXECUTOR_NAME, duplicate_config)

        # Try to get nonexistent executor (should fail)
        with pytest.raises(KeyError):
            registry.get_executor("nonexistent")

        # Registry should still work normally
        valid_config = ExecutorConfig(name="valid", max_workers=2)
        registry.register_executor("valid", valid_config)

        valid_exec = registry.get_executor("valid")
        assert isinstance(valid_exec, ThreadPoolExecutor)

        # Shutdown should still work
        registry.shutdown_executors()



