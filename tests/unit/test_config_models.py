# tests/unit/test_config_models.py

"""
Unit tests for plugginger.config.models module.

Tests all Pydantic models for configuration structures to ensure they
have correct validation, defaults, and behavior.
"""

from __future__ import annotations

from datetime import datetime

import pytest
from pydantic import ValidationError

from plugginger.config.models import (
    ExecutorConfig,
    GlobalAppConfig,
    PluggingerLockfile,
    PluggingerLockfileMetadata,
    PluginLockInfo,
    PythonPackageLockInfo,
)
from plugginger.core.config import EventListenerFaultPolicy, LogLevel


class TestExecutorConfig:
    """Test ExecutorConfig model."""

    def test_default_initialization(self) -> None:
        """Test ExecutorConfig with default values."""
        config = ExecutorConfig(name="test_executor")

        assert config.name == "test_executor"
        assert config.max_workers == 4
        assert config.thread_name_prefix == "PluggingerWorker"

    def test_custom_initialization(self) -> None:
        """Test ExecutorConfig with custom values."""
        config = ExecutorConfig(
            name="custom_executor",
            max_workers=8,
            thread_name_prefix="CustomWorker"
        )

        assert config.name == "custom_executor"
        assert config.max_workers == 8
        assert config.thread_name_prefix == "CustomWorker"

    def test_max_workers_validation_positive(self) -> None:
        """Test that max_workers must be positive."""
        with pytest.raises(ValidationError) as exc_info:
            ExecutorConfig(name="test", max_workers=0)

        errors = exc_info.value.errors()
        assert any("greater than or equal to 1" in str(error) for error in errors)

    def test_max_workers_validation_negative(self) -> None:
        """Test that max_workers cannot be negative."""
        with pytest.raises(ValidationError) as exc_info:
            ExecutorConfig(name="test", max_workers=-1)

        errors = exc_info.value.errors()
        assert any("greater than or equal to 1" in str(error) for error in errors)

    def test_name_required(self) -> None:
        """Test that name is required."""
        with pytest.raises(ValidationError) as exc_info:
            ExecutorConfig()  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any(error.get("loc") == ("name",) for error in errors)

    def test_extra_fields_forbidden(self) -> None:
        """Test that extra fields are forbidden."""
        with pytest.raises(ValidationError) as exc_info:
            ExecutorConfig(name="test", extra_field="not_allowed")  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any("extra_field" in str(error) for error in errors)

    def test_model_dump(self) -> None:
        """Test model serialization."""
        config = ExecutorConfig(name="test", max_workers=6)
        data = config.model_dump()

        expected = {
            "name": "test",
            "max_workers": 6,
            "thread_name_prefix": "PluggingerWorker"
        }
        assert data == expected

    def test_model_validation_assignment(self) -> None:
        """Test that assignment validation works."""
        config = ExecutorConfig(name="test")

        # Valid assignment
        config.max_workers = 8
        assert config.max_workers == 8

        # Invalid assignment should raise ValidationError
        with pytest.raises(ValidationError):
            config.max_workers = 0


class TestGlobalAppConfig:
    """Test GlobalAppConfig model."""

    def test_default_initialization(self) -> None:
        """Test GlobalAppConfig with default values."""
        config = GlobalAppConfig()

        assert config.app_name == "PluggingerApp"
        assert config.log_level == LogLevel.INFO
        assert config.event_listener_fault_policy == EventListenerFaultPolicy.FAIL_FAST
        assert config.default_event_listener_timeout_seconds == 5.0
        assert len(config.executors) == 1
        assert config.executors[0].name == "default"
        assert config.plugin_configs == {}
        assert config.max_fractal_depth == 10

    def test_custom_initialization(self) -> None:
        """Test GlobalAppConfig with custom values."""
        custom_executor = ExecutorConfig(name="custom", max_workers=8)
        plugin_configs = {"plugin1": {"setting": "value"}}

        config = GlobalAppConfig(
            app_name="MyApp",
            log_level=LogLevel.DEBUG,
            event_listener_fault_policy=EventListenerFaultPolicy.LOG_AND_CONTINUE,
            default_event_listener_timeout_seconds=10.0,
            executors=[custom_executor],
            plugin_configs=plugin_configs,
            max_fractal_depth=5
        )

        assert config.app_name == "MyApp"
        assert config.log_level == LogLevel.DEBUG
        assert config.event_listener_fault_policy == EventListenerFaultPolicy.LOG_AND_CONTINUE
        assert config.default_event_listener_timeout_seconds == 10.0
        assert len(config.executors) == 1
        assert config.executors[0].name == "custom"
        assert config.plugin_configs == plugin_configs
        assert config.max_fractal_depth == 5

    def test_app_name_validation_min_length(self) -> None:
        """Test that app_name must have minimum length."""
        with pytest.raises(ValidationError) as exc_info:
            GlobalAppConfig(app_name="")

        errors = exc_info.value.errors()
        assert any("at least 1 character" in str(error) for error in errors)

    def test_timeout_validation_positive(self) -> None:
        """Test that timeout must be positive."""
        with pytest.raises(ValidationError) as exc_info:
            GlobalAppConfig(default_event_listener_timeout_seconds=0.0)

        errors = exc_info.value.errors()
        assert any("greater than 0" in str(error) for error in errors)

    def test_timeout_validation_negative(self) -> None:
        """Test that timeout cannot be negative."""
        with pytest.raises(ValidationError) as exc_info:
            GlobalAppConfig(default_event_listener_timeout_seconds=-1.0)

        errors = exc_info.value.errors()
        assert any("greater than 0" in str(error) for error in errors)

    def test_max_fractal_depth_validation_positive(self) -> None:
        """Test that max_fractal_depth must be positive."""
        with pytest.raises(ValidationError) as exc_info:
            GlobalAppConfig(max_fractal_depth=0)

        errors = exc_info.value.errors()
        assert any("greater than or equal to 1" in str(error) for error in errors)

    def test_executors_default_factory(self) -> None:
        """Test that executors default factory creates independent instances."""
        config1 = GlobalAppConfig()
        config2 = GlobalAppConfig()

        # Should be different instances
        assert config1.executors is not config2.executors
        # But should have same content
        assert config1.executors[0].name == config2.executors[0].name

    def test_plugin_configs_default_factory(self) -> None:
        """Test that plugin_configs default factory creates independent instances."""
        config1 = GlobalAppConfig()
        config2 = GlobalAppConfig()

        # Should be different instances
        assert config1.plugin_configs is not config2.plugin_configs
        # Both should be empty
        assert config1.plugin_configs == {}
        assert config2.plugin_configs == {}

    def test_extra_fields_forbidden(self) -> None:
        """Test that extra fields are forbidden."""
        with pytest.raises(ValidationError) as exc_info:
            GlobalAppConfig(extra_field="not_allowed")  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any("extra_field" in str(error) for error in errors)


class TestPluginLockInfo:
    """Test PluginLockInfo model."""

    def test_basic_initialization(self) -> None:
        """Test PluginLockInfo with required fields."""
        info = PluginLockInfo(
            name="test_plugin",
            version="1.0.0",
            instance_id="test_plugin_001"
        )

        assert info.name == "test_plugin"
        assert info.version == "1.0.0"
        assert info.instance_id == "test_plugin_001"
        assert info.source_location is None

    def test_initialization_with_source_location(self) -> None:
        """Test PluginLockInfo with source location."""
        info = PluginLockInfo(
            name="test_plugin",
            version="1.0.0",
            instance_id="test_plugin_001",
            source_location="/path/to/plugin"
        )

        assert info.source_location == "/path/to/plugin"

    def test_required_fields(self) -> None:
        """Test that required fields are enforced."""
        with pytest.raises(ValidationError) as exc_info:
            PluginLockInfo()  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        required_fields = {("name",), ("version",), ("instance_id",)}
        error_locs = {error.get("loc") for error in errors}
        assert required_fields.issubset(error_locs)

    def test_extra_fields_forbidden(self) -> None:
        """Test that extra fields are forbidden."""
        with pytest.raises(ValidationError) as exc_info:
            PluginLockInfo(
                name="test",
                version="1.0.0",
                instance_id="test_001",
                extra_field="not_allowed"  # type: ignore[call-arg]
            )

        errors = exc_info.value.errors()
        assert any("extra_field" in str(error) for error in errors)


class TestPythonPackageLockInfo:
    """Test PythonPackageLockInfo model."""

    def test_basic_initialization(self) -> None:
        """Test PythonPackageLockInfo with required fields."""
        info = PythonPackageLockInfo(
            name="requests",
            version="2.28.1"
        )

        assert info.name == "requests"
        assert info.version == "2.28.1"
        assert info.source == "pypi"  # default
        assert info.integrity_hash is None

    def test_initialization_with_all_fields(self) -> None:
        """Test PythonPackageLockInfo with all fields."""
        info = PythonPackageLockInfo(
            name="custom_package",
            version="1.0.0",
            source="git",
            integrity_hash="sha256:abc123"
        )

        assert info.name == "custom_package"
        assert info.version == "1.0.0"
        assert info.source == "git"
        assert info.integrity_hash == "sha256:abc123"

    def test_source_literal_validation(self) -> None:
        """Test that source field accepts only valid literals."""
        valid_sources = ["pypi", "git", "local", "other"]

        for source in valid_sources:
            info = PythonPackageLockInfo(name="test", version="1.0.0", source=source)  # type: ignore[arg-type]
            assert info.source == source

    def test_source_invalid_literal(self) -> None:
        """Test that invalid source values are rejected."""
        with pytest.raises(ValidationError) as exc_info:
            PythonPackageLockInfo(
                name="test",
                version="1.0.0",
                source="invalid_source"  # type: ignore[arg-type]
            )

        errors = exc_info.value.errors()
        assert any("source" in str(error) for error in errors)

    def test_required_fields(self) -> None:
        """Test that required fields are enforced."""
        with pytest.raises(ValidationError) as exc_info:
            PythonPackageLockInfo()  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        required_fields = {("name",), ("version",)}
        error_locs = {error.get("loc") for error in errors}
        assert required_fields.issubset(error_locs)


class TestPluggingerLockfileMetadata:
    """Test PluggingerLockfileMetadata model."""

    def test_default_initialization(self) -> None:
        """Test PluggingerLockfileMetadata with defaults."""
        metadata = PluggingerLockfileMetadata()

        assert metadata.lockfile_version == "1.0.0"
        assert isinstance(metadata.created_at, datetime)
        assert metadata.created_by == "plugginger-cli"
        assert metadata.python_version is None

    def test_custom_initialization(self) -> None:
        """Test PluggingerLockfileMetadata with custom values."""
        custom_time = datetime(2023, 1, 1, 12, 0, 0)
        metadata = PluggingerLockfileMetadata(
            lockfile_version="2.0.0",
            created_at=custom_time,
            created_by="custom-tool",
            python_version="3.11.0"
        )

        assert metadata.lockfile_version == "2.0.0"
        assert metadata.created_at == custom_time
        assert metadata.created_by == "custom-tool"
        assert metadata.python_version == "3.11.0"

    def test_created_at_default_factory(self) -> None:
        """Test that created_at default factory creates different times."""
        metadata1 = PluggingerLockfileMetadata()
        metadata2 = PluggingerLockfileMetadata()

        # Should be different datetime objects (though might be very close)
        assert metadata1.created_at is not metadata2.created_at

    def test_extra_fields_forbidden(self) -> None:
        """Test that extra fields are forbidden."""
        with pytest.raises(ValidationError) as exc_info:
            PluggingerLockfileMetadata(extra_field="not_allowed")  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any("extra_field" in str(error) for error in errors)


class TestPluggingerLockfile:
    """Test PluggingerLockfile model."""

    def test_minimal_initialization(self) -> None:
        """Test PluggingerLockfile with minimal required fields."""
        lockfile = PluggingerLockfile(app_name="TestApp")

        assert lockfile.app_name == "TestApp"
        assert isinstance(lockfile.metadata, PluggingerLockfileMetadata)
        assert lockfile.plugins == []
        assert lockfile.resolved_python_packages == []

    def test_full_initialization(self) -> None:
        """Test PluggingerLockfile with all fields."""
        metadata = PluggingerLockfileMetadata(lockfile_version="2.0.0")
        plugin = PluginLockInfo(name="plugin1", version="1.0.0", instance_id="plugin1_001")
        package = PythonPackageLockInfo(name="requests", version="2.28.1")

        lockfile = PluggingerLockfile(
            app_name="FullApp",
            metadata=metadata,
            plugins=[plugin],
            resolved_python_packages=[package]
        )

        assert lockfile.app_name == "FullApp"
        assert lockfile.metadata.lockfile_version == "2.0.0"
        assert len(lockfile.plugins) == 1
        assert lockfile.plugins[0].name == "plugin1"
        assert len(lockfile.resolved_python_packages) == 1
        assert lockfile.resolved_python_packages[0].name == "requests"

    def test_app_name_required(self) -> None:
        """Test that app_name is required."""
        with pytest.raises(ValidationError) as exc_info:
            PluggingerLockfile()  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any(error.get("loc") == ("app_name",) for error in errors)

    def test_metadata_default_factory(self) -> None:
        """Test that metadata default factory works."""
        lockfile1 = PluggingerLockfile(app_name="App1")
        lockfile2 = PluggingerLockfile(app_name="App2")

        # Should be different instances
        assert lockfile1.metadata is not lockfile2.metadata
        # But should have same default values
        assert lockfile1.metadata.lockfile_version == lockfile2.metadata.lockfile_version

    def test_lists_default_factory(self) -> None:
        """Test that list default factories create independent instances."""
        lockfile1 = PluggingerLockfile(app_name="App1")
        lockfile2 = PluggingerLockfile(app_name="App2")

        # Should be different instances
        assert lockfile1.plugins is not lockfile2.plugins
        assert lockfile1.resolved_python_packages is not lockfile2.resolved_python_packages

        # Both should be empty
        assert lockfile1.plugins == []
        assert lockfile1.resolved_python_packages == []

    def test_extra_fields_forbidden(self) -> None:
        """Test that extra fields are forbidden."""
        with pytest.raises(ValidationError) as exc_info:
            PluggingerLockfile(app_name="Test", extra_field="not_allowed")  # type: ignore[call-arg]

        errors = exc_info.value.errors()
        assert any("extra_field" in str(error) for error in errors)

    def test_complex_lockfile_serialization(self) -> None:
        """Test serialization of a complex lockfile."""
        plugin = PluginLockInfo(
            name="complex_plugin",
            version="2.1.0",
            instance_id="complex_plugin_001",
            source_location="/path/to/plugin"
        )
        package = PythonPackageLockInfo(
            name="pydantic",
            version="1.10.0",
            source="pypi",
            integrity_hash="sha256:def456"
        )

        lockfile = PluggingerLockfile(
            app_name="ComplexApp",
            plugins=[plugin],
            resolved_python_packages=[package]
        )

        data = lockfile.model_dump()

        assert data["app_name"] == "ComplexApp"
        assert len(data["plugins"]) == 1
        assert data["plugins"][0]["name"] == "complex_plugin"
        assert len(data["resolved_python_packages"]) == 1
        assert data["resolved_python_packages"][0]["name"] == "pydantic"


class TestModelInteractions:
    """Test interactions between different models."""

    def test_global_app_config_with_multiple_executors(self) -> None:
        """Test GlobalAppConfig with multiple ExecutorConfig instances."""
        executor1 = ExecutorConfig(name="fast", max_workers=2)
        executor2 = ExecutorConfig(name="slow", max_workers=8, thread_name_prefix="SlowWorker")

        config = GlobalAppConfig(executors=[executor1, executor2])

        assert len(config.executors) == 2
        assert config.executors[0].name == "fast"
        assert config.executors[1].name == "slow"
        assert config.executors[1].thread_name_prefix == "SlowWorker"

    def test_lockfile_with_nested_models(self) -> None:
        """Test PluggingerLockfile with nested model validation."""
        # This should work
        lockfile = PluggingerLockfile(
            app_name="NestedTest",
            plugins=[
                PluginLockInfo(name="plugin1", version="1.0.0", instance_id="p1"),
                PluginLockInfo(name="plugin2", version="2.0.0", instance_id="p2")
            ]
        )

        assert len(lockfile.plugins) == 2

        # Invalid nested model should fail
        with pytest.raises(ValidationError):
            PluggingerLockfile(
                app_name="NestedTest",
                plugins=[
                    {"name": "plugin1", "version": "1.0.0"}  # type: ignore[list-item]  # Missing instance_id
                ]
            )

    def test_model_config_consistency(self) -> None:
        """Test that all models have consistent configuration."""
        models = [
            ExecutorConfig,
            GlobalAppConfig,
            PluginLockInfo,
            PythonPackageLockInfo,
            PluggingerLockfileMetadata,
            PluggingerLockfile,
        ]

        for model in models:
            # Access model_config through an instance to avoid mypy issues
            if model == ExecutorConfig:
                instance = model(name="test")
            elif model == PluginLockInfo:
                instance = model(name="test", version="1.0.0", instance_id="test_001")
            elif model == PythonPackageLockInfo:
                instance = model(name="test", version="1.0.0")
            elif model == PluggingerLockfile:
                instance = model(app_name="test")
            else:
                instance = model()

            config = instance.model_config
            assert config.get("extra") == "forbid"
            assert config.get("validate_assignment") is True
