"""
Tests for automatic manifest generation functionality.

These tests verify that the PluggingerAppBuilder and PluggingerAppInstance
have the export_manifests methods available and can be called.
"""

import tempfile

import pytest


class TestAutoGenerationMethods:
    """Test that auto-generation methods exist and are callable."""

    def test_builder_has_export_manifests_method(self) -> None:
        """Test that PluggingerAppBuilder has export_manifests method."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")

        # Check method exists
        assert hasattr(builder, "export_manifests")
        assert callable(builder.export_manifests)

    def test_app_instance_has_export_manifests_method(self) -> None:
        """Test that PluggingerAppInstance has export_manifests method."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")
        app = builder.build()

        # Check method exists
        assert hasattr(app, "export_manifests")
        assert callable(app.export_manifests)

    def test_builder_export_manifests_no_plugins_raises_error(self) -> None:
        """Test that export_manifests raises error when no plugins are registered."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")

        with tempfile.TemporaryDirectory() as temp_dir:
            with pytest.raises(ValueError, match="No plugins registered"):
                builder.export_manifests(output_dir=temp_dir)

    def test_app_instance_export_manifests_no_plugins_raises_error(self) -> None:
        """Test that app instance export_manifests raises error when no plugins."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("empty_app")
        app = builder.build()

        with tempfile.TemporaryDirectory() as temp_dir:
            with pytest.raises(ValueError, match="No plugins registered"):
                app.export_manifests(output_dir=temp_dir)

    def test_builder_export_manifests_invalid_output_dir(self) -> None:
        """Test that export fails with invalid output directory."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")

        # First test should fail because no plugins are registered
        with pytest.raises(ValueError, match="No plugins registered"):
            builder.export_manifests(output_dir="")

        with pytest.raises(ValueError, match="No plugins registered"):
            builder.export_manifests(output_dir=None)  # type: ignore


class TestAutoGenerationDocumentation:
    """Test that auto-generation functionality is properly documented."""

    def test_builder_export_manifests_docstring(self) -> None:
        """Test that export_manifests method has proper documentation."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")
        export_method = builder.export_manifests

        # Check that method has docstring
        assert export_method.__doc__ is not None
        assert "Export plugin and application manifests" in export_method.__doc__

    def test_app_instance_export_manifests_docstring(self) -> None:
        """Test that app instance export_manifests method has proper documentation."""
        from plugginger.api.builder import PluggingerAppBuilder

        builder = PluggingerAppBuilder("test_app")
        app = builder.build()
        export_method = app.export_manifests

        # Check that method has docstring
        assert export_method.__doc__ is not None
        assert "Export plugin and application manifests" in export_method.__doc__
