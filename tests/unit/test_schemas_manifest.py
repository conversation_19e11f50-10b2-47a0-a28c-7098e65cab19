"""
Tests for plugin manifest schema definitions.

These tests verify that the Pydantic models for plugin manifests
work correctly and validate data as expected.
"""

from datetime import datetime

import pytest
from pydantic import ValidationError

from plugginger.schemas.manifest import (
    AppManifest,
    DependencyInfo,
    EventListenerInfo,
    ExecutionMode,
    ParameterInfo,
    ParameterKind,
    PluginManifest,
    PluginMetadata,
    PluginRuntime,
    ServiceInfo,
)


class TestParameterInfo:
    """Test ParameterInfo model."""

    def test_valid_parameter_info(self) -> None:
        """Test creating valid parameter info."""
        param = ParameterInfo(
            name="user_id",
            annotation="int",
            default=None,
            kind=ParameterKind.POSITIONAL_OR_KEYWORD,
        )

        assert param.name == "user_id"
        assert param.annotation == "int"
        assert param.default is None
        assert param.kind == ParameterKind.POSITIONAL_OR_KEYWORD

    def test_parameter_info_with_default(self) -> None:
        """Test parameter info with default value."""
        param = ParameterInfo(
            name="timeout",
            annotation="float",
            default=30.0,
            kind=ParameterKind.KEYWORD_ONLY,
        )

        assert param.default == 30.0
        assert param.kind == ParameterKind.KEYWORD_ONLY


class TestServiceInfo:
    """Test ServiceInfo model."""

    def test_valid_service_info(self) -> None:
        """Test creating valid service info."""
        service = ServiceInfo(
            name="get_user",
            method_name="get_user_by_id",
            description="Get user by ID",
            timeout_seconds=5.0,
            signature="(self, user_id: int) -> dict",
            parameters=[
                ParameterInfo(
                    name="user_id",
                    annotation="int",
                    kind=ParameterKind.POSITIONAL_OR_KEYWORD,
                )
            ],
            return_annotation="dict",
        )

        assert service.name == "get_user"
        assert service.method_name == "get_user_by_id"
        assert service.description == "Get user by ID"
        assert service.timeout_seconds == 5.0
        assert len(service.parameters) == 1
        assert service.parameters[0].name == "user_id"

    def test_service_info_minimal(self) -> None:
        """Test service info with minimal required fields."""
        service = ServiceInfo(
            name="simple_service",
            method_name="simple_method",
            signature="(self) -> None",
        )

        assert service.name == "simple_service"
        assert service.description is None
        assert service.timeout_seconds is None
        assert len(service.parameters) == 0


class TestEventListenerInfo:
    """Test EventListenerInfo model."""

    def test_valid_event_listener_info(self) -> None:
        """Test creating valid event listener info."""
        listener = EventListenerInfo(
            patterns=["user.created", "user.updated"],
            method_name="on_user_event",
            description="Handle user events",
            timeout_seconds=10.0,
            priority=5,
            signature="(self, event_data: dict, event_type: str) -> None",
            parameters=[
                ParameterInfo(
                    name="event_data",
                    annotation="dict",
                    kind=ParameterKind.POSITIONAL_OR_KEYWORD,
                ),
                ParameterInfo(
                    name="event_type",
                    annotation="str",
                    kind=ParameterKind.POSITIONAL_OR_KEYWORD,
                ),
            ],
        )

        assert listener.patterns == ["user.created", "user.updated"]
        assert listener.method_name == "on_user_event"
        assert listener.priority == 5
        assert len(listener.parameters) == 2

    def test_event_listener_info_minimal(self) -> None:
        """Test event listener info with minimal fields."""
        listener = EventListenerInfo(
            patterns=["app.startup"],
            method_name="on_startup",
            signature="(self, event_data: dict) -> None",
        )

        assert listener.patterns == ["app.startup"]
        assert listener.priority == 0  # Default value
        assert listener.timeout_seconds is None


class TestDependencyInfo:
    """Test DependencyInfo model."""

    def test_valid_dependency_info(self) -> None:
        """Test creating valid dependency info."""
        dep = DependencyInfo(
            name="database",
            version=">=1.0.0,<2.0.0",
            optional=False,
            description="Database connection service",
        )

        assert dep.name == "database"
        assert dep.version == ">=1.0.0,<2.0.0"
        assert dep.optional is False
        assert dep.description == "Database connection service"

    def test_dependency_info_minimal(self) -> None:
        """Test dependency info with minimal fields."""
        dep = DependencyInfo(name="logger")

        assert dep.name == "logger"
        assert dep.version is None
        assert dep.optional is False  # Default value
        assert dep.description is None


class TestPluginMetadata:
    """Test PluginMetadata model."""

    def test_valid_plugin_metadata(self) -> None:
        """Test creating valid plugin metadata."""
        metadata = PluginMetadata(
            name="user_manager",
            version="1.2.0",
            description="User management plugin",
            author="John Doe",
            homepage="https://example.com",
            repository="https://github.com/user/plugin",
            license="MIT",
            keywords=["user", "auth"],
        )

        assert metadata.name == "user_manager"
        assert metadata.version == "1.2.0"
        assert metadata.author == "John Doe"
        assert metadata.keywords == ["user", "auth"]

    def test_plugin_metadata_minimal(self) -> None:
        """Test plugin metadata with minimal fields."""
        metadata = PluginMetadata(
            name="simple_plugin",
            version="1.0.0",
        )

        assert metadata.name == "simple_plugin"
        assert metadata.version == "1.0.0"
        assert metadata.description is None
        assert metadata.keywords == []  # Default value

    def test_invalid_plugin_name(self) -> None:
        """Test that invalid plugin names are rejected."""
        with pytest.raises(ValidationError, match="must be a valid Python identifier"):
            PluginMetadata(name="invalid-name", version="1.0.0")

        with pytest.raises(ValidationError, match="cannot start or end with underscore"):
            PluginMetadata(name="_invalid", version="1.0.0")

        with pytest.raises(ValidationError, match="cannot start or end with underscore"):
            PluginMetadata(name="invalid_", version="1.0.0")


class TestPluginRuntime:
    """Test PluginRuntime model."""

    def test_valid_plugin_runtime(self) -> None:
        """Test creating valid plugin runtime."""
        runtime = PluginRuntime(
            execution_mode=ExecutionMode.PROCESS,
            python_version=">=3.9",
            plugginger_version=">=0.9.0",
        )

        assert runtime.execution_mode == ExecutionMode.PROCESS
        assert runtime.python_version == ">=3.9"
        assert runtime.plugginger_version == ">=0.9.0"

    def test_plugin_runtime_defaults(self) -> None:
        """Test plugin runtime with default values."""
        runtime = PluginRuntime(plugginger_version=">=0.9.0")

        assert runtime.execution_mode == ExecutionMode.THREAD  # Default
        assert runtime.python_version is None


class TestPluginManifest:
    """Test PluginManifest model."""

    def test_valid_plugin_manifest(self) -> None:
        """Test creating valid plugin manifest."""
        manifest = PluginManifest(
            metadata=PluginMetadata(
                name="test_plugin",
                version="1.0.0",
                author="Test Author",
            ),
            runtime=PluginRuntime(
                plugginger_version=">=0.9.0",
            ),
            dependencies=[
                DependencyInfo(name="logger"),
                DependencyInfo(name="database", version=">=1.0.0"),
            ],
            services=[
                ServiceInfo(
                    name="test_service",
                    method_name="test_method",
                    signature="(self) -> str",
                ),
            ],
            event_listeners=[
                EventListenerInfo(
                    patterns=["test.event"],
                    method_name="on_test",
                    signature="(self, event_data: dict) -> None",
                ),
            ],
        )

        assert manifest.manifest_version == "1.0.0"
        assert manifest.metadata.name == "test_plugin"
        assert len(manifest.dependencies) == 2
        assert len(manifest.services) == 1
        assert len(manifest.event_listeners) == 1
        assert isinstance(manifest.generated_at, datetime)

    def test_plugin_manifest_minimal(self) -> None:
        """Test plugin manifest with minimal fields."""
        manifest = PluginManifest(
            metadata=PluginMetadata(
                name="minimal_plugin",
                version="1.0.0",
            ),
            runtime=PluginRuntime(
                plugginger_version=">=0.9.0",
            ),
        )

        assert manifest.metadata.name == "minimal_plugin"
        assert len(manifest.dependencies) == 0  # Default
        assert len(manifest.services) == 0  # Default
        assert len(manifest.event_listeners) == 0  # Default
        assert manifest.config_schema is None


class TestAppManifest:
    """Test AppManifest model."""

    def test_valid_app_manifest(self) -> None:
        """Test creating valid app manifest."""
        manifest = AppManifest(
            app_name="test_app",
            app_version="2.0.0",
            description="Test application",
            plugins=["plugin1", "plugin2"],
            plugin_configs={
                "plugin1": {"setting": "value"},
                "plugin2": {"timeout": 30},
            },
            global_config={"debug": True},
        )

        assert manifest.app_name == "test_app"
        assert manifest.app_version == "2.0.0"
        assert manifest.plugins == ["plugin1", "plugin2"]
        assert manifest.plugin_configs["plugin1"]["setting"] == "value"
        assert manifest.global_config["debug"] is True

    def test_app_manifest_minimal(self) -> None:
        """Test app manifest with minimal fields."""
        manifest = AppManifest(
            app_name="minimal_app",
            app_version="1.0.0",
            plugins=["single_plugin"],
        )

        assert manifest.app_name == "minimal_app"
        assert manifest.plugins == ["single_plugin"]
        assert manifest.plugin_configs == {}  # Default
        assert manifest.global_config == {}  # Default


class TestExecutionMode:
    """Test ExecutionMode enum."""

    def test_execution_mode_values(self) -> None:
        """Test execution mode enum values."""
        assert ExecutionMode.THREAD.value == "thread"
        assert ExecutionMode.PROCESS.value == "process"
        assert ExecutionMode.EXTERNAL.value == "external"

    def test_execution_mode_in_runtime(self) -> None:
        """Test using execution mode in runtime."""
        runtime = PluginRuntime(
            execution_mode=ExecutionMode.EXTERNAL,
            plugginger_version=">=0.9.0",
        )

        assert runtime.execution_mode == ExecutionMode.EXTERNAL


class TestParameterKind:
    """Test ParameterKind enum."""

    def test_parameter_kind_values(self) -> None:
        """Test parameter kind enum values."""
        assert ParameterKind.POSITIONAL_ONLY.value == "POSITIONAL_ONLY"
        assert ParameterKind.POSITIONAL_OR_KEYWORD.value == "POSITIONAL_OR_KEYWORD"
        assert ParameterKind.VAR_POSITIONAL.value == "VAR_POSITIONAL"
        assert ParameterKind.KEYWORD_ONLY.value == "KEYWORD_ONLY"
        assert ParameterKind.VAR_KEYWORD.value == "VAR_KEYWORD"


class TestManifestSerialization:
    """Test manifest serialization and deserialization."""

    def test_manifest_to_dict(self) -> None:
        """Test converting manifest to dictionary."""
        manifest = PluginManifest(
            metadata=PluginMetadata(
                name="test_plugin",
                version="1.0.0",
            ),
            runtime=PluginRuntime(
                plugginger_version=">=0.9.0",
            ),
        )

        data = manifest.model_dump()

        assert data["manifest_version"] == "1.0.0"
        assert data["metadata"]["name"] == "test_plugin"
        assert data["runtime"]["execution_mode"] == "thread"

    def test_manifest_from_dict(self) -> None:
        """Test creating manifest from dictionary."""
        data = {
            "manifest_version": "1.0.0",
            "metadata": {
                "name": "test_plugin",
                "version": "1.0.0",
            },
            "runtime": {
                "execution_mode": "thread",
                "plugginger_version": ">=0.9.0",
            },
            "dependencies": [],
            "services": [],
            "event_listeners": [],
            "generated_at": "2025-01-15T10:30:00Z",
            "generated_by": "test",
        }

        manifest = PluginManifest.model_validate(data)

        assert manifest.metadata.name == "test_plugin"
        assert manifest.runtime.execution_mode == ExecutionMode.THREAD
