"""
Tests for breaking change policy implementation.

These tests verify that the deprecation and stability utilities work correctly
and that the breaking change policy is properly enforced.
"""

import warnings
from pathlib import Path

from plugginger.core.deprecation import (
    DeprecatedMixin,
    deprecated,
    deprecated_parameter,
    get_deprecation_info,
    is_deprecated,
    warn_experimental,
)
from plugginger.core.stability import (
    StabilityLevel,
    StabilityMixin,
    check_stability_compatibility,
    experimental,
    get_stability_info,
    get_stability_level,
    is_experimental,
    is_stable,
    is_stable_candidate,
    stability,
    stable,
    stable_candidate,
)


class TestBreakingChangePolicyDocumentation:
    """Test that the breaking change policy documentation exists and is complete."""

    def test_breaking_change_policy_exists(self) -> None:
        """Test that the breaking change policy document exists."""
        policy_path = Path("docs/BREAKING_CHANGE_POLICY.md")
        assert policy_path.exists(), "Breaking change policy document should exist"

        content = policy_path.read_text()
        assert "Plugginger Breaking Change Policy" in content
        assert "API Stability Levels" in content
        assert "What Constitutes a Breaking Change" in content

    def test_policy_covers_all_stability_levels(self) -> None:
        """Test that the policy document covers all stability levels."""
        policy_path = Path("docs/BREAKING_CHANGE_POLICY.md")
        content = policy_path.read_text()

        # Should cover all main stability levels
        assert "Stable Candidates" in content
        assert "Experimental APIs" in content
        assert "Internal APIs" in content

        # Should mention specific modules
        assert "plugginger.api.*" in content
        assert "plugginger.experimental.*" in content
        assert "plugginger._internal.*" in content

    def test_policy_includes_examples(self) -> None:
        """Test that the policy includes code examples."""
        policy_path = Path("docs/BREAKING_CHANGE_POLICY.md")
        content = policy_path.read_text()

        # Should contain code examples
        assert "```python" in content
        assert "@service" in content
        assert "@plugin" in content

        # Should show breaking vs non-breaking examples
        assert "BREAKING" in content
        assert "OK:" in content or "✅" in content


class TestDeprecationUtilities:
    """Test the deprecation utilities."""

    def test_deprecated_decorator(self) -> None:
        """Test the @deprecated decorator."""
        @deprecated("This function is deprecated", removal_version="v2.0.0")
        def old_function() -> str:
            return "old"

        # Should be marked as deprecated
        assert is_deprecated(old_function)

        # Should have deprecation info
        info = get_deprecation_info(old_function)
        assert info is not None
        assert "deprecated" in info["message"]
        assert info["removal_version"] == "v2.0.0"

        # Should issue warning when called
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = old_function()

            assert len(w) == 1
            assert issubclass(w[0].category, DeprecationWarning)
            assert "deprecated" in str(w[0].message)
            assert result == "old"

    def test_deprecated_with_replacement(self) -> None:
        """Test deprecated decorator with replacement suggestion."""
        @deprecated(
            "old_func is deprecated",
            replacement="new_func",
            removal_version="v2.0.0"
        )
        def old_func() -> str:
            return "old"

        info = get_deprecation_info(old_func)
        assert info is not None
        assert info["replacement"] == "new_func"

        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            old_func()

            assert len(w) == 1
            message = str(w[0].message)
            assert "new_func" in message
            assert "v2.0.0" in message

    def test_deprecated_parameter(self) -> None:
        """Test the @deprecated_parameter decorator."""
        @deprecated_parameter("old_param", replacement="new_param")
        def test_function(new_param: str, old_param: str | None = None) -> str:
            if old_param is not None:
                return old_param
            return new_param

        # Should not warn when using new parameter
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = test_function(new_param="new")

            assert len(w) == 0
            assert result == "new"

        # Should warn when using deprecated parameter
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = test_function(new_param="new", old_param="old")

            assert len(w) == 1
            assert issubclass(w[0].category, DeprecationWarning)
            assert "old_param" in str(w[0].message)
            assert "new_param" in str(w[0].message)
            assert result == "old"

    def test_deprecated_mixin(self) -> None:
        """Test the DeprecatedMixin class."""
        class OldClass(DeprecatedMixin):
            __deprecation_message__ = "OldClass is deprecated"
            __removal_version__ = "v2.0.0"
            __replacement__ = "NewClass"

            def __init__(self, value: str) -> None:
                self.value = value

        # Should warn when instantiating
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            instance = OldClass("test")

            assert len(w) == 1
            assert issubclass(w[0].category, DeprecationWarning)
            message = str(w[0].message)
            assert "OldClass is deprecated" in message
            assert "NewClass" in message
            assert "v2.0.0" in message
            assert instance.value == "test"

    def test_warn_experimental(self) -> None:
        """Test the warn_experimental function."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            warn_experimental("plugginger.experimental.test")

            assert len(w) == 1
            assert issubclass(w[0].category, FutureWarning)
            message = str(w[0].message)
            assert "experimental" in message
            assert "plugginger.experimental.test" in message

    def test_non_deprecated_function(self) -> None:
        """Test that non-deprecated functions work normally."""
        def normal_function() -> str:
            return "normal"

        assert not is_deprecated(normal_function)
        assert get_deprecation_info(normal_function) is None

        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = normal_function()

            assert len(w) == 0
            assert result == "normal"


class TestStabilityUtilities:
    """Test the stability utilities."""

    def test_stability_levels(self) -> None:
        """Test that all stability levels are defined."""
        assert StabilityLevel.EXPERIMENTAL.value == "experimental"
        assert StabilityLevel.STABLE_CANDIDATE.value == "stable_candidate"
        assert StabilityLevel.STABLE.value == "stable"
        assert StabilityLevel.DEPRECATED.value == "deprecated"

    def test_stability_decorator(self) -> None:
        """Test the @stability decorator."""
        @stability(StabilityLevel.STABLE_CANDIDATE, since="v0.9.0")
        def stable_func() -> str:
            return "stable"

        level = get_stability_level(stable_func)
        assert level == StabilityLevel.STABLE_CANDIDATE

        info = get_stability_info(stable_func)
        assert info is not None
        assert info["level"] == StabilityLevel.STABLE_CANDIDATE
        assert info["since"] == "v0.9.0"

        assert is_stable_candidate(stable_func)
        assert not is_experimental(stable_func)
        assert not is_stable(stable_func)

    def test_experimental_decorator(self) -> None:
        """Test the @experimental decorator."""
        @experimental(since="v0.9.0")
        def experimental_func() -> str:
            return "experimental"

        assert is_experimental(experimental_func)
        assert get_stability_level(experimental_func) == StabilityLevel.EXPERIMENTAL

        # Should issue warning when called
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = experimental_func()

            assert len(w) == 1
            assert issubclass(w[0].category, FutureWarning)
            assert "experimental" in str(w[0].message)
            assert result == "experimental"

    def test_stable_candidate_decorator(self) -> None:
        """Test the @stable_candidate decorator."""
        @stable_candidate(since="v0.9.0")
        def candidate_func() -> str:
            return "candidate"

        assert is_stable_candidate(candidate_func)
        assert get_stability_level(candidate_func) == StabilityLevel.STABLE_CANDIDATE

        # Should not issue warning
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = candidate_func()

            assert len(w) == 0
            assert result == "candidate"

    def test_stable_decorator(self) -> None:
        """Test the @stable decorator."""
        @stable(since="v1.0.0")
        def stable_func() -> str:
            return "stable"

        assert is_stable(stable_func)
        assert get_stability_level(stable_func) == StabilityLevel.STABLE

        # Should not issue warning
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = stable_func()

            assert len(w) == 0
            assert result == "stable"

    def test_stability_mixin(self) -> None:
        """Test the StabilityMixin class."""
        class ExperimentalClass(StabilityMixin):
            __stability_level__ = StabilityLevel.EXPERIMENTAL
            __stability_since__ = "v0.9.0"

            def __init__(self, value: str) -> None:
                self.value = value

        # Should warn when instantiating experimental class
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            exp_instance = ExperimentalClass("test")

            assert len(w) == 1
            assert issubclass(w[0].category, FutureWarning)
            assert "experimental" in str(w[0].message)
            assert exp_instance.value == "test"

        class StableClass(StabilityMixin):
            __stability_level__ = StabilityLevel.STABLE

            def __init__(self, value: str) -> None:
                self.value = value

        # Should not warn for stable class
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            stable_instance = StableClass("test")

            assert len(w) == 0
            assert stable_instance.value == "test"

    def test_stability_compatibility_check(self) -> None:
        """Test the stability compatibility checker."""
        @experimental()
        def exp_func() -> str:
            return "exp"

        @stable_candidate()
        def candidate_func() -> str:
            return "candidate"

        @stable()
        def stable_func() -> str:
            return "stable"

        def unmarked_func() -> str:
            return "unmarked"

        # Experimental should only be compatible with experimental requirement
        assert check_stability_compatibility(StabilityLevel.EXPERIMENTAL, exp_func)
        assert not check_stability_compatibility(StabilityLevel.STABLE_CANDIDATE, exp_func)
        assert not check_stability_compatibility(StabilityLevel.STABLE, exp_func)

        # Stable candidate should be compatible with experimental and candidate requirements
        assert check_stability_compatibility(StabilityLevel.EXPERIMENTAL, candidate_func)
        assert check_stability_compatibility(StabilityLevel.STABLE_CANDIDATE, candidate_func)
        assert not check_stability_compatibility(StabilityLevel.STABLE, candidate_func)

        # Stable should be compatible with all requirements
        assert check_stability_compatibility(StabilityLevel.EXPERIMENTAL, stable_func)
        assert check_stability_compatibility(StabilityLevel.STABLE_CANDIDATE, stable_func)
        assert check_stability_compatibility(StabilityLevel.STABLE, stable_func)

        # Unmarked should be treated as stable (backward compatibility)
        assert check_stability_compatibility(StabilityLevel.EXPERIMENTAL, unmarked_func)
        assert check_stability_compatibility(StabilityLevel.STABLE_CANDIDATE, unmarked_func)
        assert check_stability_compatibility(StabilityLevel.STABLE, unmarked_func)

    def test_unmarked_functions(self) -> None:
        """Test that unmarked functions work normally."""
        def normal_func() -> str:
            return "normal"

        assert get_stability_level(normal_func) is None
        assert get_stability_info(normal_func) is None
        assert not is_experimental(normal_func)
        assert not is_stable_candidate(normal_func)
        assert not is_stable(normal_func)

        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = normal_func()

            assert len(w) == 0
            assert result == "normal"
