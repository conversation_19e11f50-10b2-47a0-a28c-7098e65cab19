# tests/unit/test_api_background.py

"""
Unit tests for plugginger.api.background module.

Tests the @background_task decorator, utility functions, and background task
execution functionality.
"""

from __future__ import annotations

import asyncio
import inspect
from typing import Any
from unittest.mock import AsyncMock, patch

import pytest

from plugginger.api.background import (
    DEFAULT_EXECUTOR_NAME,
    background_task,
    get_background_task_executor,
    get_background_task_original,
    is_background_task,
)
from plugginger.core.exceptions import BackgroundTaskError


# Test functions for decoration
def sync_function(x: int, y: int = 10) -> int:
    """Synchronous test function."""
    return x + y


def sync_function_with_exception(x: int) -> int:
    """Synchronous function that raises an exception."""
    if x < 0:
        raise ValueError("Negative value not allowed")
    return x * 2


async def async_function(x: int) -> int:
    """Asynchronous test function."""
    return x * 2


def cpu_intensive_function(data: str) -> str:
    """Simulate CPU-intensive work."""
    return data.upper() * 100


def blocking_io_function(url: str) -> str:
    """Simulate blocking I/O operation."""
    # Simulate some blocking operation
    import time
    time.sleep(0.001)  # Very short sleep for testing
    return f"Response from {url}"


class TestBackgroundTaskDecorator:
    """Test @background_task decorator functionality."""

    def test_background_task_direct_decoration(self) -> None:
        """Test direct decoration: @background_task"""
        @background_task
        def test_func(x: int) -> int:
            return x * 2

        # Check that function is properly decorated
        assert is_background_task(test_func)
        assert get_background_task_executor(test_func) == DEFAULT_EXECUTOR_NAME
        assert get_background_task_original(test_func).__name__ == "test_func"
        assert inspect.iscoroutinefunction(test_func)

    def test_background_task_factory_decoration(self) -> None:
        """Test factory decoration: @background_task(executor="custom")"""
        @background_task(executor="custom_pool")
        def test_func(x: int) -> int:
            return x * 2

        # Check that function is properly decorated
        assert is_background_task(test_func)
        assert get_background_task_executor(test_func) == "custom_pool"
        assert get_background_task_original(test_func).__name__ == "test_func"
        assert inspect.iscoroutinefunction(test_func)

    def test_background_task_default_executor(self) -> None:
        """Test decoration with default executor."""
        @background_task()
        def test_func(x: int) -> int:
            return x * 2

        assert is_background_task(test_func)
        assert get_background_task_executor(test_func) == DEFAULT_EXECUTOR_NAME

    def test_background_task_preserves_function_metadata(self) -> None:
        """Test that decorator preserves function metadata."""
        @background_task
        def test_function(x: int, y: str = "default") -> str:
            """Test function docstring."""
            return f"{x}:{y}"

        assert test_function.__name__ == "test_function"
        assert test_function.__doc__ == "Test function docstring."

        # Check that original function signature is preserved in metadata
        original = get_background_task_original(test_function)
        assert original.__name__ == "test_function"
        assert original.__doc__ == "Test function docstring."

    def test_background_task_async_function_error(self) -> None:
        """Test that decorator raises error for async functions."""
        with pytest.raises(TypeError) as exc_info:
            @background_task
            async def async_test_func(x: int) -> int:
                return x * 2

        assert "can only be applied to synchronous functions" in str(exc_info.value)
        assert "async_test_func" in str(exc_info.value)

    def test_background_task_async_function_factory_error(self) -> None:
        """Test that factory decorator raises error for async functions."""
        with pytest.raises(TypeError) as exc_info:
            @background_task(executor="test")
            async def async_test_func(x: int) -> int:
                return x * 2

        assert "can only be applied to synchronous functions" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_background_task_execution(self) -> None:
        """Test that background task executes correctly."""
        @background_task
        def multiply(x: int, y: int) -> int:
            return x * y

        result = await multiply(5, 3)
        assert result == 15

    @pytest.mark.asyncio
    async def test_background_task_execution_with_kwargs(self) -> None:
        """Test background task execution with keyword arguments."""
        @background_task
        def add_numbers(x: int, y: int = 10, z: int = 5) -> int:
            return x + y + z

        result = await add_numbers(1, y=2, z=3)
        assert result == 6

    @pytest.mark.asyncio
    async def test_background_task_execution_exception(self) -> None:
        """Test that background task properly handles exceptions."""
        @background_task
        def failing_function(x: int) -> int:
            if x < 0:
                raise ValueError("Negative value")
            return x

        # Should work with positive value
        result = await failing_function(5)
        assert result == 5

        # Should raise BackgroundTaskError for negative value
        with pytest.raises(BackgroundTaskError) as exc_info:
            await failing_function(-1)

        assert "Background task 'failing_function' failed" in str(exc_info.value)
        assert exc_info.value.__cause__ is not None
        assert isinstance(exc_info.value.__cause__, ValueError)

    @pytest.mark.asyncio
    async def test_background_task_with_custom_executor(self) -> None:
        """Test background task with custom executor name."""
        @background_task(executor="io_pool")
        def io_operation(data: str) -> str:
            return f"processed: {data}"

        # Check metadata
        assert get_background_task_executor(io_operation) == "io_pool"

        # Test execution
        result = await io_operation("test_data")
        assert result == "processed: test_data"

    @pytest.mark.asyncio
    async def test_background_task_concurrent_execution(self) -> None:
        """Test concurrent execution of background tasks."""
        @background_task
        def slow_operation(x: int) -> int:
            import time
            time.sleep(0.001)  # Very short sleep for testing
            return x * 2

        # Execute multiple tasks concurrently
        tasks = [slow_operation(i) for i in range(5)]
        results = await asyncio.gather(*tasks)

        assert results == [0, 2, 4, 6, 8]

    def test_background_task_protocol_compliance(self) -> None:
        """Test that decorated function complies with BackgroundTaskCallable protocol."""
        @background_task(executor="test_pool")
        def test_func(x: int) -> int:
            return x

        # Check protocol attributes
        assert hasattr(test_func, "_background_task_executor")
        assert hasattr(test_func, "_background_task_original")
        assert hasattr(test_func, "_is_background_task")

        # Check attribute values
        assert test_func._background_task_executor == "test_pool"
        assert test_func._background_task_original.__name__ == "test_func"
        assert test_func._is_background_task is True

        # Check that it's callable and returns awaitable
        assert callable(test_func)
        result = test_func(5)
        assert inspect.iscoroutine(result)

        # Clean up the coroutine
        result.close()


class TestIsBackgroundTask:
    """Test is_background_task function."""

    def test_is_background_task_decorated_function(self) -> None:
        """Test that function returns True for decorated functions."""
        @background_task
        def test_func(x: int) -> int:
            return x

        assert is_background_task(test_func) is True

    def test_is_background_task_factory_decorated_function(self) -> None:
        """Test that function returns True for factory decorated functions."""
        @background_task(executor="custom")
        def test_func(x: int) -> int:
            return x

        assert is_background_task(test_func) is True

    def test_is_background_task_regular_function(self) -> None:
        """Test that function returns False for regular functions."""
        def regular_func(x: int) -> int:
            return x

        assert is_background_task(regular_func) is False

    def test_is_background_task_async_function(self) -> None:
        """Test that function returns False for async functions."""
        async def async_func(x: int) -> int:
            return x

        assert is_background_task(async_func) is False

    def test_is_background_task_non_function(self) -> None:
        """Test that function returns False for non-function objects."""
        assert is_background_task("not a function") is False  # type: ignore[arg-type]
        assert is_background_task(123) is False  # type: ignore[arg-type]
        assert is_background_task(None) is False  # type: ignore[arg-type]

    def test_is_background_task_class_method(self) -> None:
        """Test with class methods."""
        class TestClass:
            @background_task
            def method(self, x: int) -> int:
                return x

        assert is_background_task(TestClass.method) is True

        instance = TestClass()
        assert is_background_task(instance.method) is True


class TestGetBackgroundTaskExecutor:
    """Test get_background_task_executor function."""

    def test_get_background_task_executor_decorated_function(self) -> None:
        """Test getting executor from decorated function."""
        @background_task(executor="custom_executor")
        def test_func(x: int) -> int:
            return x

        assert get_background_task_executor(test_func) == "custom_executor"

    def test_get_background_task_executor_default(self) -> None:
        """Test getting default executor from decorated function."""
        @background_task
        def test_func(x: int) -> int:
            return x

        assert get_background_task_executor(test_func) == DEFAULT_EXECUTOR_NAME

    def test_get_background_task_executor_regular_function(self) -> None:
        """Test getting executor from regular function returns default."""
        def regular_func(x: int) -> int:
            return x

        assert get_background_task_executor(regular_func) == DEFAULT_EXECUTOR_NAME

    def test_get_background_task_executor_non_function(self) -> None:
        """Test getting executor from non-function returns default."""
        assert get_background_task_executor("not a function") == DEFAULT_EXECUTOR_NAME  # type: ignore[arg-type]
        assert get_background_task_executor(None) == DEFAULT_EXECUTOR_NAME  # type: ignore[arg-type]


class TestGetBackgroundTaskOriginal:
    """Test get_background_task_original function."""

    def test_get_background_task_original_decorated_function(self) -> None:
        """Test getting original function from decorated function."""
        def original_func(x: int) -> int:
            return x * 2

        decorated_func = background_task(original_func)
        retrieved_original = get_background_task_original(decorated_func)

        assert retrieved_original is original_func
        assert retrieved_original.__name__ == "original_func"

    def test_get_background_task_original_factory_decorated(self) -> None:
        """Test getting original function from factory decorated function."""
        @background_task(executor="test")
        def test_func(x: int) -> int:
            return x

        original = get_background_task_original(test_func)
        assert original.__name__ == "test_func"

    def test_get_background_task_original_regular_function(self) -> None:
        """Test getting original from regular function returns itself."""
        def regular_func(x: int) -> int:
            return x

        assert get_background_task_original(regular_func) is regular_func

    def test_get_background_task_original_non_function(self) -> None:
        """Test getting original from non-function returns itself."""
        non_func = "not a function"
        assert get_background_task_original(non_func) is non_func  # type: ignore[arg-type,comparison-overlap]


class TestBackgroundTaskIntegration:
    """Test integration scenarios for background tasks."""

    @pytest.mark.asyncio
    async def test_background_task_with_complex_data_types(self) -> None:
        """Test background task with complex data types."""
        @background_task
        def process_data(data: dict[str, Any]) -> dict[str, Any]:
            result: dict[str, Any] = {}
            for key, value in data.items():
                if isinstance(value, str):
                    result[key] = value.upper()
                elif isinstance(value, int | float):
                    result[key] = value * 2
                else:
                    result[key] = str(value)
            return result

        input_data = {
            "name": "test",
            "count": 5,
            "ratio": 2.5,
            "items": [1, 2, 3]
        }

        result = await process_data(input_data)

        assert result["name"] == "TEST"
        assert result["count"] == 10
        assert result["ratio"] == 5.0
        assert result["items"] == "[1, 2, 3]"

    @pytest.mark.asyncio
    async def test_background_task_with_class_methods(self) -> None:
        """Test background task decoration on class methods."""
        class DataProcessor:
            def __init__(self, multiplier: int) -> None:
                self.multiplier = multiplier

            @background_task
            def process(self, value: int) -> int:
                return value * self.multiplier

            @background_task(executor="io_pool")
            def process_async(self, value: int) -> int:
                return value + self.multiplier

        processor = DataProcessor(3)

        # Test both methods
        result1 = await processor.process(10)
        assert result1 == 30

        result2 = await processor.process_async(10)
        assert result2 == 13

        # Check metadata
        assert is_background_task(processor.process)
        assert is_background_task(processor.process_async)
        assert get_background_task_executor(processor.process) == DEFAULT_EXECUTOR_NAME
        assert get_background_task_executor(processor.process_async) == "io_pool"

    @pytest.mark.asyncio
    async def test_background_task_exception_propagation(self) -> None:
        """Test that exceptions are properly propagated with context."""
        @background_task
        def divide_numbers(a: int, b: int) -> float:
            if b == 0:
                raise ZeroDivisionError("Cannot divide by zero")
            return a / b

        # Normal execution should work
        result = await divide_numbers(10, 2)
        assert result == 5.0

        # Exception should be wrapped in BackgroundTaskError
        with pytest.raises(BackgroundTaskError) as exc_info:
            await divide_numbers(10, 0)

        assert "Background task 'divide_numbers' failed" in str(exc_info.value)
        assert isinstance(exc_info.value.__cause__, ZeroDivisionError)
        assert "Cannot divide by zero" in str(exc_info.value.__cause__)

    @pytest.mark.asyncio
    async def test_background_task_with_generator_function(self) -> None:
        """Test background task with generator function."""
        @background_task
        def create_sequence(start: int, count: int) -> list[int]:
            # Convert generator to list for serialization
            return list(range(start, start + count))

        result = await create_sequence(5, 3)
        assert result == [5, 6, 7]

    def test_background_task_multiple_decorations(self) -> None:
        """Test applying background_task decorator multiple times raises error."""
        # Multiple decorations should fail because the inner decorator makes it async
        with pytest.raises(TypeError) as exc_info:
            @background_task(executor="outer")
            @background_task(executor="inner")
            def test_func(x: int) -> int:
                return x

        assert "can only be applied to synchronous functions" in str(exc_info.value)
        assert "test_func" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_background_task_with_default_arguments(self) -> None:
        """Test background task with default arguments."""
        @background_task
        def calculate(base: int, multiplier: int = 2, offset: int = 0) -> int:
            return base * multiplier + offset

        # Test with all defaults
        result1 = await calculate(5)
        assert result1 == 10

        # Test with some arguments
        result2 = await calculate(5, multiplier=3)
        assert result2 == 15

        # Test with all arguments
        result3 = await calculate(5, multiplier=3, offset=1)
        assert result3 == 16

    @pytest.mark.asyncio
    async def test_background_task_with_variable_arguments(self) -> None:
        """Test background task with *args and **kwargs."""
        @background_task
        def sum_all(*args: int, **kwargs: int) -> int:
            total = sum(args)
            total += sum(kwargs.values())
            return total

        result = await sum_all(1, 2, 3, a=4, b=5)
        assert result == 15

    def test_background_task_constants(self) -> None:
        """Test module constants."""
        assert DEFAULT_EXECUTOR_NAME == "default"

    def test_background_task_protocol_type_checking(self) -> None:
        """Test that BackgroundTaskCallable protocol is properly defined."""
        @background_task
        def test_func(x: int) -> int:
            return x

        # Check that the function has all required protocol attributes
        assert hasattr(test_func, "_background_task_executor")
        assert hasattr(test_func, "_background_task_original")
        assert hasattr(test_func, "_is_background_task")
        assert callable(test_func)

        # Check types
        assert isinstance(test_func._background_task_executor, str)
        assert callable(test_func._background_task_original)
        assert isinstance(test_func._is_background_task, bool)

    @pytest.mark.asyncio
    async def test_background_task_with_mock_executor(self) -> None:
        """Test background task execution with mocked executor."""
        @background_task(executor="test_executor")
        def mock_operation(value: str) -> str:
            return f"processed_{value}"

        # Mock the event loop's run_in_executor method
        with patch("asyncio.get_running_loop") as mock_get_loop:
            mock_loop = AsyncMock()
            mock_get_loop.return_value = mock_loop
            mock_loop.run_in_executor.return_value = "mocked_result"

            result = await mock_operation("test")

            # Verify the executor was called correctly
            mock_loop.run_in_executor.assert_called_once()
            call_args = mock_loop.run_in_executor.call_args
            assert call_args[0][0] is None  # Default executor
            assert callable(call_args[0][1])  # Lambda function

            assert result == "mocked_result"

    @pytest.mark.asyncio
    async def test_background_task_executor_exception_handling(self) -> None:
        """Test exception handling in executor execution."""
        @background_task
        def failing_task() -> None:
            raise RuntimeError("Task failed")

        # Mock the executor to raise an exception
        with patch("asyncio.get_running_loop") as mock_get_loop:
            mock_loop = AsyncMock()
            mock_get_loop.return_value = mock_loop
            mock_loop.run_in_executor.side_effect = RuntimeError("Executor failed")

            with pytest.raises(BackgroundTaskError) as exc_info:
                await failing_task()

            assert "Background task 'failing_task' failed" in str(exc_info.value)
            assert isinstance(exc_info.value.__cause__, RuntimeError)
            assert "Executor failed" in str(exc_info.value.__cause__)


class TestBackgroundTaskEdgeCases:
    """Test edge cases and error conditions."""

    def test_background_task_with_lambda(self) -> None:
        """Test that background_task works with lambda functions."""
        # Note: This is more of a theoretical test since lambdas can't be decorated directly
        lambda_func = lambda x: x * 2  # noqa: E731
        decorated_lambda = background_task(lambda_func)

        assert is_background_task(decorated_lambda)
        assert get_background_task_original(decorated_lambda) is lambda_func

    def test_background_task_function_without_name(self) -> None:
        """Test background task with function that has no __name__."""
        class CallableClass:
            def __call__(self, x: int) -> int:
                return x * 2

        callable_obj = CallableClass()

        # This should work even though callable objects might not have __name__
        decorated_callable = background_task(callable_obj)
        assert is_background_task(decorated_callable)

    @pytest.mark.asyncio
    async def test_background_task_with_none_return(self) -> None:
        """Test background task that returns None."""
        @background_task
        def void_operation(x: int) -> None:
            # Simulate side effect
            pass

        result = await void_operation(5)
        assert result is None

    def test_background_task_decorator_factory_without_parentheses(self) -> None:
        """Test that decorator factory requires parentheses."""
        # This should work - direct decoration
        @background_task
        def func1(x: int) -> int:
            return x

        # This should also work - factory decoration
        @background_task()
        def func2(x: int) -> int:
            return x

        assert is_background_task(func1)
        assert is_background_task(func2)

    def test_background_task_with_empty_executor_name(self) -> None:
        """Test background task with empty executor name."""
        @background_task(executor="")
        def test_func(x: int) -> int:
            return x

        # Empty string should be preserved
        assert get_background_task_executor(test_func) == ""

    def test_background_task_with_none_executor_name(self) -> None:
        """Test background task with None executor name."""
        # This should use the default value due to function signature
        @background_task()
        def test_func(x: int) -> int:
            return x

        assert get_background_task_executor(test_func) == DEFAULT_EXECUTOR_NAME
