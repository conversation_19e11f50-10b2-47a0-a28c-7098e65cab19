# tests/unit/test_internal_graph.py

"""
Unit tests for plugginger._internal.graph module.

Tests the DependencyGraph class and its functionality for managing
dependency relationships and topological sorting.
"""

from __future__ import annotations

import pytest

from plugginger._internal.graph import DependencyGraph, T_Node
from plugginger.core.exceptions import CircularDependencyError


class TestDependencyGraphBasics:
    """Test basic DependencyGraph functionality."""

    def test_initialization(self) -> None:
        """Test DependencyGraph initialization."""
        graph = DependencyGraph[str]()
        assert len(graph) == 0
        assert graph.get_all_nodes() == set()

    def test_add_single_node(self) -> None:
        """Test adding a single node."""
        graph = DependencyGraph[str]()
        graph.add_node("node1")

        assert len(graph) == 1
        assert "node1" in graph
        assert graph.get_all_nodes() == {"node1"}

    def test_add_multiple_nodes(self) -> None:
        """Test adding multiple nodes."""
        graph = DependencyGraph[str]()
        graph.add_node("node1")
        graph.add_node("node2")
        graph.add_node("node3")

        assert len(graph) == 3
        assert "node1" in graph
        assert "node2" in graph
        assert "node3" in graph
        assert graph.get_all_nodes() == {"node1", "node2", "node3"}

    def test_add_duplicate_node_idempotent(self) -> None:
        """Test that adding duplicate nodes is idempotent."""
        graph = DependencyGraph[str]()
        graph.add_node("node1")
        graph.add_node("node1")  # Add same node again

        assert len(graph) == 1
        assert "node1" in graph

    def test_contains_operator(self) -> None:
        """Test the __contains__ operator."""
        graph = DependencyGraph[str]()
        graph.add_node("existing")

        assert "existing" in graph
        assert "nonexistent" not in graph

    def test_len_operator(self) -> None:
        """Test the __len__ operator."""
        graph = DependencyGraph[str]()
        assert len(graph) == 0

        graph.add_node("node1")
        assert len(graph) == 1

        graph.add_node("node2")
        assert len(graph) == 2

    def test_repr_method(self) -> None:
        """Test the __repr__ method."""
        graph = DependencyGraph[str]()
        repr_str = repr(graph)
        assert "DependencyGraph" in repr_str
        assert "nodes=0" in repr_str
        assert "edges=0" in repr_str

    def test_repr_method_with_data(self) -> None:
        """Test __repr__ with nodes and edges."""
        graph = DependencyGraph[str]()
        graph.add_node("node1")
        graph.add_node("node2")
        graph.add_dependency_edge("node1", "node2")

        repr_str = repr(graph)
        assert "nodes=2" in repr_str
        assert "edges=1" in repr_str


class TestDependencyEdges:
    """Test dependency edge functionality."""

    def test_add_dependency_edge_basic(self) -> None:
        """Test adding a basic dependency edge."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("prerequisite", "dependent")

        assert len(graph) == 2
        assert "prerequisite" in graph
        assert "dependent" in graph

    def test_add_dependency_edge_auto_adds_nodes(self) -> None:
        """Test that adding edges automatically adds nodes."""
        graph = DependencyGraph[str]()
        # Don't add nodes explicitly
        graph.add_dependency_edge("node1", "node2")

        assert "node1" in graph
        assert "node2" in graph
        assert len(graph) == 2

    def test_get_prerequisites_basic(self) -> None:
        """Test getting prerequisites of a node."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("prereq1", "dependent")
        graph.add_dependency_edge("prereq2", "dependent")

        prerequisites = graph.get_prerequisites("dependent")
        assert prerequisites == {"prereq1", "prereq2"}

    def test_get_prerequisites_empty(self) -> None:
        """Test getting prerequisites of a node with no dependencies."""
        graph = DependencyGraph[str]()
        graph.add_node("independent")

        prerequisites = graph.get_prerequisites("independent")
        assert prerequisites == set()

    def test_get_prerequisites_nonexistent_node(self) -> None:
        """Test getting prerequisites of nonexistent node raises KeyError."""
        graph = DependencyGraph[str]()

        with pytest.raises(KeyError) as exc_info:
            graph.get_prerequisites("nonexistent")

        assert "Node 'nonexistent' not found in dependency graph" in str(exc_info.value)

    def test_get_dependents_basic(self) -> None:
        """Test getting dependents of a node."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("prerequisite", "dep1")
        graph.add_dependency_edge("prerequisite", "dep2")

        dependents = graph.get_dependents("prerequisite")
        assert dependents == {"dep1", "dep2"}

    def test_get_dependents_empty(self) -> None:
        """Test getting dependents of a node with no dependents."""
        graph = DependencyGraph[str]()
        graph.add_node("leaf")

        dependents = graph.get_dependents("leaf")
        assert dependents == set()

    def test_get_dependents_nonexistent_node(self) -> None:
        """Test getting dependents of nonexistent node raises KeyError."""
        graph = DependencyGraph[str]()

        with pytest.raises(KeyError) as exc_info:
            graph.get_dependents("nonexistent")

        assert "Node 'nonexistent' not found in dependency graph" in str(exc_info.value)

    def test_prerequisites_and_dependents_consistency(self) -> None:
        """Test that prerequisites and dependents are consistent."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("A", "B")

        # A should be a prerequisite of B
        assert "A" in graph.get_prerequisites("B")
        # B should be a dependent of A
        assert "B" in graph.get_dependents("A")

    def test_multiple_dependency_chains(self) -> None:
        """Test multiple dependency chains."""
        graph = DependencyGraph[str]()
        # Chain: A -> B -> C
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("B", "C")

        assert graph.get_prerequisites("B") == {"A"}
        assert graph.get_prerequisites("C") == {"B"}
        assert graph.get_dependents("A") == {"B"}
        assert graph.get_dependents("B") == {"C"}

    def test_complex_dependency_graph(self) -> None:
        """Test a more complex dependency graph."""
        graph = DependencyGraph[str]()
        # A -> C, B -> C, C -> D
        graph.add_dependency_edge("A", "C")
        graph.add_dependency_edge("B", "C")
        graph.add_dependency_edge("C", "D")

        assert graph.get_prerequisites("C") == {"A", "B"}
        assert graph.get_prerequisites("D") == {"C"}
        assert graph.get_dependents("A") == {"C"}
        assert graph.get_dependents("B") == {"C"}
        assert graph.get_dependents("C") == {"D"}


class TestTopologicalSort:
    """Test topological sorting functionality."""

    def test_topological_sort_empty_graph(self) -> None:
        """Test topological sort on empty graph."""
        graph = DependencyGraph[str]()
        result = graph.topological_sort()
        assert result == []

    def test_topological_sort_single_node(self) -> None:
        """Test topological sort with single node."""
        graph = DependencyGraph[str]()
        graph.add_node("single")

        result = graph.topological_sort()
        assert result == ["single"]

    def test_topological_sort_no_dependencies(self) -> None:
        """Test topological sort with multiple independent nodes."""
        graph = DependencyGraph[str]()
        graph.add_node("C")
        graph.add_node("A")
        graph.add_node("B")

        result = graph.topological_sort()
        # Should be sorted alphabetically for determinism
        assert result == ["A", "B", "C"]

    def test_topological_sort_linear_chain(self) -> None:
        """Test topological sort with linear dependency chain."""
        graph = DependencyGraph[str]()
        # Chain: A -> B -> C
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("B", "C")

        result = graph.topological_sort()
        assert result == ["A", "B", "C"]

    def test_topological_sort_diamond_dependency(self) -> None:
        """Test topological sort with diamond dependency pattern."""
        graph = DependencyGraph[str]()
        # Diamond: A -> B, A -> C, B -> D, C -> D
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("A", "C")
        graph.add_dependency_edge("B", "D")
        graph.add_dependency_edge("C", "D")

        result = graph.topological_sort()
        # A must come first, D must come last
        assert result[0] == "A"
        assert result[-1] == "D"
        # B and C can be in either order but both before D
        assert "B" in result[1:3]
        assert "C" in result[1:3]

    def test_topological_sort_complex_graph(self) -> None:
        """Test topological sort with complex dependency graph."""
        graph = DependencyGraph[str]()
        # Complex graph: A -> C, B -> C, C -> E, D -> E, E -> F
        graph.add_dependency_edge("A", "C")
        graph.add_dependency_edge("B", "C")
        graph.add_dependency_edge("C", "E")
        graph.add_dependency_edge("D", "E")
        graph.add_dependency_edge("E", "F")

        result = graph.topological_sort()

        # Verify ordering constraints
        assert result.index("A") < result.index("C")
        assert result.index("B") < result.index("C")
        assert result.index("C") < result.index("E")
        assert result.index("D") < result.index("E")
        assert result.index("E") < result.index("F")

    def test_topological_sort_deterministic(self) -> None:
        """Test that topological sort is deterministic."""
        graph = DependencyGraph[str]()
        graph.add_node("Z")
        graph.add_node("A")
        graph.add_node("M")

        # Run multiple times to ensure consistency
        results = [graph.topological_sort() for _ in range(5)]

        # All results should be identical
        for result in results[1:]:
            assert result == results[0]

        # Should be alphabetically sorted
        assert results[0] == ["A", "M", "Z"]


class TestCircularDependencies:
    """Test circular dependency detection."""

    def test_simple_circular_dependency(self) -> None:
        """Test detection of simple circular dependency."""
        graph = DependencyGraph[str]()
        # Create cycle: A -> B -> A
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("B", "A")

        with pytest.raises(CircularDependencyError) as exc_info:
            graph.topological_sort()

        error_msg = str(exc_info.value)
        assert "Circular dependency detected" in error_msg
        assert "A" in error_msg
        assert "B" in error_msg

    def test_self_dependency(self) -> None:
        """Test detection of self-dependency."""
        graph = DependencyGraph[str]()
        # Create self-cycle: A -> A
        graph.add_dependency_edge("A", "A")

        with pytest.raises(CircularDependencyError) as exc_info:
            graph.topological_sort()

        error_msg = str(exc_info.value)
        assert "Circular dependency detected" in error_msg
        assert "A" in error_msg

    def test_complex_circular_dependency(self) -> None:
        """Test detection of complex circular dependency."""
        graph = DependencyGraph[str]()
        # Create cycle: A -> B -> C -> A
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("B", "C")
        graph.add_dependency_edge("C", "A")

        with pytest.raises(CircularDependencyError) as exc_info:
            graph.topological_sort()

        error_msg = str(exc_info.value)
        assert "Circular dependency detected" in error_msg
        # All nodes in cycle should be mentioned
        assert "A" in error_msg
        assert "B" in error_msg
        assert "C" in error_msg

    def test_partial_circular_dependency(self) -> None:
        """Test graph with some valid nodes and some in cycles."""
        graph = DependencyGraph[str]()
        # Valid part: X -> Y
        graph.add_dependency_edge("X", "Y")
        # Circular part: A -> B -> A
        graph.add_dependency_edge("A", "B")
        graph.add_dependency_edge("B", "A")

        with pytest.raises(CircularDependencyError) as exc_info:
            graph.topological_sort()

        error_msg = str(exc_info.value)
        assert "Circular dependency detected" in error_msg
        # Only circular nodes should be mentioned
        assert "A" in error_msg
        assert "B" in error_msg


class TestGenericTypes:
    """Test generic type functionality."""

    def test_string_nodes(self) -> None:
        """Test graph with string nodes."""
        graph = DependencyGraph[str]()
        graph.add_node("string_node")
        assert "string_node" in graph

    def test_integer_nodes(self) -> None:
        """Test graph with integer nodes."""
        graph = DependencyGraph[int]()
        graph.add_node(42)
        graph.add_node(1)
        graph.add_dependency_edge(1, 42)

        result = graph.topological_sort()
        assert result == [1, 42]

    def test_mixed_type_consistency(self) -> None:
        """Test that graph maintains type consistency."""
        # This is more of a type-checking test
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("test")

        # The following would be caught by mypy if uncommented:
        # graph.add_node(123)  # type: ignore


class TestTypeVariable:
    """Test the T_Node TypeVar."""

    def test_type_variable_exists(self) -> None:
        """Test that T_Node TypeVar is defined."""
        assert T_Node is not None

    def test_type_variable_name(self) -> None:
        """Test that T_Node TypeVar has correct name."""
        assert T_Node.__name__ == "T_Node"


class TestEdgeCases:
    """Test edge cases and error conditions."""

    def test_get_all_nodes_returns_copy(self) -> None:
        """Test that get_all_nodes returns a copy, not reference."""
        graph = DependencyGraph[str]()
        graph.add_node("test")

        nodes1 = graph.get_all_nodes()
        nodes2 = graph.get_all_nodes()

        # Should be equal but not the same object
        assert nodes1 == nodes2
        assert nodes1 is not nodes2

        # Modifying returned set shouldn't affect graph
        nodes1.add("external")
        assert "external" not in graph

    def test_get_prerequisites_returns_copy(self) -> None:
        """Test that get_prerequisites returns a copy."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("A", "B")

        prereqs1 = graph.get_prerequisites("B")
        prereqs2 = graph.get_prerequisites("B")

        assert prereqs1 == prereqs2
        assert prereqs1 is not prereqs2

    def test_get_dependents_returns_copy(self) -> None:
        """Test that get_dependents returns a copy."""
        graph = DependencyGraph[str]()
        graph.add_dependency_edge("A", "B")

        deps1 = graph.get_dependents("A")
        deps2 = graph.get_dependents("A")

        assert deps1 == deps2
        assert deps1 is not deps2

    def test_large_graph_performance(self) -> None:
        """Test performance with larger graph (basic smoke test)."""
        graph = DependencyGraph[str]()

        # Create a larger graph
        for i in range(100):
            graph.add_node(f"node_{i}")
            if i > 0:
                graph.add_dependency_edge(f"node_{i-1}", f"node_{i}")

        # Should complete without issues
        result = graph.topological_sort()
        assert len(result) == 100
        assert result[0] == "node_0"
        assert result[-1] == "node_99"
