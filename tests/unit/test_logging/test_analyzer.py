# tests/unit/logging/test_analyzer.py

"""Unit tests for the LogAnalyzer class."""

from __future__ import annotations

import json
import tempfile
from pathlib import Path
from typing import Any

import pytest

from plugginger.logging import LogAnalyzer


class TestLogAnalyzer:
    """Test cases for LogAnalyzer class."""
    
    def test_parse_structured_logs(self) -> None:
        """Test parsing structured logs from file."""
        # Create sample log data
        log_entries: list[dict[str, str] | str] = [
            {"timestamp": "2025-06-02T10:00:00Z", "event_type": "build_started", "level": "info"},
            {"timestamp": "2025-06-02T10:00:01Z", "event_type": "plugin_loaded", "level": "info"},
            "invalid json line",  # Should be skipped
            {"timestamp": "2025-06-02T10:00:02Z", "event_type": "build_completed", "level": "info"}
        ]
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            for entry in log_entries:
                if isinstance(entry, dict):
                    f.write(json.dumps(entry) + '\n')
                else:
                    f.write(str(entry) + '\n')
            temp_path = f.name
        
        try:
            # Parse logs
            parsed_logs = LogAnalyzer.parse_structured_logs(temp_path)
            
            # Verify results
            assert len(parsed_logs) == 3  # Invalid line should be skipped
            assert parsed_logs[0]["event_type"] == "build_started"
            assert parsed_logs[1]["event_type"] == "plugin_loaded"
            assert parsed_logs[2]["event_type"] == "build_completed"
        finally:
            Path(temp_path).unlink()
    
    def test_analyze_build_performance(self) -> None:
        """Test build performance analysis."""
        sample_logs: list[dict[str, Any]] = [
            {
                "event_type": "build_started",
                "duration_ms": 100,
                "level": "info"
            },
            {
                "event_type": "build_completed",
                "duration_ms": 50,
                "level": "info"
            },
            {
                "event_type": "performance_metric",
                "duration_ms": 200,
                "context": {"operation": "plugin_inclusion.test_plugin"},
                "level": "info"
            },
            {
                "event_type": "performance_metric",
                "duration_ms": 300,
                "context": {"operation": "dependency_resolution"},
                "level": "info"
            },
            {
                "event_type": "plugin_loaded",
                "context": {"plugin_name": "test_plugin"},
                "level": "info"
            },
            {
                "event_type": "plugin_failed",
                "level": "error"
            }
        ]
        
        analysis = LogAnalyzer.analyze_build_performance(sample_logs)
        
        assert analysis["total_build_time_ms"] == 150  # 100 + 50
        assert analysis["plugin_loading_time_ms"] == 200  # Only plugin_inclusion operation
        assert len(analysis["slowest_operations"]) == 2
        assert analysis["slowest_operations"][0]["duration_ms"] == 300  # dependency_resolution
        assert analysis["error_count"] == 1
        assert analysis["plugin_count"] == 1  # Only test_plugin has plugin_name
    
    def test_detect_issues(self) -> None:
        """Test issue detection from logs."""
        sample_logs = [
            {
                "event_type": "performance_metric",
                "duration_ms": 1500,  # Slow operation
                "context": {"operation": "slow_plugin_loading"},
                "level": "info"
            },
            {
                "event_type": "performance_metric",
                "duration_ms": 500,  # Normal operation
                "context": {"operation": "fast_operation"},
                "level": "info"
            },
            {"event_type": "plugin_failed", "level": "error"},
            {"event_type": "build_failed", "level": "error"},
            {"event_type": "dependency_error", "level": "error"},
            {"event_type": "validation_error", "level": "error"},
            {"event_type": "config_error", "level": "error"},
            {"event_type": "runtime_error", "level": "error"}  # 6 errors total
        ]
        
        issues = LogAnalyzer.detect_issues(sample_logs)
        
        # Should detect slow operation and high error rate
        assert len(issues) == 2
        
        slow_operation_issue = next(issue for issue in issues if issue["type"] == "slow_operation")
        assert slow_operation_issue["operation"] == "slow_plugin_loading"
        assert slow_operation_issue["duration_ms"] == 1500
        
        high_error_issue = next(issue for issue in issues if issue["type"] == "high_error_rate")
        assert high_error_issue["error_count"] == 6
    
    def test_generate_performance_report(self) -> None:
        """Test comprehensive performance report generation."""
        sample_logs = [
            {
                "event_type": "build_started",
                "duration_ms": 100,
                "level": "info"
            },
            {
                "event_type": "performance_metric",
                "duration_ms": 200,
                "context": {"operation": "plugin_loading"},
                "level": "info"
            },
            {
                "event_type": "performance_metric",
                "duration_ms": 300,
                "context": {"operation": "dependency_resolution"},
                "level": "info"
            },
            {
                "event_type": "plugin_failed",
                "level": "error"
            }
        ]
        
        report = LogAnalyzer.generate_performance_report(sample_logs)
        
        # Check report structure
        assert "summary" in report
        assert "performance" in report
        assert "issues" in report
        assert "recommendations" in report
        
        # Check summary
        summary = report["summary"]
        assert summary["total_events"] == 4
        assert summary["performance_events"] == 2
        assert summary["error_events"] == 1
        assert summary["average_operation_time_ms"] == 250  # (200 + 300) / 2
        
        # Check recommendations
        recommendations = report["recommendations"]
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
    
    def test_generate_recommendations_high_build_time(self) -> None:
        """Test recommendations for high build time."""
        analysis = {"total_build_time_ms": 6000, "plugin_loading_time_ms": 1000, "error_count": 0}
        issues: list[dict[str, str]] = []
        
        recommendations = LogAnalyzer._generate_recommendations(analysis, issues)
        
        assert any("Build time is high" in rec for rec in recommendations)
    
    def test_generate_recommendations_high_plugin_loading_time(self) -> None:
        """Test recommendations for high plugin loading time."""
        analysis = {"total_build_time_ms": 1000, "plugin_loading_time_ms": 600, "error_count": 0}
        issues: list[dict[str, str]] = []
        
        recommendations = LogAnalyzer._generate_recommendations(analysis, issues)
        
        assert any("Plugin loading takes significant time" in rec for rec in recommendations)
    
    def test_generate_recommendations_slow_operations(self) -> None:
        """Test recommendations for slow operations."""
        analysis = {"total_build_time_ms": 1000, "plugin_loading_time_ms": 200, "error_count": 0}
        issues = [
            {"type": "slow_operation", "operation": "test_op", "duration_ms": 1500},
            {"type": "slow_operation", "operation": "test_op2", "duration_ms": 2000}
        ]
        
        recommendations = LogAnalyzer._generate_recommendations(analysis, issues)
        
        assert any("Found 2 slow operations" in rec for rec in recommendations)
    
    def test_generate_recommendations_errors(self) -> None:
        """Test recommendations for errors."""
        analysis = {"total_build_time_ms": 1000, "plugin_loading_time_ms": 200, "error_count": 3}
        issues: list[dict[str, str]] = []
        
        recommendations = LogAnalyzer._generate_recommendations(analysis, issues)
        
        assert any("Errors detected" in rec for rec in recommendations)
