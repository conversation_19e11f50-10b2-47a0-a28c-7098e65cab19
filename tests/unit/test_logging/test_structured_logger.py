# tests/unit/logging/test_structured_logger.py

"""Unit tests for the StructuredLogger class."""

from __future__ import annotations

import json
import logging
import time
from unittest.mock import Mock, patch

import pytest

from plugginger.logging import EventType, LogLevel, OperationTimer, StructuredLogger


class TestStructuredLogger:
    """Test cases for StructuredLogger class."""
    
    def test_init_with_defaults(self) -> None:
        """Test StructuredLogger initialization with default values."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        assert logger.name == "test"
        assert logger.base_logger is base_logger
        assert logger.enable_structured is True
        assert logger.correlation_id is not None
        assert len(logger.correlation_id) == 8
        assert logger.session_start is not None
    
    def test_init_with_custom_correlation_id(self) -> None:
        """Test StructuredLogger initialization with custom correlation ID."""
        base_logger = Mock(spec=logging.Logger)
        custom_id = "custom123"
        logger = StructuredLogger("test", base_logger, correlation_id=custom_id)
        
        assert logger.correlation_id == custom_id
    
    def test_generate_correlation_id(self) -> None:
        """Test correlation ID generation."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        correlation_id = logger._generate_correlation_id()
        assert isinstance(correlation_id, str)
        assert len(correlation_id) == 8
    
    def test_log_event_structured_enabled(self) -> None:
        """Test structured event logging when enabled."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger, enable_structured=True)
        
        with patch.object(logger.base_logger, 'log') as mock_log:
            logger.log_event(
                event_type=EventType.PLUGIN_LOADED,
                level=LogLevel.INFO,
                message="Test plugin loaded",
                context={"plugin_name": "test_plugin"},
                metrics={"duration_ms": 100.5}
            )
        
        # Verify log was called with correct level
        mock_log.assert_called_once()
        call_args = mock_log.call_args
        assert call_args[0][0] == logging.INFO
        
        # Parse and verify JSON structure
        logged_json = call_args[0][1]
        logged_data = json.loads(logged_json)
        
        assert logged_data["event_type"] == "plugin_loaded"
        assert logged_data["level"] == "info"
        assert logged_data["message"] == "Test plugin loaded"
        assert logged_data["context"]["plugin_name"] == "test_plugin"
        assert logged_data["metrics"]["duration_ms"] == 100.5
        assert "timestamp" in logged_data
        assert "correlation_id" in logged_data
        assert "logger_name" in logged_data
        assert "session_duration_ms" in logged_data
    
    def test_log_event_structured_disabled(self) -> None:
        """Test event logging when structured logging is disabled."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger, enable_structured=False)
        
        with patch.object(logger.base_logger, 'log') as mock_log:
            logger.log_event(
                event_type=EventType.PLUGIN_LOADED,
                message="Test plugin loaded"
            )
        
        mock_log.assert_called_once_with(
            logging.INFO,
            "[plugin_loaded] Test plugin loaded"
        )
    
    def test_log_build_event_started(self) -> None:
        """Test logging build started event."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        with patch.object(logger, 'log_event') as mock_log_event:
            logger.log_build_event(
                phase="initialization",
                status="started",
                context={"app_name": "test_app"},
                duration_ms=50.0
            )
        
        mock_log_event.assert_called_once_with(
            event_type=EventType.BUILD_STARTED,
            level=LogLevel.INFO,
            message="Build phase 'initialization' started",
            context={
                "build_phase": "initialization",
                "build_status": "started",
                "app_name": "test_app"
            },
            duration_ms=50.0
        )
    
    def test_log_build_event_failed(self) -> None:
        """Test logging build failed event."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        with patch.object(logger, 'log_event') as mock_log_event:
            logger.log_build_event(
                phase="plugin_loading",
                status="failed"
            )
        
        mock_log_event.assert_called_once_with(
            event_type=EventType.BUILD_FAILED,
            level=LogLevel.ERROR,
            message="Build phase 'plugin_loading' failed",
            context={
                "build_phase": "plugin_loading",
                "build_status": "failed"
            },
            duration_ms=None
        )
    
    def test_log_performance(self) -> None:
        """Test performance logging."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        with patch.object(logger, 'log_event') as mock_log_event:
            logger.log_performance(
                operation="plugin_instantiation",
                duration_ms=250.5,
                context={"plugin_count": 5}
            )
        
        mock_log_event.assert_called_once_with(
            event_type=EventType.PERFORMANCE_METRIC,
            message="Performance: plugin_instantiation took 250.50ms",
            context={
                "operation": "plugin_instantiation",
                "plugin_count": 5
            },
            metrics={
                "duration_ms": 250.5,
                "operations_per_second": 1000 / 250.5
            },
            duration_ms=250.5
        )
    
    def test_create_timer(self) -> None:
        """Test timer creation."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        timer = logger.create_timer("test_operation")
        
        assert isinstance(timer, OperationTimer)
        assert timer.logger is logger
        assert timer.operation == "test_operation"


class TestOperationTimer:
    """Test cases for OperationTimer class."""
    
    def test_timer_context_manager(self) -> None:
        """Test OperationTimer as context manager."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        
        with patch.object(logger, 'log_performance') as mock_log_perf:
            with logger.create_timer("test_operation"):
                time.sleep(0.01)  # Simulate work
        
        # Verify performance was logged
        mock_log_perf.assert_called_once()
        call_args = mock_log_perf.call_args
        assert call_args[0][0] == "test_operation"
        assert call_args[0][1] >= 10  # At least 10ms
    
    def test_timer_manual_usage(self) -> None:
        """Test OperationTimer manual usage."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        timer = OperationTimer(logger, "manual_operation")
        
        with patch.object(logger, 'log_performance') as mock_log_perf:
            timer.__enter__()
            time.sleep(0.01)
            timer.__exit__(None, None, None)
        
        mock_log_perf.assert_called_once()
        call_args = mock_log_perf.call_args
        assert call_args[0][0] == "manual_operation"
        assert call_args[0][1] >= 10  # At least 10ms
    
    def test_timer_without_start(self) -> None:
        """Test OperationTimer when start_time is None."""
        base_logger = Mock(spec=logging.Logger)
        logger = StructuredLogger("test", base_logger)
        timer = OperationTimer(logger, "no_start_operation")
        
        with patch.object(logger, 'log_performance') as mock_log_perf:
            timer.__exit__(None, None, None)
        
        # Should not log performance if start_time is None
        mock_log_perf.assert_not_called()
