# tests/unit/test_core_constants.py

"""
Unit tests for plugginger.core.constants module.

Tests all constants defined in the constants module to ensure they have
the correct types, values, and immutability characteristics.
"""

from __future__ import annotations

from plugginger.core.constants import (
    BACKGROUND_TASK_EXECUTOR_KEY,
    DEFAULT_APP_NAME,
    DEFAULT_EVENT_TIMEOUT_SECONDS,
    DEFAULT_EXECUTOR_NAME,
    DEFAULT_PLUGIN_TIMEOUT_SECONDS,
    DEFAULT_SERVICE_TIMEOUT_SECONDS,
    EVENT_METADATA_KEY,
    MAX_EVENT_NAME_LENGTH,
    MAX_PLUGIN_NAME_LENGTH,
    MAX_SERVICE_NAME_LENGTH,
    PLUGIN_METADATA_KEY,
    RESERVED_PLUGIN_NAMES,
    SERVICE_METADATA_KEY,
)


class TestMetadataKeys:
    """Test metadata key constants."""

    def test_plugin_metadata_key_type_and_value(self) -> None:
        """Test PLUGIN_METADATA_KEY has correct type and value."""
        assert isinstance(PLUGIN_METADATA_KEY, str)
        assert PLUGIN_METADATA_KEY == "_plugginger_is_plugin_class_meta"

    def test_service_metadata_key_type_and_value(self) -> None:
        """Test SERVICE_METADATA_KEY has correct type and value."""
        assert isinstance(SERVICE_METADATA_KEY, str)
        assert SERVICE_METADATA_KEY == "_plugginger_service_config"

    def test_event_metadata_key_type_and_value(self) -> None:
        """Test EVENT_METADATA_KEY has correct type and value."""
        assert isinstance(EVENT_METADATA_KEY, str)
        assert EVENT_METADATA_KEY == "_plugginger_event_config"

    def test_background_task_executor_key_type_and_value(self) -> None:
        """Test BACKGROUND_TASK_EXECUTOR_KEY has correct type and value."""
        assert isinstance(BACKGROUND_TASK_EXECUTOR_KEY, str)
        assert BACKGROUND_TASK_EXECUTOR_KEY == "_plugginger_background_executor_name"

    def test_metadata_keys_are_unique(self) -> None:
        """Test that all metadata keys are unique."""
        keys = [
            PLUGIN_METADATA_KEY,
            SERVICE_METADATA_KEY,
            EVENT_METADATA_KEY,
            BACKGROUND_TASK_EXECUTOR_KEY,
        ]
        assert len(keys) == len(set(keys)), "Metadata keys must be unique"

    def test_metadata_keys_start_with_plugginger_prefix(self) -> None:
        """Test that all metadata keys start with the expected prefix."""
        prefix = "_plugginger_"
        keys = [
            PLUGIN_METADATA_KEY,
            SERVICE_METADATA_KEY,
            EVENT_METADATA_KEY,
            BACKGROUND_TASK_EXECUTOR_KEY,
        ]
        for key in keys:
            assert key.startswith(prefix), f"Key {key} should start with {prefix}"


class TestDefaultValues:
    """Test default value constants."""

    def test_default_executor_name_type_and_value(self) -> None:
        """Test DEFAULT_EXECUTOR_NAME has correct type and value."""
        assert isinstance(DEFAULT_EXECUTOR_NAME, str)
        assert DEFAULT_EXECUTOR_NAME == "default"

    def test_default_app_name_type_and_value(self) -> None:
        """Test DEFAULT_APP_NAME has correct type and value."""
        assert isinstance(DEFAULT_APP_NAME, str)
        assert DEFAULT_APP_NAME == "PluggingerApp"

    def test_default_names_are_non_empty(self) -> None:
        """Test that default name constants are non-empty strings."""
        assert len(DEFAULT_EXECUTOR_NAME) > 0
        assert len(DEFAULT_APP_NAME) > 0


class TestTimeoutConstants:
    """Test timeout-related constants."""

    def test_default_plugin_timeout_type_and_value(self) -> None:
        """Test DEFAULT_PLUGIN_TIMEOUT_SECONDS has correct type and value."""
        assert isinstance(DEFAULT_PLUGIN_TIMEOUT_SECONDS, float)
        assert DEFAULT_PLUGIN_TIMEOUT_SECONDS == 60.0

    def test_default_event_timeout_type_and_value(self) -> None:
        """Test DEFAULT_EVENT_TIMEOUT_SECONDS has correct type and value."""
        assert isinstance(DEFAULT_EVENT_TIMEOUT_SECONDS, float)
        assert DEFAULT_EVENT_TIMEOUT_SECONDS == 30.0

    def test_default_service_timeout_type_and_value(self) -> None:
        """Test DEFAULT_SERVICE_TIMEOUT_SECONDS has correct type and value."""
        assert isinstance(DEFAULT_SERVICE_TIMEOUT_SECONDS, float)
        assert DEFAULT_SERVICE_TIMEOUT_SECONDS == 30.0

    def test_timeout_values_are_positive(self) -> None:
        """Test that all timeout values are positive."""
        timeouts = [
            DEFAULT_PLUGIN_TIMEOUT_SECONDS,
            DEFAULT_EVENT_TIMEOUT_SECONDS,
            DEFAULT_SERVICE_TIMEOUT_SECONDS,
        ]
        for timeout in timeouts:
            assert timeout > 0, f"Timeout {timeout} must be positive"

    def test_timeout_values_are_reasonable(self) -> None:
        """Test that timeout values are within reasonable bounds."""
        # Should be at least 1 second and at most 10 minutes
        timeouts = [
            DEFAULT_PLUGIN_TIMEOUT_SECONDS,
            DEFAULT_EVENT_TIMEOUT_SECONDS,
            DEFAULT_SERVICE_TIMEOUT_SECONDS,
        ]
        for timeout in timeouts:
            assert 1.0 <= timeout <= 600.0, f"Timeout {timeout} should be between 1 and 600 seconds"


class TestLimitConstants:
    """Test limit-related constants."""

    def test_max_plugin_name_length_type_and_value(self) -> None:
        """Test MAX_PLUGIN_NAME_LENGTH has correct type and value."""
        assert isinstance(MAX_PLUGIN_NAME_LENGTH, int)
        assert MAX_PLUGIN_NAME_LENGTH == 100

    def test_max_service_name_length_type_and_value(self) -> None:
        """Test MAX_SERVICE_NAME_LENGTH has correct type and value."""
        assert isinstance(MAX_SERVICE_NAME_LENGTH, int)
        assert MAX_SERVICE_NAME_LENGTH == 100

    def test_max_event_name_length_type_and_value(self) -> None:
        """Test MAX_EVENT_NAME_LENGTH has correct type and value."""
        assert isinstance(MAX_EVENT_NAME_LENGTH, int)
        assert MAX_EVENT_NAME_LENGTH == 200

    def test_length_limits_are_positive(self) -> None:
        """Test that all length limits are positive."""
        limits = [
            MAX_PLUGIN_NAME_LENGTH,
            MAX_SERVICE_NAME_LENGTH,
            MAX_EVENT_NAME_LENGTH,
        ]
        for limit in limits:
            assert limit > 0, f"Length limit {limit} must be positive"

    def test_length_limits_are_reasonable(self) -> None:
        """Test that length limits are within reasonable bounds."""
        # Should be at least 10 characters and at most 1000 characters
        limits = [
            MAX_PLUGIN_NAME_LENGTH,
            MAX_SERVICE_NAME_LENGTH,
            MAX_EVENT_NAME_LENGTH,
        ]
        for limit in limits:
            assert 10 <= limit <= 1000, f"Length limit {limit} should be between 10 and 1000"


class TestReservedNames:
    """Test reserved names constants."""

    def test_reserved_plugin_names_type(self) -> None:
        """Test RESERVED_PLUGIN_NAMES has correct type."""
        assert isinstance(RESERVED_PLUGIN_NAMES, tuple)

    def test_reserved_plugin_names_content(self) -> None:
        """Test RESERVED_PLUGIN_NAMES contains expected values."""
        expected_names = {
            "system",
            "kernel",
            "framework",
            "plugginger",
            "app",
            "core",
            "internal",
        }
        actual_names = set(RESERVED_PLUGIN_NAMES)
        assert actual_names == expected_names

    def test_reserved_plugin_names_are_strings(self) -> None:
        """Test that all reserved plugin names are strings."""
        for name in RESERVED_PLUGIN_NAMES:
            assert isinstance(name, str), f"Reserved name {name} must be a string"

    def test_reserved_plugin_names_are_non_empty(self) -> None:
        """Test that all reserved plugin names are non-empty."""
        for name in RESERVED_PLUGIN_NAMES:
            assert len(name) > 0, f"Reserved name {name} must be non-empty"

    def test_reserved_plugin_names_are_unique(self) -> None:
        """Test that all reserved plugin names are unique."""
        names_list = list(RESERVED_PLUGIN_NAMES)
        names_set = set(RESERVED_PLUGIN_NAMES)
        assert len(names_list) == len(names_set), "Reserved plugin names must be unique"

    def test_reserved_plugin_names_are_lowercase(self) -> None:
        """Test that all reserved plugin names are lowercase."""
        for name in RESERVED_PLUGIN_NAMES:
            assert name.islower(), f"Reserved name {name} should be lowercase"


class TestConstantImmutability:
    """Test that constants cannot be modified (as much as Python allows)."""

    def test_reserved_plugin_names_immutability(self) -> None:
        """Test that RESERVED_PLUGIN_NAMES tuple cannot be modified."""
        # Tuples are immutable, but we can test that it's actually a tuple
        assert isinstance(RESERVED_PLUGIN_NAMES, tuple)

        # Test that the tuple is immutable by verifying it's the same object
        # and that it doesn't have mutable methods
        original_id = id(RESERVED_PLUGIN_NAMES)
        assert id(RESERVED_PLUGIN_NAMES) == original_id

        # Verify it doesn't have mutable methods like append, extend, etc.
        assert not hasattr(RESERVED_PLUGIN_NAMES, "append")
        assert not hasattr(RESERVED_PLUGIN_NAMES, "extend")
        assert not hasattr(RESERVED_PLUGIN_NAMES, "remove")


class TestConstantConsistency:
    """Test consistency between related constants."""

    def test_event_name_length_is_largest(self) -> None:
        """Test that event name length limit is largest (events can be more descriptive)."""
        assert MAX_EVENT_NAME_LENGTH >= MAX_PLUGIN_NAME_LENGTH
        assert MAX_EVENT_NAME_LENGTH >= MAX_SERVICE_NAME_LENGTH

    def test_plugin_timeout_is_largest(self) -> None:
        """Test that plugin timeout is largest (plugins may need more setup time)."""
        assert DEFAULT_PLUGIN_TIMEOUT_SECONDS >= DEFAULT_EVENT_TIMEOUT_SECONDS
        assert DEFAULT_PLUGIN_TIMEOUT_SECONDS >= DEFAULT_SERVICE_TIMEOUT_SECONDS
