# tests/unit/test_core_config.py

"""
Unit tests for plugginger.core.config module.

Tests all configuration enums, constants, and type aliases to ensure they
have the correct values and types.
"""

from __future__ import annotations

from enum import Enum
from typing import get_args

from plugginger.core.config import (
    DEFAULT_APP_NAME,
    DEFAULT_EVENT_CLEANUP_INTERVAL,
    DEFAULT_EVENT_LISTENER_FAULT_POLICY,
    DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS,
    DEFAULT_EVENT_TASK_TIMEOUT,
    DEFAULT_LOG_LEVEL,
    DEFAULT_MAX_CONCURRENT_EVENTS,
    DEFAULT_MAX_FRACTAL_DEPTH,
    MAX_APP_NAME_LENGTH,
    MAX_EVENT_LISTENER_TIMEOUT,
    MAX_FRACTAL_DEPTH,
    MIN_APP_NAME_LENGTH,
    MIN_EVENT_LISTENER_TIMEOUT,
    MIN_FRACTAL_DEPTH,
    EventListenerFaultPolicy,
    LogLevel,
    LogLevelType,
)


class TestEventListenerFaultPolicy:
    """Test EventListenerFaultPolicy enum."""

    def test_inheritance(self) -> None:
        """Test that EventListenerFaultPolicy inherits from str and Enum."""
        assert issubclass(EventListenerFaultPolicy, str)
        assert issubclass(EventListenerFaultPolicy, Enum)

    def test_enum_values(self) -> None:
        """Test that enum has expected values."""
        assert EventListenerFaultPolicy.LOG_AND_CONTINUE.value == "log"
        assert EventListenerFaultPolicy.FAIL_FAST.value == "fail"
        assert EventListenerFaultPolicy.ISOLATE_AND_LOG.value == "isolate"

    def test_enum_members_count(self) -> None:
        """Test that enum has exactly 3 members."""
        assert len(EventListenerFaultPolicy) == 3

    def test_enum_members_exist(self) -> None:
        """Test that all expected enum members exist."""
        expected_members = {"LOG_AND_CONTINUE", "FAIL_FAST", "ISOLATE_AND_LOG"}
        actual_members = {member.name for member in EventListenerFaultPolicy}
        assert actual_members == expected_members

    def test_enum_values_are_strings(self) -> None:
        """Test that all enum values are strings."""
        for member in EventListenerFaultPolicy:
            assert isinstance(member.value, str)
            assert len(member.value) > 0

    def test_enum_values_are_unique(self) -> None:
        """Test that all enum values are unique."""
        values = [member.value for member in EventListenerFaultPolicy]
        assert len(values) == len(set(values))

    def test_string_behavior(self) -> None:
        """Test that enum members behave like strings."""
        policy: EventListenerFaultPolicy = EventListenerFaultPolicy.LOG_AND_CONTINUE
        # Enum values can be compared to their string values
        assert policy == "log"
        assert "log" == policy
        # Since these are str enums, they inherit string behavior
        # Use the enum value for string operations (ignore mypy issue with Never type)
        policy_value = policy.value  # type: ignore[attr-defined]
        assert policy_value == "log"
        assert policy_value.upper() == "LOG"
        assert policy_value.startswith("l")


class TestLogLevel:
    """Test LogLevel enum."""

    def test_inheritance(self) -> None:
        """Test that LogLevel inherits from str and Enum."""
        assert issubclass(LogLevel, str)
        assert issubclass(LogLevel, Enum)

    def test_enum_values(self) -> None:
        """Test that enum has expected values."""
        assert LogLevel.DEBUG.value == "DEBUG"
        assert LogLevel.INFO.value == "INFO"
        assert LogLevel.WARNING.value == "WARNING"
        assert LogLevel.ERROR.value == "ERROR"
        assert LogLevel.CRITICAL.value == "CRITICAL"

    def test_enum_members_count(self) -> None:
        """Test that enum has exactly 5 members."""
        assert len(LogLevel) == 5

    def test_enum_members_exist(self) -> None:
        """Test that all expected enum members exist."""
        expected_members = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        actual_members = {member.name for member in LogLevel}
        assert actual_members == expected_members

    def test_enum_values_are_strings(self) -> None:
        """Test that all enum values are strings."""
        for member in LogLevel:
            assert isinstance(member.value, str)
            assert len(member.value) > 0

    def test_enum_values_are_uppercase(self) -> None:
        """Test that all enum values are uppercase."""
        for member in LogLevel:
            assert member.value.isupper()

    def test_enum_values_are_unique(self) -> None:
        """Test that all enum values are unique."""
        values = [member.value for member in LogLevel]
        assert len(values) == len(set(values))

    def test_string_behavior(self) -> None:
        """Test that enum members behave like strings."""
        level: LogLevel = LogLevel.INFO
        # Enum values can be compared to their string values
        assert level == "INFO"
        assert "INFO" == level
        # Since these are str enums, they inherit string behavior
        # Use the enum value for string operations (ignore mypy issue with Never type)
        level_value = level.value  # type: ignore[attr-defined]
        assert level_value == "INFO"
        assert level_value.lower() == "info"
        assert level_value.endswith("O")

    def test_log_level_ordering(self) -> None:
        """Test that log levels follow expected severity ordering."""
        # This is more of a semantic test - the enum doesn't enforce ordering
        # but we can test that the expected levels exist in the right order
        levels = list(LogLevel)
        level_names = [level.name for level in levels]

        # Check that all expected levels are present
        expected_order = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        for expected_level in expected_order:
            assert expected_level in level_names


class TestLogLevelType:
    """Test LogLevelType type alias."""

    def test_log_level_type_alias(self) -> None:
        """Test that LogLevelType is a proper Literal type."""
        # Get the args from the Literal type
        args = get_args(LogLevelType)

        # Should contain all log level strings
        expected_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        actual_levels = set(args)
        assert actual_levels == expected_levels

    def test_log_level_type_consistency_with_enum(self) -> None:
        """Test that LogLevelType is consistent with LogLevel enum."""
        literal_args = set(get_args(LogLevelType))
        enum_values = {member.value for member in LogLevel}
        assert literal_args == enum_values


class TestDefaultValues:
    """Test default configuration values."""

    def test_default_log_level(self) -> None:
        """Test DEFAULT_LOG_LEVEL value and type."""
        assert isinstance(DEFAULT_LOG_LEVEL, LogLevel)
        assert DEFAULT_LOG_LEVEL == LogLevel.INFO

    def test_default_event_listener_timeout_seconds(self) -> None:
        """Test DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS value and type."""
        assert isinstance(DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS, float)
        assert DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS == 5.0

    def test_default_event_listener_fault_policy(self) -> None:
        """Test DEFAULT_EVENT_LISTENER_FAULT_POLICY value and type."""
        assert isinstance(DEFAULT_EVENT_LISTENER_FAULT_POLICY, EventListenerFaultPolicy)
        assert DEFAULT_EVENT_LISTENER_FAULT_POLICY == EventListenerFaultPolicy.FAIL_FAST

    def test_default_max_fractal_depth(self) -> None:
        """Test DEFAULT_MAX_FRACTAL_DEPTH value and type."""
        assert isinstance(DEFAULT_MAX_FRACTAL_DEPTH, int)
        assert DEFAULT_MAX_FRACTAL_DEPTH == 10

    def test_default_app_name(self) -> None:
        """Test DEFAULT_APP_NAME value and type."""
        assert isinstance(DEFAULT_APP_NAME, str)
        assert DEFAULT_APP_NAME == "PluggingerApp"

    def test_default_max_concurrent_events(self) -> None:
        """Test DEFAULT_MAX_CONCURRENT_EVENTS value and type."""
        assert isinstance(DEFAULT_MAX_CONCURRENT_EVENTS, int)
        assert DEFAULT_MAX_CONCURRENT_EVENTS == 100

    def test_default_event_cleanup_interval(self) -> None:
        """Test DEFAULT_EVENT_CLEANUP_INTERVAL value and type."""
        assert isinstance(DEFAULT_EVENT_CLEANUP_INTERVAL, float)
        assert DEFAULT_EVENT_CLEANUP_INTERVAL == 1.0

    def test_default_event_task_timeout(self) -> None:
        """Test DEFAULT_EVENT_TASK_TIMEOUT value and type."""
        assert isinstance(DEFAULT_EVENT_TASK_TIMEOUT, float)
        assert DEFAULT_EVENT_TASK_TIMEOUT == 30.0


class TestValidationConstraints:
    """Test validation constraint constants."""

    def test_event_listener_timeout_constraints(self) -> None:
        """Test event listener timeout constraint values and types."""
        assert isinstance(MIN_EVENT_LISTENER_TIMEOUT, float)
        assert isinstance(MAX_EVENT_LISTENER_TIMEOUT, float)
        assert MIN_EVENT_LISTENER_TIMEOUT == 0.1
        assert MAX_EVENT_LISTENER_TIMEOUT == 300.0
        assert MIN_EVENT_LISTENER_TIMEOUT < MAX_EVENT_LISTENER_TIMEOUT

    def test_fractal_depth_constraints(self) -> None:
        """Test fractal depth constraint values and types."""
        assert isinstance(MIN_FRACTAL_DEPTH, int)
        assert isinstance(MAX_FRACTAL_DEPTH, int)
        assert MIN_FRACTAL_DEPTH == 1
        assert MAX_FRACTAL_DEPTH == 100
        assert MIN_FRACTAL_DEPTH < MAX_FRACTAL_DEPTH

    def test_app_name_length_constraints(self) -> None:
        """Test app name length constraint values and types."""
        assert isinstance(MIN_APP_NAME_LENGTH, int)
        assert isinstance(MAX_APP_NAME_LENGTH, int)
        assert MIN_APP_NAME_LENGTH == 1
        assert MAX_APP_NAME_LENGTH == 100
        assert MIN_APP_NAME_LENGTH < MAX_APP_NAME_LENGTH

    def test_constraints_are_positive(self) -> None:
        """Test that all constraint values are positive."""
        constraints = [
            MIN_EVENT_LISTENER_TIMEOUT,
            MAX_EVENT_LISTENER_TIMEOUT,
            MIN_FRACTAL_DEPTH,
            MAX_FRACTAL_DEPTH,
            MIN_APP_NAME_LENGTH,
            MAX_APP_NAME_LENGTH,
        ]
        for constraint in constraints:
            assert constraint > 0, f"Constraint {constraint} should be positive"

    def test_default_values_within_constraints(self) -> None:
        """Test that default values are within their respective constraints."""
        # Event listener timeout
        assert MIN_EVENT_LISTENER_TIMEOUT <= DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS <= MAX_EVENT_LISTENER_TIMEOUT

        # Fractal depth
        assert MIN_FRACTAL_DEPTH <= DEFAULT_MAX_FRACTAL_DEPTH <= MAX_FRACTAL_DEPTH

        # App name length
        assert MIN_APP_NAME_LENGTH <= len(DEFAULT_APP_NAME) <= MAX_APP_NAME_LENGTH


class TestConfigurationConsistency:
    """Test consistency between related configuration values."""

    def test_timeout_values_are_reasonable(self) -> None:
        """Test that timeout values are reasonable relative to each other."""
        # Event listener timeout should be less than event task timeout
        assert DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS <= DEFAULT_EVENT_TASK_TIMEOUT

        # Cleanup interval should be much smaller than timeouts
        assert DEFAULT_EVENT_CLEANUP_INTERVAL < DEFAULT_EVENT_LISTENER_TIMEOUT_SECONDS
        assert DEFAULT_EVENT_CLEANUP_INTERVAL < DEFAULT_EVENT_TASK_TIMEOUT

    def test_concurrent_events_is_reasonable(self) -> None:
        """Test that max concurrent events is a reasonable value."""
        assert 1 <= DEFAULT_MAX_CONCURRENT_EVENTS <= 10000

    def test_default_app_name_is_valid(self) -> None:
        """Test that default app name meets its own constraints."""
        assert len(DEFAULT_APP_NAME) >= MIN_APP_NAME_LENGTH
        assert len(DEFAULT_APP_NAME) <= MAX_APP_NAME_LENGTH
        assert DEFAULT_APP_NAME.strip() == DEFAULT_APP_NAME  # No leading/trailing whitespace
        assert len(DEFAULT_APP_NAME.strip()) > 0  # Not empty after stripping


class TestEnumStringCompatibility:
    """Test that enums work properly as strings in various contexts."""

    def test_fault_policy_in_dict_keys(self) -> None:
        """Test that fault policy enum can be used as dict keys."""
        policy_dict = {
            EventListenerFaultPolicy.LOG_AND_CONTINUE: "continue",
            EventListenerFaultPolicy.FAIL_FAST: "fail",
            EventListenerFaultPolicy.ISOLATE_AND_LOG: "isolate",
        }

        assert len(policy_dict) == 3
        assert policy_dict[EventListenerFaultPolicy.LOG_AND_CONTINUE] == "continue"

    def test_log_level_in_dict_keys(self) -> None:
        """Test that log level enum can be used as dict keys."""
        level_dict = {
            LogLevel.DEBUG: 10,
            LogLevel.INFO: 20,
            LogLevel.WARNING: 30,
            LogLevel.ERROR: 40,
            LogLevel.CRITICAL: 50,
        }

        assert len(level_dict) == 5
        assert level_dict[LogLevel.INFO] == 20

    def test_enum_string_operations(self) -> None:
        """Test that enums support string operations."""
        policy: EventListenerFaultPolicy = EventListenerFaultPolicy.LOG_AND_CONTINUE
        level: LogLevel = LogLevel.INFO

        # Test that enums can be used in string contexts
        # Use the enum values for string operations
        policy_value = policy.value
        level_value = level.value
        assert policy_value == "log"
        assert level_value == "INFO"
        assert policy_value.upper() == "LOG"
        assert level_value.lower() == "info"
        assert policy_value.startswith("l")
        assert level_value.endswith("O")

        # Test string formatting - note that f-strings use the enum representation
        # but we can test that the enum values work correctly in string contexts
        assert policy == "log"
        assert level == "INFO"
