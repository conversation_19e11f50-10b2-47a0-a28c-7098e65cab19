"""
Tests for the experimental namespace.

These tests verify that the experimental namespace is properly set up
and issues appropriate warnings when used.
"""

import warnings

import pytest

import plugginger.experimental as experimental


class TestExperimentalNamespace:
    """Test the experimental namespace setup."""

    def test_experimental_import_issues_warning(self) -> None:
        """Test that importing experimental module issues a warning."""
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            # Re-import to trigger warning
            import importlib
            importlib.reload(experimental)

            # Check that a warning was issued
            assert len(w) >= 1
            warning = w[-1]  # Get the last warning
            assert issubclass(warning.category, FutureWarning)
            assert "experimental" in str(warning.message).lower()
            assert "unstable" in str(warning.message).lower()

    def test_get_experimental_features(self) -> None:
        """Test getting list of experimental features."""
        features = experimental.get_experimental_features()

        # Should return a list
        assert isinstance(features, list)

        # Should contain expected experimental features
        expected_features = [
            "AdvancedFractalComposer",
            "FractalMetrics",
            "EventSourcingMixin",
            "PluginRegistry",
        ]

        for feature in expected_features:
            assert feature in features

    def test_is_experimental_feature(self) -> None:
        """Test checking if a feature is experimental."""
        # Known experimental features should return True
        assert experimental.is_experimental_feature("AdvancedFractalComposer") is True
        assert experimental.is_experimental_feature("EventSourcingMixin") is True
        assert experimental.is_experimental_feature("PluginRegistry") is True

        # Non-experimental features should return False
        assert experimental.is_experimental_feature("NonExistentFeature") is False
        assert experimental.is_experimental_feature("PluggingerAppBuilder") is False

    def test_lazy_import_experimental_feature(self) -> None:
        """Test lazy importing of experimental features."""
        # This should work without error and issue a warning
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            # Import an experimental feature
            fractal_composer = experimental.AdvancedFractalComposer

            # Should be a class
            assert isinstance(fractal_composer, type)
            assert fractal_composer.__name__ == "AdvancedFractalComposer"

            # Should have issued warnings (from both experimental import and fractal import)
            assert len(w) >= 1

    def test_experimental_feature_not_found(self) -> None:
        """Test that accessing non-existent experimental feature raises AttributeError."""
        with pytest.raises(AttributeError) as exc_info:
            _ = experimental.NonExistentFeature

        assert "has no attribute 'NonExistentFeature'" in str(exc_info.value)

    def test_experimental_all_exports(self) -> None:
        """Test that __all__ contains all experimental features."""
        all_exports = experimental.__all__
        features = experimental.get_experimental_features()

        # __all__ should match the features list
        assert set(all_exports) == set(features)

        # Should contain expected number of features
        assert len(all_exports) > 0


class TestExperimentalFractal:
    """Test experimental fractal features."""

    def test_advanced_fractal_composer_import(self) -> None:
        """Test importing AdvancedFractalComposer."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            composer = experimental.AdvancedFractalComposer()

            # Should have default depth limit
            assert composer.get_fractal_depth_limit() == 10

            # Should be able to set depth limit
            composer.set_fractal_depth_limit(5)
            assert composer.get_fractal_depth_limit() == 5

    def test_fractal_metrics_import(self) -> None:
        """Test importing FractalMetrics."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            metrics = experimental.FractalMetrics()

            # Should start with no metrics
            assert metrics.get_max_fractal_depth() == 0
            assert metrics.get_fractal_count() == 0

            # Should be able to record metrics
            metrics.record_fractal_depth("test_app", 3)
            assert metrics.get_max_fractal_depth() == 3
            assert metrics.get_fractal_count() == 1


class TestExperimentalEvents:
    """Test experimental event features."""

    def test_event_sourcing_mixin_import(self) -> None:
        """Test importing EventSourcingMixin."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            mixin = experimental.EventSourcingMixin()

            # Should be able to record events
            event_id = mixin.record_event("test_event", {"data": "test"})
            assert isinstance(event_id, str)
            assert len(event_id) > 0

            # Should be able to replay events
            events = mixin.replay_events()
            assert len(events) == 1
            assert events[0].event_type == "test_event"

    def test_advanced_event_filter_import(self) -> None:
        """Test importing AdvancedEventFilter."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            filter_obj = experimental.AdvancedEventFilter()

            # Should process all events by default
            assert filter_obj.should_process_event({"test": "data"}) is True

            # Should be able to add filters
            filter_obj.add_filter(lambda event: event.get("test") == "data")
            assert filter_obj.should_process_event({"test": "data"}) is True
            assert filter_obj.should_process_event({"test": "other"}) is False


class TestExperimentalRegistry:
    """Test experimental registry features."""

    def test_plugin_registry_import(self) -> None:
        """Test importing PluginRegistry."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            registry = experimental.PluginRegistry()

            # Should have default registry URL
            assert hasattr(registry, '_registry_url')

            # Should start with no installed plugins
            installed = registry.list_installed_plugins()
            assert isinstance(installed, dict)
            assert len(installed) == 0

    def test_plugin_discovery_import(self) -> None:
        """Test importing PluginDiscovery."""
        with warnings.catch_warnings(record=True):
            warnings.simplefilter("always")

            discovery = experimental.PluginDiscovery()

            # Should be able to add discovery sources
            discovery.add_discovery_source("https://example.com")
            assert "https://example.com" in discovery._discovery_sources

            # Should be able to remove discovery sources
            discovery.remove_discovery_source("https://example.com")
            assert "https://example.com" not in discovery._discovery_sources
