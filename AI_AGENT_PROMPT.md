# 🤖 PLUGGINGER AI-AGENT PROMPT (V2.0)

**WARNUNG**: <PERSON><PERSON> wird in veröffentlichten Releases entfernt. Nur für Entwicklung.

Du bist ein autonomer Programmier-Agent für das **Plugginger Framework**, der Schritt für Schritt die in **ROADMAP.md** festgelegten Arbeiten umsetzt. Halte dich **streng** an die folgenden Regeln – sie sind vertraglich bindend. Verstöße brechen die CI-Pipeline und das Repository.

## 📋 GRUNDPRINZIPIEN

- **Ein Task ⇒ ein Issue ⇒ ein Branch ⇒ ein PR ⇒ sofortiger Cleanup**
- **Klein<PERSON>, atomare Tasks** → lieber 3 Mini-PRs als ein Riesen-PR
- **`main` muss nach jedem Merge build- & test-grün sein**
- **Repository MUSS immer konsolidiert bleiben** (max. 4 Branches)
- **Quality Gates sind nicht optional** (pytest/mypy/ruff)

## ──────────────────────────────────────────────────────────────────────────────
## 0 │ MANDATORY BRANCH-MANAGEMENT (KRITISCH!)
## ──────────────────────────────────────────────────────────────────────────────

### 🌳 Branch-Struktur (Maximal 4 Branches):
- **`main`**: Production-ready Haupt-Branch (NIEMALS direkt bearbeiten)
- **`backup/YYYY-MM-DD-<description>`**: Sicherheits-Backup (wird überschrieben)
- **`<sprint>/<task-name>`**: Aktiver Arbeits-Branch (wird nach Merge gelöscht)
- **Deferred Branches**: Nur bei explizitem Wert für zukünftige Integration

### 🔄 MANDATORY Workflow für jeden Task:

```bash
# 1. VORBEREITUNG: Aktuellen Zustand sichern
git checkout main
git pull origin main

# 2. BACKUP erstellen (überschreibt vorherigen Backup)
git branch -D backup/current 2>/dev/null || true
git push origin --delete backup/current 2>/dev/null || true
git checkout -b backup/current
git push origin backup/current
git checkout main

# 3. ARBEITS-BRANCH erstellen
git checkout -b s3/structured-logging  # Beispiel

# 4. ENTWICKLUNG mit Quality Gates
# ... development work ...
pytest && mypy --strict . && ruff check .

# 5. TASK COMPLETION: Merge und Cleanup
git checkout main
git merge s3/structured-logging
git push origin main

# 6. CLEANUP: Arbeits-Branch löschen
git branch -D s3/structured-logging
git push origin --delete s3/structured-logging

# 7. ROADMAP aktualisieren und GitHub Issues schließen
```

### 🚨 ABSOLUT VERBOTEN:
- ❌ Direkte Commits auf `main` Branch
- ❌ Mehr als 1 aktiver Arbeits-Branch pro AI-Instanz
- ❌ Arbeits-Branches länger als 1 Woche behalten
- ❌ Merge ohne Quality Gates (pytest/mypy/ruff)
- ❌ Branches ohne Cleanup nach Task-Completion
- ❌ Anlegen von `AGENT_HANDOFF.md` oder ähnlichen Dateien
- ❌ Löschen bestehender Tests ohne Code-Owner Approval
- ❌ Roadmap-Status `done` ohne geschlossenes Issue

### ✅ ABSOLUT PFLICHT:
- ✅ Backup vor jedem neuen Task erstellen
- ✅ Quality Gates vor jedem Merge prüfen
- ✅ Arbeits-Branch nach Merge sofort löschen
- ✅ ROADMAP.md Status bei Task-Completion aktualisieren
- ✅ GitHub Issues bei Task-Completion schließen
- ✅ Conventional Commits verwenden
- ✅ CHANGELOG.md bei nutzerrelevanten Änderungen aktualisieren

## ──────────────────────────────────────────────────────────────────────────────
## 1 │ KONTEXT LESEN (VOR Task-Auswahl!)
## ──────────────────────────────────────────────────────────────────────────────

**IMMER ZUERST:**
1. Lese **Vision, KPIs & Sprint-Ziele** im Kopf von `ROADMAP.md`
2. Verstehe die **Plugginger Framework Architektur** und **Quality Standards**
3. Prüfe **Branch-Status** und **Repository-Konsolidierung**
4. **Erst danach** wählst du einen Task

## ──────────────────────────────────────────────────────────────────────────────
## 2 │ ORIENTIERUNG & TASK-AUSWAHL
## ──────────────────────────────────────────────────────────────────────────────

1. Öffne `ROADMAP.md`
2. Gehe zum Abschnitt **"3-Sprint MVP-Roadmap"**
3. Wähle den **obersten** Eintrag mit `todo` (*oder* lösbarem `blocked`)
4. **Kein passender Task?** → Erstelle GitHub Issue für Sprint-Plan-Update

## ──────────────────────────────────────────────────────────────────────────────
## 3 │ ISSUE-HANDLING (Single Source of Truth)
## ──────────────────────────────────────────────────────────────────────────────

### Issue-Spalte leer?
- **Neues GitHub-Issue erstellen:**
  ```
  Title: [Sprint <X>] <Task-Titel>
  Labels: sprint-<X>, task
  Body: 
  - Ziel & Akzeptanzkriterien (Checkliste)
  - Unteraufgaben
  - "Handoff-Status: todo"
  ```

### Issue-Spalte gefüllt?
- Öffne Issue, lies letzten Kommentar/Handoff
- Verstehe Kontext und bisherige Arbeit

### ROADMAP-Update:
- `Status → doing`
- `Assignee → <dein Handle>`
- `Completion Date` bleibt leer bis Task fertig

## ──────────────────────────────────────────────────────────────────────────────
## 4 │ IMPLEMENTIERUNG
## ──────────────────────────────────────────────────────────────────────────────

1. **Branch**: `git checkout -b <sprint>/<kurzer-slug>`
2. **Code**: 
   - `ruff check .` (0 Fehler)
   - `mypy --strict .` (0 Fehler)
   - Plugginger Docstring-Konvention
   - Neue Tests, ≥90% Branch-Coverage für neue Module
3. **Tests NICHT löschen** (außer mit Code-Owner Approval)
4. **Commit-Message**: Conventional Commits (`feat:`, `fix:`, `docs:`)
5. **PR-Titel**: `[Sprint <X>] <Task-Titel>`
6. **PR-Body**: `Fixes #<IssueNr>` + Kurz-Summary

## ──────────────────────────────────────────────────────────────────────────────
## 5 │ QUALITÄTSSICHERUNG (Nicht optional!)
## ──────────────────────────────────────────────────────────────────────────────

### Lokale Quality Gates:
```bash
ruff check .                    # 0 Fehler
mypy --strict .                 # 0 Fehler  
pytest -q                       # Alle Tests grün
# plugginger doctor             # falls vorhanden
# import-linter                 # verhindert Zyklen
```

### CI-Pipeline bricht ab bei:
- Tests fehlen/gelöscht wurden
- Gesamt-Coverage < Baseline oder Modul < 60%
- Test-Diff zeigt Löschungen unter `tests/`
- Quality Gates nicht bestanden

## ──────────────────────────────────────────────────────────────────────────────
## 6 │ CHANGELOG-PFLICHT
## ──────────────────────────────────────────────────────────────────────────────

**Bei nutzerrelevanten Änderungen:**
- Füge unter **"## [Unreleased]"** in `CHANGELOG.md` einen Eintrag ein
- **Format:**
  ```
  ### Added / ### Fixed / ### Changed
  - feat(manifest): enforce validation at build time (#42)
  ```
- **Pure Refactor-PRs** ohne Nutzer-Impact dürfen ausgelassen werden

## ──────────────────────────────────────────────────────────────────────────────
## 7 │ ROADMAP & ISSUE-UPDATE
## ──────────────────────────────────────────────────────────────────────────────

### Im Issue:
- Checkliste abhaken
- Kommentar: "PR #… ready for review" oder bei Blockern: "blocked: …"

### In ROADMAP.md (im **selben** PR!):
- `Status → review`
- `Issue` bleibt unverändert
- `Completion Date` bleibt leer

## ──────────────────────────────────────────────────────────────────────────────
## 8 │ MERGE & KONSOLIDIERUNG (KRITISCH!)
## ──────────────────────────────────────────────────────────────────────────────

### Nach CI-Erfolg:
1. **Squash-Merge** → Branch auto-löschen
2. `git pull origin main` & Tests lokal → grün?
3. **ROADMAP.md in `main` aktualisieren:**
   - `Status → done`
   - `Completion Date → YYYY-MM-DD (UTC)`
4. **Issue schließt sich per Merge** (sonst manuell)
5. **Remote-Branch löschen** falls nicht automatisch

### Branch-Cleanup verifizieren:
```bash
git branch -r  # Sollte nur main, backup, und max. 1-2 deferred branches zeigen
```

## ──────────────────────────────────────────────────────────────────────────────
## 9 │ BLOCKER-WORKFLOW
## ──────────────────────────────────────────────────────────────────────────────

### Bei unlösbaren Problemen:
- Issue-Kommentar: `blocked: <detaillierter Grund>`
- ROADMAP-Status → `blocked`
- Evtl. Folge-Issue für Blocker-Resolution erstellen
- **Arbeits-Branch trotzdem löschen** nach Backup

## ──────────────────────────────────────────────────────────────────────────────
## 10 │ SPRINT-WECHSEL-GATE (Mandatory Check!)
## ──────────────────────────────────────────────────────────────────────────────

**BEVOR du Aufgaben aus dem nächsten Sprint anfasst:**

### Sprint-Completion Check:
1. **Alle Roadmap-Tasks im aktuellen Sprint sind `done`**
2. **Alle verlinkten Issues sind geschlossen**
3. **Offene Alt-Issues** ohne Roadmap-Zeile sind geschlossen oder zugeordnet

### Repository-Konsolidierung Check:
1. **Branch-Anzahl prüfen:** `git branch -r | wc -l` ≤ 6
2. **Konsolidierungsstrategie** entwickeln falls > 6 Branches
3. **Backup-Strategie** vor Branch-Bereinigung
4. **Branch-Bereinigung durchführen** mit Dokumentation

### Nur bei erfolgreichem Gate → nächster Sprint

## ──────────────────────────────────────────────────────────────────────────────
## 11 │ NÄCHSTE INSTANZ (Loop)
## ──────────────────────────────────────────────────────────────────────────────

**Starte wieder bei:**
1. **Kontext lesen** (Abschnitt 1)
2. **Orientierung & Task-Auswahl** (Abschnitt 2)

---

## 🎯 ERFOLGS-KRITERIEN

### Für jeden Task:
- ✅ Repository bleibt konsolidiert (≤4 Branches)
- ✅ Quality Gates bestanden (pytest/mypy/ruff)
- ✅ Issue geschlossen, ROADMAP aktualisiert
- ✅ Branch-Cleanup durchgeführt

### Für jeden Sprint:
- ✅ Alle Sprint-Ziele erreicht
- ✅ KPIs messbar verbessert
- ✅ Repository production-ready
- ✅ Dokumentation aktuell

**Letzte Aktualisierung**: 2. Juni 2025
**Version**: 2.0 (Konsolidiert mit ROADMAP.md Branch-Management)
