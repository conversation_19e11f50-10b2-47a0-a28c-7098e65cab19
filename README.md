# Plugginger

> **Build complex, AI-ready apps out of bite-sized modules – without drowning in
> framework details.**

---

## Why Should *I* Care?

| ❤️ Benefit | What it means for **you** |
|------------|---------------------------|
| **Plug-and-Play Functionality** | Drop in ready-made “Plugs” (e.g. auth, vector search, OpenAI chat) instead of wiring services by hand. |
| **Fractal Composition** | Treat an entire sub-app as just *another* plug – scale your product the Lego® way. |
| **Async by Default** | Concurrency without the callback hell; ideal for real-time APIs, chatbots, and data pipelines. |
| **AI-Friendly Design** | Each plug is fully described by a tiny manifest → LLM agents can generate or update features autonomously. |
| **Fault Isolation** | Misbehaving plugs can be sandboxed or restarted without killing the whole app. |

---

## Typical Use-Cases

* **AI Chat Service** combining  
  *an OpenAI-Chat plug* + *a DuckDB vector-store plug* + *a FastAPI gateway plug*  
  – assembled in minutes, not days.
* **Event-Driven Data Pipelines** where each stage (ingest, transform, export) is an independent plug.
* **Feature Labs / Hack-Weeks**: spin up experimental modules without touching the core product.

---

## Quick Start (5 Minutes)

> *You don’t need to understand Plugginger internals – follow the steps and run the demo app.*

1. **Prerequisites**  
   * Python 3.10+  
   * `pipx` (optional but recommended)

2. **Install the latest alpha**

   ```bash
   pipx install plugginger                # or: pip install plugginger
   ```

3. **Bootstrap the demo project**

   ```bash
   plugginger new app my_ai_chat
   cd my_ai_chat
   ```

   This generates:

   ```
   my_ai_chat/
   ├── app.py              # main entry
   └── plugs/              # chat_ai, memory_store, web_api
   ```

4. **Run it**

   ```bash
   python app.py
   # → FastAPI server on http://127.0.0.1:8000
   ```

5. **Talk to your bot**

   ```bash
   curl -X POST http://127.0.0.1:8000/chat \
        -H "Content-Type: application/json" \
        -d '{"message": "Hello, Plugginger!"}'
   ```

---

## Next Steps for Interested Users

| Step              | What to do                                                                       | Docs                                                   |
| ----------------- | -------------------------------------------------------------------------------- | ------------------------------------------------------ |
| **Explore Plugs** | `ls plugs/` – browse the code, each plug is ≤ 150 LoC.                           | [Plugs 101](docs/getting-started/your-first-plugin.md) |
| **Add a Feature** | Copy an existing plug folder, change the manifest, add a service, restart.       | [Add a Service](docs/guides/plugin-patterns.md)        |
| **Scale Out**     | Nest this app inside a *bigger* Plugginger project as a single line in `app.py`. | [Fractal Apps](docs/guides/fractal-composition.md)     |
| **Stay Updated**  | `pip install --upgrade plugginger` – new releases every \~2 weeks.               | [Changelog](CHANGELOG.md)                              |

---

## API Stability (v0.9.0-alpha)

Plugginger follows a clear API stability policy to help you build with confidence:

| API Level | Stability | Breaking Changes | Use Case |
|-----------|-----------|------------------|----------|
| **✅ Stable Candidates** | High | Only with major version bump | Production apps |
| **🧪 Experimental** | None | May change without notice | Prototyping, testing |
| **🔧 Internal** | None | Implementation details | Framework development |

### Stable Candidate APIs (Safe for Production)

- **`plugginger.api.plugin`** - Plugin creation (`@plugin`, `PluginBase`)
- **`plugginger.api.service`** - Service decoration (`@service`)
- **`plugginger.api.events`** - Event listeners (`@on_event`)
- **`plugginger.api.builder`** - Application building
- **`plugginger.api.app`** - Runtime interface

### Experimental APIs (Use with Caution)

- **`plugginger.experimental.*`** - Advanced features that may change

**📖 Full Policy**: [Breaking Change Policy](docs/BREAKING_CHANGE_POLICY.md)

---

## Community & Support

* **Issues / Ideas** – [https://github.com/plugginger/plugginger/issues](https://github.com/plugginger/plugginger/issues)
* **Discord** – *#plugginger* channel on the AI-Dev Guild
* **Twitter / X** – [@plugginger](https://twitter.com/plugginger)

*Early alpha: bugs expected – we love bug reports and feedback!*

---

## License

MIT — use it, fork it, ship it 🚀


