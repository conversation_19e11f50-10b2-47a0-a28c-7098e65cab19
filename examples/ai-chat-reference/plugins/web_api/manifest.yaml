manifest_version: "1.0.0"
metadata:
  name: "web_api"
  version: "1.0.0"
  description: "FastAPI HTTP Endpoints for Web Interface"
  author: "Plugginger AI-Agent"
  license: "MIT"
  tags: ["web", "api", "fastapi", "http", "rest"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
  python_version: ">=3.11"

services:
  - name: "start_server"
    description: "Start the FastAPI server"
    parameters:
      - name: "host"
        type: "str"
        required: false
        default: "0.0.0.0"
        description: "Host to bind the server to"
      - name: "port"
        type: "int"
        required: false
        default: 8000
        description: "Port to bind the server to"
    returns:
      type: "None"

  - name: "stop_server"
    description: "Stop the FastAPI server"
    returns:
      type: "None"

  - name: "get_health_status"
    description: "Get API health status for /health endpoint"
    returns:
      type: "dict[str, str]"
      description: "Health status information"

  - name: "get_server_info"
    description: "Get server information and statistics"
    returns:
      type: "dict[str, Any]"
      description: "Server information and metrics"

http_endpoints:
  - path: "/health"
    method: "GET"
    description: "Health check endpoint - returns 200 OK when healthy"
    response_model: "dict[str, str]"

  - path: "/chat"
    method: "POST"
    description: "Send message and get AI response"
    request_model: "ChatRequest"
    response_model: "ChatResponse"

  - path: "/conversations"
    method: "GET"
    description: "List all conversations with metadata"
    response_model: "list[dict[str, Any]]"

  - path: "/conversations/{conversation_id}/history"
    method: "GET"
    description: "Get conversation history with messages"
    parameters:
      - name: "conversation_id"
        type: "str"
        location: "path"
        description: "The conversation ID"
      - name: "limit"
        type: "int"
        location: "query"
        default: 50
        description: "Maximum number of messages"
    response_model: "list[dict[str, Any]]"

  - path: "/conversations/{conversation_id}"
    method: "GET"
    description: "Get conversation information"
    parameters:
      - name: "conversation_id"
        type: "str"
        location: "path"
        description: "The conversation ID"
    response_model: "dict[str, Any]"

  - path: "/conversations/{conversation_id}"
    method: "DELETE"
    description: "Delete a conversation"
    parameters:
      - name: "conversation_id"
        type: "str"
        location: "path"
        description: "The conversation ID"
    response_model: "dict[str, bool]"

  - path: "/models"
    method: "GET"
    description: "Get AI model information"
    response_model: "dict[str, Any]"

event_listeners:
  - event_pattern: "chat.response_generated"
    handler: "handle_chat_response"
    description: "Handle chat response events for logging and monitoring"
    priority: 1

events_emitted:
  - event_name: "api.request_received"
    description: "Emitted when API request is received"
    data_schema:
      endpoint: "str"
      conversation_id: "str"
      message: "str"
      timestamp: "float"

  - event_name: "api.response_sent"
    description: "Emitted when API response is sent"
    data_schema:
      endpoint: "str"
      conversation_id: "str"
      response_time: "float"
      timestamp: "float"

dependencies:
  - name: "chat_ai"
    version: null
    optional: false
    description: "Required for AI chat completion"
  - name: "memory_store"
    version: null
    optional: false
    description: "Required for conversation storage"

config_schema:
  type: "object"
  properties:
    server_host:
      type: "string"
      default: "0.0.0.0"
      description: "Default host for the web server"
    server_port:
      type: "integer"
      default: 8000
      description: "Default port for the web server"
    cors_origins:
      type: "array"
      items:
        type: "string"
      default: ["*"]
      description: "Allowed CORS origins"
    auto_start_server:
      type: "boolean"
      default: false
      description: "Automatically start server on plugin setup"
  additionalProperties: false

external_dependencies:
  - name: "fastapi"
    version: ">=0.100.0"
    description: "FastAPI web framework"
  - name: "uvicorn"
    version: ">=0.20.0"
    description: "ASGI server for FastAPI"
