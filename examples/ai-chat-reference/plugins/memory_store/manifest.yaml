manifest_version: "1.0.0"
metadata:
  name: "memory_store"
  version: "1.0.0"
  description: "In-Memory Storage for Chat History and Session Management"
  author: "Plugginger AI-Agent"
  license: "MIT"
  tags: ["storage", "memory", "chat", "session"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
  python_version: ">=3.11"

services:
  - name: "create_conversation"
    description: "Create a new conversation and return its ID"
    parameters:
      - name: "title"
        type: "str"
        required: false
        default: "New Conversation"
        description: "Optional title for the conversation"
    returns:
      type: "str"
      description: "The unique conversation ID"

  - name: "store_message"
    description: "Store a message in the conversation history"
    parameters:
      - name: "conversation_id"
        type: "str"
        required: true
        description: "The conversation to store the message in"
      - name: "role"
        type: "str"
        required: true
        description: "Message role (user, assistant, system)"
      - name: "content"
        type: "str"
        required: true
        description: "The message content"
      - name: "metadata"
        type: "dict[str, Any] | None"
        required: false
        description: "Optional metadata for the message"
    returns:
      type: "None"

  - name: "get_conversation_history"
    description: "Get conversation history with messages"
    parameters:
      - name: "conversation_id"
        type: "str"
        required: true
        description: "The conversation ID to retrieve"
      - name: "limit"
        type: "int"
        required: false
        default: 50
        description: "Maximum number of messages to return"
      - name: "include_metadata"
        type: "bool"
        required: false
        default: false
        description: "Whether to include message metadata"
    returns:
      type: "list[dict[str, Any]]"
      description: "List of messages in chronological order"

  - name: "list_conversations"
    description: "List all conversations with basic metadata"
    returns:
      type: "list[dict[str, Any]]"
      description: "List of conversation summaries"

  - name: "get_conversation_info"
    description: "Get detailed information about a specific conversation"
    parameters:
      - name: "conversation_id"
        type: "str"
        required: true
        description: "The conversation ID"
    returns:
      type: "dict[str, Any]"
      description: "Conversation metadata and statistics"

  - name: "delete_conversation"
    description: "Delete a conversation and all its messages"
    parameters:
      - name: "conversation_id"
        type: "str"
        required: true
        description: "The conversation ID to delete"
    returns:
      type: "bool"
      description: "True if deleted, False if not found"

  - name: "get_storage_stats"
    description: "Get storage statistics for monitoring"
    returns:
      type: "dict[str, Any]"
      description: "Storage statistics and metrics"

event_listeners:
  - event_pattern: "chat.response_generated"
    handler: "handle_chat_response"
    description: "Handle chat response events to automatically store AI responses"
    priority: 10

events_emitted:
  - event_name: "memory.conversation_created"
    description: "Emitted when a new conversation is created"
    data_schema:
      conversation_id: "str"
      title: "str"
      created_at: "str"

  - event_name: "memory.message_stored"
    description: "Emitted when a message is stored"
    data_schema:
      conversation_id: "str"
      message_id: "str"
      role: "str"
      content: "str"
      timestamp: "str"

  - event_name: "memory.conversation_deleted"
    description: "Emitted when a conversation is deleted"
    data_schema:
      conversation_id: "str"
      deleted_at: "str"

dependencies: []

config_schema:
  type: "object"
  properties:
    max_conversations:
      type: "integer"
      default: 1000
      description: "Maximum number of conversations to keep in memory"
    max_messages_per_conversation:
      type: "integer"
      default: 1000
      description: "Maximum number of messages per conversation"
  additionalProperties: false
