manifest_version: "1.0.0"
metadata:
  name: "chat_ai"
  version: "1.0.0"
  description: "OpenAI/Anthropic Integration for Chat Completion"
  author: "Plugginger AI-Agent"
  license: "MIT"
  tags: ["ai", "chat", "openai", "llm", "completion"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
  python_version: ">=3.11"

services:
  - name: "generate_response"
    description: "Generate AI response for user message"
    parameters:
      - name: "message"
        type: "str"
        required: true
        description: "The user message to respond to"
      - name: "conversation_id"
        type: "str"
        required: true
        description: "The conversation context"
      - name: "model"
        type: "str"
        required: false
        default: "gpt-3.5-turbo"
        description: "AI model to use for generation"
      - name: "temperature"
        type: "float"
        required: false
        default: 0.7
        description: "Response creativity (0.0-1.0)"
      - name: "max_tokens"
        type: "int"
        required: false
        default: 1000
        description: "Maximum response length"
    returns:
      type: "str"
      description: "The AI-generated response"

  - name: "get_model_info"
    description: "Get information about available models and current configuration"
    returns:
      type: "dict[str, Any]"
      description: "Model information and configuration"

  - name: "set_default_model"
    description: "Set the default model for chat generation"
    parameters:
      - name: "model"
        type: "str"
        required: true
        description: "The model name to set as default"
    returns:
      type: "bool"
      description: "True if model was set successfully"

event_listeners:
  - event_pattern: "memory.conversation_created"
    handler: "handle_new_conversation"
    description: "Handle new conversation events"
    priority: 5

events_emitted:
  - event_name: "chat.response_generated"
    description: "Emitted when AI response is generated"
    data_schema:
      conversation_id: "str"
      user_message: "str"
      response: "str"
      model: "str"
      tokens_used: "int"
      response_time: "float"
      timestamp: "float"

  - event_name: "chat.conversation_started"
    description: "Emitted when AI is ready for new conversation"
    data_schema:
      conversation_id: "str"
      ai_model: "str"
      timestamp: "float"

  - event_name: "chat.generation_error"
    description: "Emitted when AI response generation fails"
    data_schema:
      conversation_id: "str"
      error: "str"
      timestamp: "float"

dependencies:
  - name: "memory_store"
    version: null
    optional: false
    description: "Required for storing conversation history"

config_schema:
  type: "object"
  properties:
    default_model:
      type: "string"
      default: "gpt-3.5-turbo"
      description: "Default AI model to use"
    max_tokens:
      type: "integer"
      default: 1000
      description: "Default maximum tokens for responses"
    temperature:
      type: "number"
      default: 0.7
      minimum: 0.0
      maximum: 1.0
      description: "Default temperature for response generation"
    mock_mode:
      type: "boolean"
      default: false
      description: "Force mock mode even if API key is available"
  additionalProperties: false
