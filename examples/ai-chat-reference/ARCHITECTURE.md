# AI-Chat Reference App - Architecture Design

## 🎯 Ziel

Demonstriere Plugginger Framework Production-Readiness durch eine vollständige "AI-Chat mit Memory" Anwendung.

**Sprint 1 KPI**: Fremder Entwickler baut Reference-App in ≤10 Minuten nach

## 🏗️ Plugin-Architektur

### 1. chat_ai Plugin
**Zweck**: OpenAI/Anthropic Integration für Chat-Completion  
**Services**:
- `generate_response(message: str, conversation_id: str) -> str`
- `get_model_info() -> dict[str, Any]`

**Events**:
- Emits: `chat.response_generated`
- Listens: `chat.conversation_started`

**Dependencies**: `memory_store` (für Conversation History)

### 2. memory_store Plugin  
**Zweck**: In-Memory Storage für Chat-History und Session-Management  
**Services**:
- `store_message(conversation_id: str, role: str, content: str) -> None`
- `get_conversation_history(conversation_id: str) -> list[dict[str, Any]]`
- `create_conversation() -> str`
- `list_conversations() -> list[str]`

**Events**:
- Emits: `memory.message_stored`, `memory.conversation_created`
- Listens: `chat.response_generated`

**Dependencies**: None (base storage layer)

### 3. web_api Plugin
**Zweck**: FastAPI HTTP-Endpoints für Web-Interface  
**Services**:
- `start_server() -> None`
- `stop_server() -> None`
- `get_health_status() -> dict[str, str]`

**HTTP Endpoints**:
- `POST /chat` - Send message, get AI response
- `GET /conversations` - List all conversations  
- `GET /conversations/{id}/history` - Get conversation history
- `GET /health` - Health check (200 OK)

**Events**:
- Emits: `api.request_received`, `api.response_sent`
- Listens: `chat.response_generated`

**Dependencies**: `chat_ai`, `memory_store`

## 🔄 Event-Flow für Chat-Interaktionen

```
1. HTTP POST /chat
   ↓
2. web_api.handle_chat_request()
   ↓ emit: api.request_received
3. memory_store.store_message(user_message)
   ↓ emit: memory.message_stored  
4. chat_ai.generate_response()
   ↓ emit: chat.response_generated
5. memory_store.store_message(ai_response)
   ↓ emit: memory.message_stored
6. web_api.send_response()
   ↓ emit: api.response_sent
```

## 📊 Dependency-Graph

```
web_api
├── depends: chat_ai
└── depends: memory_store

chat_ai  
└── depends: memory_store

memory_store
└── (no dependencies)
```

**Build Order**: memory_store → chat_ai → web_api

## 🔧 Service-Interfaces

### ChatAI Service Interface
```python
@service()
async def generate_response(
    self, 
    message: str, 
    conversation_id: str,
    model: str = "gpt-3.5-turbo"
) -> str:
    """Generate AI response for user message."""
```

### Memory Service Interface  
```python
@service()
async def store_message(
    self,
    conversation_id: str,
    role: str,  # "user" | "assistant" | "system"
    content: str
) -> None:
    """Store message in conversation history."""

@service()
async def get_conversation_history(
    self,
    conversation_id: str,
    limit: int = 50
) -> list[dict[str, Any]]:
    """Get conversation history with messages."""
```

### WebAPI Service Interface
```python
@service()
async def get_health_status(self) -> dict[str, str]:
    """Get API health status for /health endpoint."""
```

## 📁 Projekt-Struktur

```
examples/ai-chat-reference/
├── ARCHITECTURE.md          # Diese Datei
├── README.md               # <10 Min Setup-Guide  
├── pyproject.toml          # Dependencies
├── manifest.yaml           # App-Manifest
├── plugins/
│   ├── __init__.py
│   ├── chat_ai/
│   │   ├── __init__.py
│   │   ├── plugin.py       # ChatAIPlugin
│   │   └── manifest.yaml   # Plugin-Manifest
│   ├── memory_store/
│   │   ├── __init__.py  
│   │   ├── plugin.py       # MemoryStorePlugin
│   │   └── manifest.yaml   # Plugin-Manifest
│   └── web_api/
│       ├── __init__.py
│       ├── plugin.py       # WebAPIPlugin  
│       └── manifest.yaml   # Plugin-Manifest
├── app.py                  # Main App Builder
├── tests/
│   ├── __init__.py
│   ├── test_integration.py # E2E Tests
│   └── test_plugins.py     # Plugin Unit Tests
└── .env.example           # Environment Variables
```

## 🔍 Discovery Integration

**Requirement**: `plugginger inspect --json` funktioniert mit Reference-App

Die App wird so gebaut, dass:
1. Alle Plugins haben gültige `manifest.yaml` Dateien
2. App-Builder ist über `app:create_app` factory function erreichbar  
3. JSON-Output zeigt vollständige Plugin-Struktur mit Services/Events
4. Dependency-Graph ist korrekt exportiert

## ✅ Akzeptanzkriterien Mapping

- [x] **App Design**: Architektur definiert (diese Datei)
- [ ] **Plugin Development**: chat_ai, memory_store, web_api Plugins implementiert
- [ ] **Documentation**: Setup-Guide für <10 Minuten (README.md)
- [ ] **External Testing**: 2 externe Tester (Mensch + KI-Agent)
- [ ] **Health Check**: Reference-App startet und liefert 200 OK auf `/health`
- [ ] **Manifest Integration**: Alle Plugins haben gültige Manifeste
- [ ] **Discovery Integration**: `plugginger inspect --json` funktioniert

## 🚀 Nächste Schritte

1. **Plugin Implementation**: Implementiere die 3 Plugins nach diesem Design
2. **App Builder**: Erstelle `app.py` mit PluggingerAppBuilder Setup
3. **Manifeste**: Erstelle YAML-Manifeste für alle Plugins + App
4. **Tests**: E2E Tests für Chat-Flow
5. **Documentation**: README.md mit Setup-Guide
6. **CI Integration**: GitHub Actions für automatische Tests

## 📋 Technische Details

**Python Version**: 3.11+  
**Framework**: Plugginger (current version)  
**AI Provider**: OpenAI (mit fallback auf mock für Tests)  
**Web Framework**: FastAPI (lightweight, async-ready)  
**Storage**: In-Memory (für Einfachheit, erweiterbar auf Redis)  
**Testing**: pytest mit async support
