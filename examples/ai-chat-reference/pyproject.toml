[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-chat-reference"
version = "1.0.0"
description = "AI-Chat Reference App - Plugginger Framework Demonstration"
authors = [
    {name = "Plugginger AI-Agent", email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
keywords = ["plugginger", "ai", "chat", "reference", "demo"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Communications :: Chat",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "plugginger>=0.9.0",
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
ai = [
    "openai>=1.0.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
]
all = [
    "ai-chat-reference[ai,dev]",
]

[project.urls]
Homepage = "https://github.com/jkehrhahn/plugginger"
Repository = "https://github.com/jkehrhahn/plugginger"
Documentation = "https://github.com/jkehrhahn/plugginger/tree/main/examples/ai-chat-reference"
Issues = "https://github.com/jkehrhahn/plugginger/issues"

[project.scripts]
ai-chat-reference = "app:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["plugins*", "tests*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "e2e: End-to-end tests",
    "slow: Slow tests",
]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[[tool.mypy.overrides]]
module = [
    "fastapi.*",
    "uvicorn.*",
    "openai.*",
]
ignore_missing_imports = true

[tool.ruff]
target-version = "py311"
line-length = 100
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]

[tool.ruff.isort]
known-first-party = ["plugginger", "plugins"]

[tool.coverage.run]
source = ["plugins", "app"]
omit = [
    "tests/*",
    "*/test_*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
