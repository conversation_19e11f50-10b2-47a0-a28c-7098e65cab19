manifest_version: "1.0.0"
app_name: "ai_chat_reference"
app_version: "1.0.0"
description: "AI-Chat Reference App - Plugginger Framework Demonstration"

metadata:
  author: "Plugginger AI-Agent"
  license: "MIT"
  repository: "https://github.com/jkehrhahn/plugginger"
  documentation: "https://github.com/jkehrhahn/plugginger/tree/main/examples/ai-chat-reference"
  tags: ["reference", "ai", "chat", "demo", "plugginger"]

runtime:
  plugginger_version: ">=0.9.0"
  python_version: ">=3.11"
  entry_point: "app:create_app_for_inspection"

plugins:
  - "memory_store"
  - "chat_ai" 
  - "web_api"

plugin_configs:
  memory_store:
    max_conversations: 1000
    max_messages_per_conversation: 1000

  chat_ai:
    default_model: "gpt-3.5-turbo"
    max_tokens: 1000
    temperature: 0.7
    mock_mode: false

  web_api:
    server_host: "0.0.0.0"
    server_port: 8000
    cors_origins: ["*"]
    auto_start_server: false

global_config:
  max_fractal_depth: 5
  default_event_listener_timeout_seconds: 30.0

external_dependencies:
  - name: "fastapi"
    version: ">=0.100.0"
    description: "FastAPI web framework for HTTP endpoints"
  - name: "uvicorn"
    version: ">=0.20.0"
    description: "ASGI server for running FastAPI"
  - name: "pydantic"
    version: ">=2.0.0"
    description: "Data validation and serialization"

optional_dependencies:
  - name: "openai"
    version: ">=1.0.0"
    description: "OpenAI API client (optional, falls back to mock mode)"

development_dependencies:
  - name: "pytest"
    version: ">=7.0.0"
    description: "Testing framework"
  - name: "pytest-asyncio"
    version: ">=0.21.0"
    description: "Async testing support"
  - name: "httpx"
    version: ">=0.24.0"
    description: "HTTP client for testing API endpoints"

features:
  - name: "AI Chat Completion"
    description: "Generate AI responses using OpenAI or mock mode"
    plugins: ["chat_ai"]
    
  - name: "Conversation Memory"
    description: "Store and retrieve chat history and conversations"
    plugins: ["memory_store"]
    
  - name: "REST API"
    description: "HTTP endpoints for web interface integration"
    plugins: ["web_api"]
    endpoints:
      - "GET /health - Health check"
      - "POST /chat - Send message and get AI response"
      - "GET /conversations - List all conversations"
      - "GET /conversations/{id}/history - Get conversation history"
      - "DELETE /conversations/{id} - Delete conversation"
      - "GET /models - Get AI model information"

architecture:
  pattern: "Layered Plugin Architecture"
  layers:
    - name: "Storage Layer"
      plugins: ["memory_store"]
      description: "Data persistence and session management"
    - name: "AI Layer" 
      plugins: ["chat_ai"]
      description: "AI completion and response generation"
    - name: "API Layer"
      plugins: ["web_api"]
      description: "HTTP interface and request handling"

deployment:
  docker_support: true
  environment_variables:
    - name: "OPENAI_API_KEY"
      required: false
      description: "OpenAI API key (optional, uses mock mode if not provided)"
    - name: "SERVER_HOST"
      required: false
      default: "0.0.0.0"
      description: "Host for the web server"
    - name: "SERVER_PORT"
      required: false
      default: "8000"
      description: "Port for the web server"
    - name: "ENABLE_MANIFESTS"
      required: false
      default: "true"
      description: "Enable manifest loading and validation"

testing:
  test_coverage_target: 90
  test_types:
    - "unit"
    - "integration"
    - "e2e"
  test_commands:
    - "pytest tests/ -v"
    - "pytest tests/test_integration.py -v"

documentation:
  setup_time_target: "< 10 minutes"
  readme_sections:
    - "Quick Start"
    - "API Documentation"
    - "Plugin Architecture"
    - "Development Guide"
    - "Troubleshooting"

quality_gates:
  - "All tests pass (pytest)"
  - "Type checking passes (mypy --strict)"
  - "Linting passes (ruff check)"
  - "Health endpoint returns 200 OK"
  - "Manifest validation passes"
  - "Discovery command works (plugginger inspect --json)"
