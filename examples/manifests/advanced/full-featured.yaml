# Full-Featured Plugin Manifest Example
#
# This example demonstrates a comprehensive plugin manifest
# showcasing all available features and configuration options.
#
# Use case: Reference implementation, documentation, testing

manifest_version: "1.0.0"

metadata:
  name: "comprehensive_plugin"
  version: "4.2.1"
  description: "Comprehensive plugin demonstrating all manifest features and capabilities"
  author: "Plugginger Development Team"
  homepage: "https://plugginger.dev/examples/comprehensive"
  repository: "https://github.com/plugginger/comprehensive-plugin"
  license: "MIT"
  keywords:
    - "comprehensive"
    - "example"
    - "reference"
    - "full-featured"
    - "documentation"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "database_connector"
    version: ">=2.3.0"
    optional: false
    description: "Required for data persistence"
  
  - name: "notification_service"
    version: ">=1.5.0"
    optional: true
    description: "Optional notifications for events"
  
  - name: "cache_manager"
    version: ">=1.0.0"
    optional: true
    description: "Optional caching for performance"
  
  - name: "metrics_collector"
    version: null
    optional: true
    description: "Optional metrics collection (any version)"

services:
  - name: "create_resource"
    method_name: "create_resource"
    description: "Create a new resource with validation and persistence"
    timeout_seconds: 30.0
    signature: "async def create_resource(self, resource_type: str, data: dict[str, Any], options: dict[str, Any] | None = None) -> dict[str, Any]"
    parameters:
      - name: "resource_type"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "options"
        annotation: "dict[str, Any] | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "get_resource"
    method_name: "get_resource_by_id"
    description: "Retrieve resource by ID with caching support"
    timeout_seconds: 10.0
    signature: "async def get_resource_by_id(self, resource_id: str, include_metadata: bool = False) -> dict[str, Any] | None"
    parameters:
      - name: "resource_id"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "include_metadata"
        annotation: "bool"
        default: false
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any] | None"

  - name: "update_resource"
    method_name: "update_resource"
    description: "Update existing resource with validation"
    timeout_seconds: 25.0
    signature: "async def update_resource(self, resource_id: str, updates: dict[str, Any], **kwargs: Any) -> bool"
    parameters:
      - name: "resource_id"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "updates"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "kwargs"
        annotation: "Any"
        default: null
        kind: "VAR_KEYWORD"
    return_annotation: "bool"

  - name: "delete_resource"
    method_name: "delete_resource"
    description: "Delete resource with cascade options"
    timeout_seconds: 20.0
    signature: "async def delete_resource(self, resource_id: str, cascade: bool = False) -> bool"
    parameters:
      - name: "resource_id"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "cascade"
        annotation: "bool"
        default: false
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "bool"

  - name: "search_resources"
    method_name: "search_resources"
    description: "Search resources with filters and pagination"
    timeout_seconds: 15.0
    signature: "async def search_resources(self, query: str, filters: dict[str, Any] | None = None, limit: int = 50, offset: int = 0) -> dict[str, Any]"
    parameters:
      - name: "query"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "filters"
        annotation: "dict[str, Any] | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "limit"
        annotation: "int"
        default: 50
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "offset"
        annotation: "int"
        default: 0
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

event_listeners:
  - patterns:
      - "resource.created"
    method_name: "on_resource_created"
    description: "Handle resource creation events"
    timeout_seconds: 10.0
    priority: 80
    signature: "async def on_resource_created(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "resource.updated"
      - "resource.modified"
    method_name: "on_resource_changed"
    description: "Handle resource modification events"
    timeout_seconds: 8.0
    priority: 70
    signature: "async def on_resource_changed(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "resource.deleted"
    method_name: "on_resource_deleted"
    description: "Handle resource deletion events"
    timeout_seconds: 5.0
    priority: 90
    signature: "async def on_resource_deleted(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "system.*"
    method_name: "on_system_event"
    description: "Handle all system events with wildcard pattern"
    timeout_seconds: 3.0
    priority: 10
    signature: "async def on_system_event(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema:
  type: "object"
  title: "ComprehensivePluginConfig"
  description: "Complete configuration schema demonstrating all features"
  properties:
    general:
      type: "object"
      description: "General plugin settings"
      properties:
        enabled:
          type: "boolean"
          description: "Enable/disable plugin functionality"
          default: true
        debug_mode:
          type: "boolean"
          description: "Enable debug logging"
          default: false
        max_concurrent_operations:
          type: "integer"
          minimum: 1
          maximum: 1000
          description: "Maximum concurrent operations"
          default: 100
    
    database:
      type: "object"
      description: "Database configuration"
      properties:
        connection_string:
          type: "string"
          description: "Database connection string"
          format: "uri"
        pool_size:
          type: "integer"
          minimum: 1
          description: "Connection pool size"
          default: 10
        timeout:
          type: "number"
          minimum: 0
          description: "Query timeout in seconds"
          default: 30.0
        retry_attempts:
          type: "integer"
          minimum: 0
          description: "Number of retry attempts"
          default: 3
      required: ["connection_string"]
    
    caching:
      type: "object"
      description: "Caching configuration"
      properties:
        enabled:
          type: "boolean"
          description: "Enable caching"
          default: true
        backend:
          type: "string"
          enum: ["memory", "redis", "memcached"]
          description: "Cache backend"
          default: "memory"
        ttl:
          type: "integer"
          minimum: 0
          description: "Default TTL in seconds"
          default: 3600
        max_size:
          type: "string"
          description: "Maximum cache size"
          default: "100MB"
    
    notifications:
      type: "object"
      description: "Notification settings"
      properties:
        enabled:
          type: "boolean"
          description: "Enable notifications"
          default: false
        channels:
          type: "array"
          description: "Notification channels"
          items:
            type: "string"
            enum: ["email", "slack", "webhook"]
          default: ["email"]
        webhook_url:
          type: "string"
          description: "Webhook URL for notifications"
          format: "uri"
    
    validation:
      type: "object"
      description: "Data validation settings"
      properties:
        strict_mode:
          type: "boolean"
          description: "Enable strict validation"
          default: true
        custom_validators:
          type: "array"
          description: "Custom validation rules"
          items:
            type: "object"
            properties:
              field:
                type: "string"
              rule:
                type: "string"
              message:
                type: "string"
            required: ["field", "rule"]
          default: []
    
    performance:
      type: "object"
      description: "Performance tuning"
      properties:
        enable_profiling:
          type: "boolean"
          description: "Enable performance profiling"
          default: false
        batch_size:
          type: "integer"
          minimum: 1
          description: "Batch processing size"
          default: 100
        worker_threads:
          type: "integer"
          minimum: 1
          description: "Number of worker threads"
          default: 4
  
  required: ["general", "database"]

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
