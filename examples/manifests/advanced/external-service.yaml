# External Service Plugin Manifest Example
#
# This example demonstrates a plugin that runs as an external service
# with maximum isolation and network communication.
#
# Use case: Untrusted plugins, heavy computations, legacy integrations

manifest_version: "1.0.0"

metadata:
  name: "ml_inference_service"
  version: "1.4.0"
  description: "Machine learning inference service running as external process"
  author: "ML Team"
  homepage: "https://ml.company.com/inference-service"
  repository: "https://github.com/company/ml-inference-service"
  license: "Apache-2.0"
  keywords:
    - "machine-learning"
    - "inference"
    - "external"
    - "gpu"

runtime:
  execution_mode: "external"  # Maximum isolation
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "model_registry"
    version: ">=2.0.0"
    optional: false
    description: "Required for model loading and versioning"

services:
  - name: "predict"
    method_name: "run_inference"
    description: "Run ML inference on input data"
    timeout_seconds: 120.0  # Longer timeout for ML processing
    signature: "async def run_inference(self, model_name: str, input_data: dict[str, Any], options: dict[str, Any] | None = None) -> dict[str, Any]"
    parameters:
      - name: "model_name"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "input_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "options"
        annotation: "dict[str, Any] | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "batch_predict"
    method_name: "run_batch_inference"
    description: "Run batch inference on multiple inputs"
    timeout_seconds: 600.0  # 10 minutes for batch processing
    signature: "async def run_batch_inference(self, model_name: str, batch_data: list[dict[str, Any]], batch_size: int = 32) -> list[dict[str, Any]]"
    parameters:
      - name: "model_name"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "batch_data"
        annotation: "list[dict[str, Any]]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "batch_size"
        annotation: "int"
        default: 32
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "list[dict[str, Any]]"

  - name: "get_model_info"
    method_name: "get_model_metadata"
    description: "Get information about available models"
    timeout_seconds: 10.0
    signature: "async def get_model_metadata(self, model_name: str | None = None) -> dict[str, Any]"
    parameters:
      - name: "model_name"
        annotation: "str | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "health_check"
    method_name: "check_service_health"
    description: "Check service health and GPU availability"
    timeout_seconds: 5.0
    signature: "async def check_service_health(self) -> dict[str, Any]"
    parameters: []
    return_annotation: "dict[str, Any]"

event_listeners:
  - patterns:
      - "model.updated"
    method_name: "on_model_updated"
    description: "Handle model updates from registry"
    timeout_seconds: 300.0  # Model loading can take time
    priority: 100
    signature: "async def on_model_updated(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "system.shutdown"
    method_name: "on_shutdown"
    description: "Graceful shutdown with model cleanup"
    timeout_seconds: 60.0
    priority: 100
    signature: "async def on_shutdown(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema:
  type: "object"
  title: "MLInferenceServiceConfig"
  description: "Configuration for ML inference external service"
  properties:
    service:
      type: "object"
      description: "External service configuration"
      properties:
        host:
          type: "string"
          description: "Service host"
          default: "localhost"
        port:
          type: "integer"
          minimum: 1024
          maximum: 65535
          description: "Service port"
          default: 8080
        protocol:
          type: "string"
          enum: ["http", "https", "grpc"]
          description: "Communication protocol"
          default: "http"
        api_key:
          type: "string"
          description: "API key for authentication"
          format: "password"
        ssl_verify:
          type: "boolean"
          description: "Verify SSL certificates"
          default: true
        max_retries:
          type: "integer"
          minimum: 0
          description: "Maximum retry attempts"
          default: 3
        retry_delay:
          type: "number"
          minimum: 0
          description: "Delay between retries in seconds"
          default: 1.0
    
    models:
      type: "object"
      description: "Model configuration"
      properties:
        cache_dir:
          type: "string"
          description: "Directory for model caching"
          default: "/tmp/ml_models"
        max_cache_size:
          type: "string"
          description: "Maximum cache size (e.g., '10GB')"
          default: "5GB"
        preload_models:
          type: "array"
          description: "Models to preload on startup"
          items:
            type: "string"
          default: []
        default_model:
          type: "string"
          description: "Default model for inference"
          default: "general_classifier"
    
    hardware:
      type: "object"
      description: "Hardware configuration"
      properties:
        use_gpu:
          type: "boolean"
          description: "Use GPU acceleration"
          default: true
        gpu_memory_limit:
          type: "string"
          description: "GPU memory limit (e.g., '8GB')"
          default: "auto"
        cpu_threads:
          type: "integer"
          minimum: 1
          description: "Number of CPU threads"
          default: 4
        batch_size:
          type: "integer"
          minimum: 1
          description: "Default batch size"
          default: 32
    
    monitoring:
      type: "object"
      description: "Monitoring and logging"
      properties:
        enable_metrics:
          type: "boolean"
          description: "Enable performance metrics"
          default: true
        metrics_port:
          type: "integer"
          minimum: 1024
          maximum: 65535
          description: "Metrics endpoint port"
          default: 9090
        log_predictions:
          type: "boolean"
          description: "Log prediction requests/responses"
          default: false
        performance_tracking:
          type: "boolean"
          description: "Track inference performance"
          default: true
  
  required: ["service", "models"]

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
