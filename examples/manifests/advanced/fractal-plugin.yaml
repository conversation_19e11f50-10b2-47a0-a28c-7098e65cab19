# Fractal Plugin Manifest Example
#
# This example demonstrates an AppPlugin that contains its own
# internal sub-application with nested plugins.
#
# Use case: Complex feature modules, plugin ecosystems, modular architecture

manifest_version: "1.0.0"

metadata:
  name: "payment_processor"
  version: "2.0.0"
  description: "Payment processing AppPlugin with internal payment providers and fraud detection"
  author: "Payments Team"
  homepage: "https://docs.company.com/payment-processor"
  repository: "https://github.com/company/payment-processor"
  license: "Proprietary"
  keywords:
    - "payments"
    - "fractal"
    - "app-plugin"
    - "financial"

runtime:
  execution_mode: "process"  # Isolated for security
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "encryption_service"
    version: ">=3.0.0"
    optional: false
    description: "Required for PCI compliance"
  
  - name: "audit_logger"
    version: ">=2.0.0"
    optional: false
    description: "Required for financial audit trails"

services:
  - name: "process_payment"
    method_name: "process_payment"
    description: "Process payment through internal payment providers"
    timeout_seconds: 60.0
    signature: "async def process_payment(self, payment_data: dict[str, Any]) -> dict[str, Any]"
    parameters:
      - name: "payment_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "refund_payment"
    method_name: "refund_payment"
    description: "Process refund through appropriate provider"
    timeout_seconds: 45.0
    signature: "async def refund_payment(self, transaction_id: str, amount: float | None = None) -> dict[str, Any]"
    parameters:
      - name: "transaction_id"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "amount"
        annotation: "float | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "get_payment_status"
    method_name: "get_payment_status"
    description: "Get status of payment transaction"
    timeout_seconds: 10.0
    signature: "async def get_payment_status(self, transaction_id: str) -> dict[str, Any]"
    parameters:
      - name: "transaction_id"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

event_listeners:
  - patterns:
      - "payment.requested"
    method_name: "on_payment_requested"
    description: "Handle payment requests from external systems"
    timeout_seconds: 30.0
    priority: 100
    signature: "async def on_payment_requested(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "fraud.detected"
    method_name: "on_fraud_detected"
    description: "Handle fraud detection alerts from internal fraud detector"
    timeout_seconds: 5.0
    priority: 100
    signature: "async def on_fraud_detected(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema:
  type: "object"
  title: "PaymentProcessorConfig"
  description: "Configuration for payment processor AppPlugin"
  properties:
    internal_app:
      type: "object"
      description: "Configuration for internal payment app"
      properties:
        plugins:
          type: "array"
          description: "Internal plugins to load"
          items:
            type: "string"
          default: ["stripe_provider", "paypal_provider", "fraud_detector", "transaction_logger"]
        
        default_provider:
          type: "string"
          enum: ["stripe", "paypal", "bank_transfer"]
          description: "Default payment provider"
          default: "stripe"
        
        fraud_detection:
          type: "object"
          properties:
            enabled:
              type: "boolean"
              default: true
            risk_threshold:
              type: "number"
              minimum: 0
              maximum: 1
              default: 0.8
            ml_model_path:
              type: "string"
              default: "/models/fraud_detection.pkl"
        
        providers:
          type: "object"
          properties:
            stripe:
              type: "object"
              properties:
                api_key:
                  type: "string"
                  format: "password"
                webhook_secret:
                  type: "string"
                  format: "password"
                currency:
                  type: "string"
                  default: "usd"
            
            paypal:
              type: "object"
              properties:
                client_id:
                  type: "string"
                client_secret:
                  type: "string"
                  format: "password"
                environment:
                  type: "string"
                  enum: ["sandbox", "production"]
                  default: "sandbox"
    
    compliance:
      type: "object"
      properties:
        pci_compliance:
          type: "boolean"
          default: true
        data_retention_days:
          type: "integer"
          minimum: 1
          default: 2555  # 7 years
        encryption_level:
          type: "string"
          enum: ["aes256", "rsa2048", "rsa4096"]
          default: "aes256"
  
  required: ["internal_app"]

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"

# Note: Internal application configuration is handled through the config_schema
# above. The actual internal app manifest would be generated separately when
# the AppPlugin is instantiated.
