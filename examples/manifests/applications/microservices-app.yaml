# Microservices Application Manifest Example
#
# This example demonstrates a complex application manifest
# with multiple plugins forming a microservices architecture.
#
# Use case: Enterprise applications, complex systems, distributed services

manifest_version: "1.0.0"

app_name: "enterprise_platform"
app_version: "3.2.1"
description: "Enterprise platform with user management, notifications, and data processing"

plugins:
  - "user_manager"
  - "notification_service"
  - "database_connector"
  - "audit_logger"
  - "api_gateway"
  - "auth_service"
  - "file_processor"
  - "metrics_collector"
  - "cache_manager"

plugin_configs:
  user_manager:
    password_policy:
      min_length: 12
      require_uppercase: true
      require_lowercase: true
      require_numbers: true
      require_symbols: true
    session_timeout: 3600
    max_failed_attempts: 5
    lockout_duration: 900
  
  notification_service:
    default_channels: ["email"]
    rate_limits:
      email: 100  # per hour
      sms: 10     # per hour
      push: 1000  # per hour
    templates_path: "/etc/notifications/templates"
  
  database_connector:
    database_type: "postgresql"
    connection:
      host: "db.internal.company.com"
      port: 5432
      database: "enterprise_platform"
      username: "app_user"
      password: "${DB_PASSWORD}"  # Environment variable
      ssl_mode: "verify-full"
    pool_settings:
      min_connections: 10
      max_connections: 50
      connection_timeout: 30.0
      idle_timeout: 600.0
    query_settings:
      default_timeout: 60.0
      retry_attempts: 3
      enable_logging: true
  
  audit_logger:
    log_level: "INFO"
    destinations:
      - type: "file"
        path: "/var/log/enterprise/audit.log"
      - type: "syslog"
        facility: "local0"
      - type: "elasticsearch"
        url: "https://logs.company.com"
    retention_days: 2555  # 7 years
  
  api_gateway:
    rate_limiting:
      default: 1000  # requests per minute
      authenticated: 5000
      premium: 10000
    cors:
      allowed_origins: ["https://app.company.com", "https://admin.company.com"]
      allowed_methods: ["GET", "POST", "PUT", "DELETE"]
      allowed_headers: ["Authorization", "Content-Type"]
    timeout: 30.0
  
  auth_service:
    jwt:
      secret_key: "${JWT_SECRET}"
      algorithm: "HS256"
      expiration: 3600
    oauth:
      providers: ["google", "microsoft", "github"]
      redirect_uri: "https://app.company.com/auth/callback"
    mfa:
      enabled: true
      methods: ["totp", "sms"]
  
  file_processor:
    storage:
      type: "s3"
      bucket: "enterprise-files"
      region: "us-east-1"
    processing:
      max_file_size: "100MB"
      allowed_types: ["pdf", "docx", "xlsx", "txt", "csv"]
      virus_scanning: true
    thumbnails:
      enabled: true
      sizes: [150, 300, 600]
  
  metrics_collector:
    collection_interval: 60  # seconds
    exporters:
      - type: "prometheus"
        port: 9090
      - type: "datadog"
        api_key: "${DATADOG_API_KEY}"
    custom_metrics:
      - "user_registrations_total"
      - "notification_delivery_duration"
      - "database_query_duration"
  
  cache_manager:
    backend: "redis"
    connection:
      host: "redis.internal.company.com"
      port: 6379
      db: 0
      password: "${REDIS_PASSWORD}"
    settings:
      default_ttl: 3600
      max_memory: "2GB"
      eviction_policy: "allkeys-lru"

global_config:
  app_name: "Enterprise Platform"
  version: "3.2.1"
  environment: "production"
  debug: false
  
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    structured: true
    correlation_id: true
  
  monitoring:
    health_check_interval: 30
    metrics_enabled: true
    tracing_enabled: true
    profiling_enabled: false
  
  security:
    encryption_at_rest: true
    encryption_in_transit: true
    security_headers: true
    content_security_policy: "default-src 'self'"
  
  performance:
    max_concurrent_requests: 10000
    request_timeout: 60.0
    connection_pool_size: 100
    enable_compression: true
  
  compliance:
    gdpr_enabled: true
    data_retention_days: 2555
    audit_all_requests: true
    pii_encryption: true

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-app-manifest-generator"
