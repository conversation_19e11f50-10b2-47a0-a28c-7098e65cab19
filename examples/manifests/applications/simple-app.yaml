# Simple Application Manifest Example
#
# This example demonstrates a basic application manifest
# with a few plugins and simple configuration.
#
# Use case: Small applications, prototypes, simple services

manifest_version: "1.0.0"

app_name: "simple_calculator_app"
app_version: "1.0.0"
description: "A simple calculator application with basic math operations and logging"

plugins:
  - "calculator_service"
  - "audit_logger"

plugin_configs:
  calculator_service:
    precision: 6
    enable_history: true
    max_history_size: 100
  
  audit_logger:
    log_level: "INFO"
    log_file: "/var/log/calculator/audit.log"
    max_file_size: "10MB"
    backup_count: 5

global_config:
  app_name: "Simple Calculator"
  version: "1.0.0"
  environment: "production"
  debug: false
  
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    handlers:
      - type: "file"
        filename: "/var/log/calculator/app.log"
      - type: "console"
        level: "WARNING"
  
  performance:
    max_concurrent_requests: 100
    request_timeout: 30.0
    enable_metrics: true
  
  security:
    enable_rate_limiting: true
    max_requests_per_minute: 60
    require_authentication: false

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-app-manifest-generator"
