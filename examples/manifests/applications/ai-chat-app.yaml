# AI Chat Application Manifest Example
#
# This example demonstrates the reference AI chat application
# mentioned in the roadmap with AI, memory, and web API plugins.
#
# Use case: AI applications, chatbots, conversational interfaces

manifest_version: "1.0.0"

app_name: "ai_chat_reference"
app_version: "1.0.0"
description: "Reference AI chat application with memory and web API"

plugins:
  - "chat_ai"
  - "memory_store"
  - "web_api"
  - "conversation_manager"
  - "user_session"

plugin_configs:
  chat_ai:
    provider: "openai"
    model: "gpt-4"
    api_key: "${OPENAI_API_KEY}"
    max_tokens: 2048
    temperature: 0.7
    timeout: 30.0
    system_prompt: |
      You are a helpful AI assistant. Be concise but informative.
      Use the conversation history to provide contextual responses.
    safety:
      content_filter: true
      max_requests_per_minute: 60
  
  memory_store:
    backend: "vector"
    vector_db:
      type: "chroma"
      persist_directory: "/data/memory"
      collection_name: "chat_memory"
    embedding:
      model: "text-embedding-ada-002"
      api_key: "${OPENAI_API_KEY}"
    retrieval:
      max_results: 5
      similarity_threshold: 0.7
    retention:
      max_memories: 10000
      cleanup_interval: 86400  # 24 hours
  
  web_api:
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
    cors:
      allowed_origins: ["http://localhost:3000", "https://chat.company.com"]
      allowed_methods: ["GET", "POST", "OPTIONS"]
    rate_limiting:
      requests_per_minute: 100
      burst_size: 20
    websocket:
      enabled: true
      max_connections: 1000
      heartbeat_interval: 30
  
  conversation_manager:
    max_history_length: 50
    context_window: 4000  # tokens
    auto_summarize: true
    summarize_threshold: 30  # messages
    session_timeout: 3600  # seconds
  
  user_session:
    storage: "redis"
    connection:
      host: "localhost"
      port: 6379
      db: 1
    session:
      timeout: 7200  # 2 hours
      cleanup_interval: 300  # 5 minutes
    tracking:
      track_usage: true
      track_preferences: true

global_config:
  app_name: "AI Chat Reference"
  version: "1.0.0"
  environment: "development"
  debug: true
  
  logging:
    level: "DEBUG"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_chat_messages: false  # Privacy
    log_api_requests: true
  
  ai:
    default_model: "gpt-4"
    fallback_model: "gpt-3.5-turbo"
    max_conversation_length: 50
    enable_streaming: true
    content_moderation: true
  
  api:
    base_url: "http://localhost:8000"
    api_version: "v1"
    documentation_url: "/docs"
    enable_swagger: true
  
  features:
    enable_memory: true
    enable_websockets: true
    enable_file_upload: false
    enable_voice_input: false
    enable_image_analysis: false
  
  limits:
    max_message_length: 4000
    max_file_size: "10MB"
    max_concurrent_chats: 100
    rate_limit_per_user: 60  # per minute
  
  ui:
    theme: "light"
    language: "en"
    show_typing_indicator: true
    show_message_timestamps: true
    enable_markdown: true

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-app-manifest-generator"
