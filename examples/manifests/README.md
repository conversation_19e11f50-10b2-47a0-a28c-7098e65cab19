# Plugin Manifest Examples

This directory contains example plugin manifests demonstrating different plugin patterns and configurations. These examples help developers understand the manifest schema and provide templates for common use cases.

## 📁 Directory Structure

```
examples/manifests/
├── README.md                    # This file
├── plugin-types/               # Examples by plugin type
│   ├── service-only.yaml       # Simple service plugin
│   ├── event-listener.yaml     # Event processing plugin
│   ├── mixed-plugin.yaml       # Service + event plugin
│   ├── with-dependencies.yaml  # Plugin with dependencies
│   └── with-config.yaml        # Plugin with configuration schema
├── applications/               # Application manifest examples
│   ├── simple-app.yaml        # Basic application
│   ├── microservices-app.yaml # Multi-plugin application
│   └── ai-chat-app.yaml       # AI chat reference app
└── advanced/                  # Advanced examples
    ├── fractal-plugin.yaml    # AppPlugin (nested apps)
    ├── external-service.yaml  # External execution mode
    └── full-featured.yaml     # All features combined
```

## 🎯 Usage

### For Developers
Use these examples as templates when creating your own plugin manifests:

```bash
# Copy a template
cp examples/manifests/plugin-types/service-only.yaml my-plugin-manifest.yaml

# Edit for your plugin
vim my-plugin-manifest.yaml
```

### For AI Agents
These manifests demonstrate the expected structure and patterns for automatic plugin generation and integration.

### Validation
All examples validate against the Plugginger manifest schema:

```python
from plugginger.schemas import manifest_from_yaml, PluginManifest

# Load and validate
with open("examples/manifests/plugin-types/service-only.yaml") as f:
    manifest = manifest_from_yaml(f.read(), PluginManifest)
```

## 📚 Example Categories

### 1. Plugin Types
- **service-only.yaml**: Plugin that only provides services
- **event-listener.yaml**: Plugin that only handles events  
- **mixed-plugin.yaml**: Plugin with both services and event listeners
- **with-dependencies.yaml**: Plugin that depends on other plugins
- **with-config.yaml**: Plugin with configuration schema

### 2. Applications
- **simple-app.yaml**: Basic application with 2-3 plugins
- **microservices-app.yaml**: Complex application with many plugins
- **ai-chat-app.yaml**: Reference AI chat application

### 3. Advanced
- **fractal-plugin.yaml**: AppPlugin with internal sub-applications
- **external-service.yaml**: Plugin running as external service
- **full-featured.yaml**: Comprehensive example with all features

## 🔗 Related Documentation

- [Plugin Manifest Schema](../../src/plugginger/schemas/manifest.py) - Complete schema definition
- [Manifest Generator](../../src/plugginger/schemas/generator.py) - Automatic generation
- [ROADMAP.md](../../ROADMAP.md) - Development roadmap
- [Issue #3](https://github.com/jkehrhahn/plugginger/issues/3) - Original requirements

---

**Generated**: 2025-06-01  
**Schema Version**: 1.0.0  
**Plugginger Version**: >=0.9.0
