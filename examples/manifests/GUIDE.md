# Plugin Manifest Guide

This guide explains how to use the plugin manifest examples and understand the manifest schema structure.

## 🎯 Quick Start

### 1. Choose Your Template
Pick the example that best matches your plugin type:

- **Simple service**: `plugin-types/service-only.yaml`
- **Event processing**: `plugin-types/event-listener.yaml`
- **Full-featured plugin**: `plugin-types/mixed-plugin.yaml`
- **Plugin with dependencies**: `plugin-types/with-dependencies.yaml`
- **Configurable plugin**: `plugin-types/with-config.yaml`

### 2. Copy and Customize
```bash
cp examples/manifests/plugin-types/service-only.yaml my-plugin-manifest.yaml
```

### 3. Validate Your Manifest
```python
from plugginger.schemas import manifest_from_yaml, PluginManifest

with open("my-plugin-manifest.yaml") as f:
    manifest = manifest_from_yaml(f.read(), PluginManifest)
    print("✅ Manifest is valid!")
```

## 📋 Manifest Structure

### Core Sections

#### 1. Metadata
```yaml
metadata:
  name: "my_plugin"           # Must be valid Python identifier
  version: "1.0.0"            # PEP 440 compatible
  description: "My plugin"    # Optional description
  author: "Your Name"         # Optional
  homepage: "https://..."     # Optional
  repository: "https://..."   # Optional
  license: "MIT"              # Optional
  keywords: ["tag1", "tag2"]  # Optional
```

#### 2. Runtime Configuration
```yaml
runtime:
  execution_mode: "thread"    # thread|process|external
  python_version: ">=3.11"   # Optional version requirement
  plugginger_version: ">=0.9.0"  # Required framework version
```

#### 3. Dependencies
```yaml
dependencies:
  - name: "other_plugin"
    version: ">=1.0.0"        # Optional version constraint
    optional: false           # Required by default
    description: "Why needed" # Optional explanation
```

#### 4. Services
```yaml
services:
  - name: "service_name"      # Service identifier
    method_name: "method"     # Python method name
    description: "What it does"
    timeout_seconds: 30.0     # Optional timeout
    signature: "async def method(self, param: str) -> str"
    parameters:               # Method parameters
      - name: "param"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "str"  # Return type
```

#### 5. Event Listeners
```yaml
event_listeners:
  - patterns: ["event.pattern"]  # Event patterns to match
    method_name: "handler"       # Python method name
    description: "What it handles"
    timeout_seconds: 10.0        # Optional timeout
    priority: 100                # Execution priority (higher = earlier)
    signature: "async def handler(self, data: dict) -> None"
    parameters:                  # Method parameters
      - name: "data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
```

#### 6. Configuration Schema
```yaml
config_schema:
  type: "object"
  title: "MyPluginConfig"
  description: "Configuration for my plugin"
  properties:
    setting1:
      type: "string"
      description: "A string setting"
      default: "default_value"
    setting2:
      type: "integer"
      minimum: 1
      maximum: 100
      default: 10
  required: ["setting1"]
```

## 🔧 Common Patterns

### Service-Only Plugin
Best for: Utility functions, data processors, API wrappers
```yaml
services: [...]      # Define your services
event_listeners: []  # Empty - no event handling
dependencies: []     # Usually no dependencies
```

### Event-Only Plugin
Best for: Logging, monitoring, notifications
```yaml
services: []         # Empty - no services
event_listeners: [...] # Define your event handlers
dependencies: []     # Usually no dependencies
```

### Mixed Plugin
Best for: Feature plugins, business logic
```yaml
services: [...]      # Provide services
event_listeners: [...] # Handle events
dependencies: [...]  # May depend on other plugins
```

### Plugin with Dependencies
Best for: Composite features, integrations
```yaml
dependencies:
  - name: "required_plugin"
    optional: false
  - name: "optional_plugin"
    optional: true
```

### Configurable Plugin
Best for: Flexible, reusable plugins
```yaml
config_schema:
  type: "object"
  properties:
    # Define configuration options
```

## 🏗️ Application Manifests

Application manifests describe complete applications with multiple plugins:

```yaml
manifest_version: "1.0.0"
app_name: "my_application"
app_version: "1.0.0"
description: "My application"

plugins:
  - "plugin1"
  - "plugin2"

plugin_configs:
  plugin1:
    setting: "value"
  plugin2:
    other_setting: 42

global_config:
  app_setting: "global_value"
```

## 🚀 Advanced Features

### Execution Modes
- **thread**: Same process, fastest (default)
- **process**: Separate process, more isolation
- **external**: External service, maximum isolation

### Event Patterns
- Exact match: `"user.login"`
- Wildcard: `"user.*"` (matches `user.login`, `user.logout`, etc.)
- Multiple patterns: `["user.login", "user.logout"]`

### Parameter Kinds
- `POSITIONAL_ONLY`: Can only be passed positionally
- `POSITIONAL_OR_KEYWORD`: Can be passed either way
- `VAR_POSITIONAL`: `*args`
- `KEYWORD_ONLY`: Must be passed as keyword
- `VAR_KEYWORD`: `**kwargs`

### Configuration Schema Types
- `string`, `integer`, `number`, `boolean`
- `array`, `object`
- `enum` for choices
- `format` for validation (e.g., `"uri"`, `"email"`, `"password"`)

## 🔍 Validation

### Schema Validation
All manifests are validated against the Pydantic schema:
```python
from plugginger.schemas import PluginManifest, manifest_from_yaml

manifest = manifest_from_yaml(yaml_content, PluginManifest)
```

### Runtime Validation
Enable manifest validation during plugin loading:
```python
from plugginger.api.builder import PluggingerAppBuilder

builder = PluggingerAppBuilder("my_app")
builder.enable_manifest_validation()  # Validates manifests against code
```

## 📚 Examples by Use Case

| Use Case | Example File | Description |
|----------|-------------|-------------|
| Simple utility | `service-only.yaml` | Calculator service |
| Event processing | `event-listener.yaml` | Audit logger |
| Feature plugin | `mixed-plugin.yaml` | User management |
| Integration | `with-dependencies.yaml` | Notification service |
| Configurable | `with-config.yaml` | Database connector |
| Small app | `simple-app.yaml` | Calculator app |
| Enterprise | `microservices-app.yaml` | Complex platform |
| AI application | `ai-chat-app.yaml` | AI chat reference |
| Nested plugins | `fractal-plugin.yaml` | Payment processor |
| External service | `external-service.yaml` | ML inference |
| Complete example | `full-featured.yaml` | All features |

## 🛠️ Tools and Utilities

### Generate from Code
```python
from plugginger.schemas import generate_plugin_manifest

manifest = generate_plugin_manifest(MyPluginClass)
```

### Convert to YAML
```python
from plugginger.schemas import manifest_to_yaml

yaml_content = manifest_to_yaml(manifest)
```

### Load from YAML
```python
from plugginger.schemas import manifest_from_yaml

manifest = manifest_from_yaml(yaml_content, PluginManifest)
```

## 🔗 Related Resources

- [Plugin Manifest Schema](../../src/plugginger/schemas/manifest.py)
- [Manifest Generator](../../src/plugginger/schemas/generator.py)
- [Manifest Validation](../../src/plugginger/_internal/validation/manifest_validation.py)
- [ROADMAP.md](../../ROADMAP.md)
- [Issue #3](https://github.com/jkehrhahn/plugginger/issues/3)

---

**Need help?** Check the examples or create an issue on GitHub!
