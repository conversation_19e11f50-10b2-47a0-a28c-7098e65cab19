# Plugin with Configuration Schema Manifest Example
#
# This example demonstrates a plugin with a detailed configuration schema
# using JSON Schema format for validation.
#
# Use case: Configurable plugins, API integrations, customizable behavior

manifest_version: "1.0.0"

metadata:
  name: "database_connector"
  version: "2.3.0"
  description: "Configurable database connector supporting multiple database types"
  author: "Data Team"
  homepage: "https://docs.company.com/database-connector"
  repository: "https://github.com/company/database-connector"
  license: "Apache-2.0"
  keywords:
    - "database"
    - "connector"
    - "sql"
    - "configurable"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "connection_pool"
    version: ">=1.0.0"
    optional: false
    description: "Required for database connection pooling"

services:
  - name: "execute_query"
    method_name: "execute_query"
    description: "Execute SQL query with parameters"
    timeout_seconds: 60.0
    signature: "async def execute_query(self, query: str, params: dict[str, Any] | None = None) -> list[dict[str, Any]]"
    parameters:
      - name: "query"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "params"
        annotation: "dict[str, Any] | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "list[dict[str, Any]]"

  - name: "health_check"
    method_name: "check_connection"
    description: "Check database connection health"
    timeout_seconds: 10.0
    signature: "async def check_connection(self) -> bool"
    parameters: []
    return_annotation: "bool"

event_listeners:
  - patterns:
      - "database.connection.lost"
    method_name: "handle_connection_lost"
    description: "Handle database connection loss"
    timeout_seconds: 5.0
    priority: 100
    signature: "async def handle_connection_lost(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema:
  type: "object"
  title: "DatabaseConnectorConfig"
  description: "Configuration schema for database connector plugin"
  properties:
    database_type:
      type: "string"
      enum: ["postgresql", "mysql", "sqlite", "oracle"]
      description: "Type of database to connect to"
      default: "postgresql"
    
    connection:
      type: "object"
      description: "Database connection settings"
      properties:
        host:
          type: "string"
          description: "Database host"
          default: "localhost"
        port:
          type: "integer"
          minimum: 1
          maximum: 65535
          description: "Database port"
          default: 5432
        database:
          type: "string"
          description: "Database name"
        username:
          type: "string"
          description: "Database username"
        password:
          type: "string"
          description: "Database password"
          format: "password"
        ssl_mode:
          type: "string"
          enum: ["disable", "require", "verify-ca", "verify-full"]
          description: "SSL connection mode"
          default: "require"
      required: ["database", "username", "password"]
    
    pool_settings:
      type: "object"
      description: "Connection pool configuration"
      properties:
        min_connections:
          type: "integer"
          minimum: 1
          description: "Minimum number of connections in pool"
          default: 5
        max_connections:
          type: "integer"
          minimum: 1
          description: "Maximum number of connections in pool"
          default: 20
        connection_timeout:
          type: "number"
          minimum: 0
          description: "Connection timeout in seconds"
          default: 30.0
        idle_timeout:
          type: "number"
          minimum: 0
          description: "Idle connection timeout in seconds"
          default: 300.0
    
    query_settings:
      type: "object"
      description: "Query execution settings"
      properties:
        default_timeout:
          type: "number"
          minimum: 0
          description: "Default query timeout in seconds"
          default: 30.0
        retry_attempts:
          type: "integer"
          minimum: 0
          description: "Number of retry attempts for failed queries"
          default: 3
        enable_logging:
          type: "boolean"
          description: "Enable query logging"
          default: false
  
  required: ["database_type", "connection"]

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
