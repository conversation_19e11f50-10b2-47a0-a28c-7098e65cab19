# Mixed Plugin Manifest Example
#
# This example demonstrates a plugin that provides both services
# and event listeners - the most common plugin pattern.
#
# Use case: Feature plugins, business logic, API integrations

manifest_version: "1.0.0"

metadata:
  name: "user_manager"
  version: "3.0.1"
  description: "User management plugin with services and event handling"
  author: "Backend Team"
  homepage: "https://docs.company.com/user-manager"
  repository: "https://github.com/company/user-manager-plugin"
  license: "Apache-2.0"
  keywords:
    - "users"
    - "authentication"
    - "management"
    - "api"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies: []

services:
  - name: "create_user"
    method_name: "create_user"
    description: "Create a new user account"
    timeout_seconds: 10.0
    signature: "async def create_user(self, username: str, email: str, password: str) -> dict[str, Any]"
    parameters:
      - name: "username"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "email"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "password"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

  - name: "get_user"
    method_name: "get_user_by_id"
    description: "Retrieve user information by ID"
    timeout_seconds: 5.0
    signature: "async def get_user_by_id(self, user_id: int) -> dict[str, Any] | None"
    parameters:
      - name: "user_id"
        annotation: "int"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any] | None"

  - name: "update_user"
    method_name: "update_user"
    description: "Update user information"
    timeout_seconds: 8.0
    signature: "async def update_user(self, user_id: int, **updates: Any) -> bool"
    parameters:
      - name: "user_id"
        annotation: "int"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "updates"
        annotation: "Any"
        default: null
        kind: "VAR_KEYWORD"
    return_annotation: "bool"

event_listeners:
  - patterns:
      - "user.created"
    method_name: "on_user_created"
    description: "Handle user creation events for welcome email"
    timeout_seconds: 15.0
    priority: 80
    signature: "async def on_user_created(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "user.login.failed"
    method_name: "on_failed_login"
    description: "Handle failed login attempts for security"
    timeout_seconds: 5.0
    priority: 95
    signature: "async def on_failed_login(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "system.shutdown"
    method_name: "on_system_shutdown"
    description: "Clean up user sessions on system shutdown"
    timeout_seconds: 30.0
    priority: 100
    signature: "async def on_system_shutdown(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema: null

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
