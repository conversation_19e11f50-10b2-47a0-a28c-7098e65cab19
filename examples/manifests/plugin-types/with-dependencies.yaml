# Plugin with Dependencies Manifest Example
#
# This example demonstrates a plugin that depends on other plugins
# and shows different dependency patterns.
#
# Use case: Composite plugins, feature extensions, integrations

manifest_version: "1.0.0"

metadata:
  name: "notification_service"
  version: "1.5.2"
  description: "Notification service that depends on user management and email plugins"
  author: "Integration Team"
  homepage: "https://docs.company.com/notifications"
  repository: "https://github.com/company/notification-service"
  license: "MIT"
  keywords:
    - "notifications"
    - "email"
    - "users"
    - "integration"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies:
  - name: "user_manager"
    version: ">=3.0.0"
    optional: false
    description: "Required for user lookup and validation"
  
  - name: "email_service"
    version: ">=2.1.0"
    optional: false
    description: "Required for sending email notifications"
  
  - name: "sms_service"
    version: ">=1.0.0"
    optional: true
    description: "Optional SMS notifications"
  
  - name: "push_service"
    version: null
    optional: true
    description: "Optional push notifications (any version)"

services:
  - name: "send_notification"
    method_name: "send_notification"
    description: "Send notification via multiple channels"
    timeout_seconds: 30.0
    signature: "async def send_notification(self, user_id: int, message: str, channels: list[str] | None = None) -> dict[str, bool]"
    parameters:
      - name: "user_id"
        annotation: "int"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "message"
        annotation: "str"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "channels"
        annotation: "list[str] | None"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, bool]"

  - name: "get_user_preferences"
    method_name: "get_notification_preferences"
    description: "Get user notification preferences"
    timeout_seconds: 5.0
    signature: "async def get_notification_preferences(self, user_id: int) -> dict[str, Any]"
    parameters:
      - name: "user_id"
        annotation: "int"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "dict[str, Any]"

event_listeners:
  - patterns:
      - "user.created"
    method_name: "send_welcome_notification"
    description: "Send welcome notification to new users"
    timeout_seconds: 20.0
    priority: 70
    signature: "async def send_welcome_notification(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "system.alert"
      - "system.warning"
    method_name: "send_admin_alert"
    description: "Send alerts to administrators"
    timeout_seconds: 10.0
    priority: 95
    signature: "async def send_admin_alert(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema: null

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
