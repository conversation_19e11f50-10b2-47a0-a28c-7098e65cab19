# Event-Listener Plugin Manifest Example
#
# This example demonstrates a plugin that only handles events
# without providing services.
#
# Use case: Logging plugins, monitoring, notification systems

manifest_version: "1.0.0"

metadata:
  name: "audit_logger"
  version: "2.1.0"
  description: "Audit logging plugin that captures and logs system events"
  author: "Security Team"
  homepage: "https://internal.company.com/audit-logger"
  repository: "https://github.com/company/audit-logger"
  license: "Proprietary"
  keywords:
    - "logging"
    - "audit"
    - "security"
    - "monitoring"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies: []

services: []

event_listeners:
  - patterns:
      - "user.login"
      - "user.logout"
    method_name: "log_user_activity"
    description: "Log user authentication events"
    timeout_seconds: 5.0
    priority: 100
    signature: "async def log_user_activity(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "system.error"
      - "system.warning"
    method_name: "log_system_events"
    description: "Log system-level events and errors"
    timeout_seconds: 3.0
    priority: 90
    signature: "async def log_system_events(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "data.*"
    method_name: "log_data_operations"
    description: "Log all data-related operations using wildcard pattern"
    timeout_seconds: 2.0
    priority: 50
    signature: "async def log_data_operations(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

  - patterns:
      - "plugin.loaded"
      - "plugin.unloaded"
    method_name: "log_plugin_lifecycle"
    description: "Log plugin lifecycle events"
    timeout_seconds: 1.0
    priority: 10
    signature: "async def log_plugin_lifecycle(self, event_data: dict[str, Any]) -> None"
    parameters:
      - name: "event_data"
        annotation: "dict[str, Any]"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"

config_schema: null

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
