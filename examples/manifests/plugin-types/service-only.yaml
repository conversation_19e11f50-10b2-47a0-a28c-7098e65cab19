# Service-Only Plugin Manifest Example
# 
# This example demonstrates a plugin that only provides services
# without event listeners or complex dependencies.
#
# Use case: Utility plugins, data processors, API wrappers

manifest_version: "1.0.0"

metadata:
  name: "calculator_service"
  version: "1.2.0"
  description: "A simple calculator service plugin providing basic mathematical operations"
  author: "Plugginger Team"
  homepage: "https://github.com/plugginger/examples"
  repository: "https://github.com/plugginger/examples/calculator-service"
  license: "MIT"
  keywords:
    - "calculator"
    - "math"
    - "utility"
    - "service"

runtime:
  execution_mode: "thread"
  python_version: ">=3.11"
  plugginger_version: ">=0.9.0"

dependencies: []

services:
  - name: "add"
    method_name: "add_numbers"
    description: "Add two numbers together"
    timeout_seconds: 1.0
    signature: "async def add_numbers(self, a: float, b: float) -> float"
    parameters:
      - name: "a"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "b"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "float"

  - name: "multiply"
    method_name: "multiply_numbers"
    description: "Multiply two numbers"
    timeout_seconds: 1.0
    signature: "async def multiply_numbers(self, a: float, b: float) -> float"
    parameters:
      - name: "a"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "b"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "float"

  - name: "divide"
    method_name: "divide_numbers"
    description: "Divide two numbers with error handling"
    timeout_seconds: 2.0
    signature: "async def divide_numbers(self, a: float, b: float) -> float"
    parameters:
      - name: "a"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
      - name: "b"
        annotation: "float"
        default: null
        kind: "POSITIONAL_OR_KEYWORD"
    return_annotation: "float"

event_listeners: []

config_schema: null

generated_at: "2025-06-01T15:30:00Z"
generated_by: "plugginger-manifest-generator"
