name: Simple Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Install dependencies
      run: |
        poetry install --no-interaction --with dev

    - name: Run tests
      run: |
        poetry run pytest tests/ -v

    - name: Check if tests pass
      run: echo "Tests completed successfully!"
